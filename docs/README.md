# 📚 项目文档目录

本目录包含项目的各类文档，按功能分类组织。

## 📁 目录结构

```
docs/
├── README.md                   # 本文件
├── development/                # 开发相关文档
│   ├── CLAUDE.md              # AI开发指南
│   └── git提交规范规则.md      # Git提交规范
├── deployment/                 # 部署相关文档
├── api/                       # API文档
└── user-guide/                # 用户指南
```

## 📋 文档说明

### 🔧 development/ - 开发文档
- **CLAUDE.md**: AI开发助手使用指南和最佳实践
- **git提交规范规则.md**: 项目Git提交信息的规范和要求

### 🚀 deployment/ - 部署文档
- 部署相关的文档将放置在此目录

### 📡 api/ - API文档
- API接口文档将放置在此目录

### 👥 user-guide/ - 用户指南
- 用户使用指南将放置在此目录

## 📝 使用说明

1. **查找文档**: 根据需要的文档类型，进入相应的子目录
2. **添加文档**: 新增文档时，请放置到合适的分类目录中
3. **更新文档**: 保持文档内容的及时更新

---

**创建时间**: 2025-01-31  
**目的**: 优化项目文档组织结构
