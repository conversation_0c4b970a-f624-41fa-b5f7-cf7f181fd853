import { Public_MockUp_ModelSelector } from '@/app/features/deviceMockup/publicMockupModelSelector'

interface MobileMockup_TabsProps {
    isActiveModel: boolean
    activeTab: string
    setActiveTab: (tab: string) => void
    setIsActiveModel: (isActiveModel: boolean) => void
}

export const MobileMockup_Tabs = ({
    isActiveModel,
    activeTab,
    setActiveTab,
    setIsActiveModel,
}: MobileMockup_TabsProps) => {
    const mobileMockupTabs = [
        {
            label: 'layout',
            renderContent: () => {
                return (
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <g fill='currentColor'>
                            <rect width={9} height={15} x={2} y={3} rx='2.5' />
                            <rect width={9} height={15} x={13} y={6} opacity='0.3' rx='2.5' />
                        </g>
                    </svg>
                )
            },
        },
        {
            label: 'style',
            renderContent: () => {
                return (
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            d='M3.996 21.017c1.316 1.294 2.964 1.313 4.257.038 1.162-1.145 2.207-3.691 3.071-4.842l2.174 2.149c.562.562 1.262.556 1.802.015l.815-.806c.558-.553.552-1.205-.015-1.765L9.288 9.089c-.576-.565-1.241-.565-1.796-.018l-.818.806c-.549.541-.557 1.214.01 1.773l2.18 2.144c-1.16.848-3.736 1.882-4.907 3.024-1.287 1.278-1.279 2.907.039 4.199m2.133-1.015a1.027 1.027 0 0 1-1.038-1.021c0-.56.46-1.014 1.038-1.014a1.017 1.017 0 1 1 0 2.035m10.846-4.583 3.358-3.307c.903-.893.885-1.985-.03-2.898l-.539-.538c-.845 1.139-3.388 2.462-3.885 1.975-.076-.077-.087-.229.026-.344 1.047-1.037 1.676-1.987 1.806-3.645l-4.278-4.223c-.796-.787-2.107-.529-2.451.869-.543 2.269-.992 3.536-1.6 4.641z'
                        />
                    </svg>
                )
            },
        },
        {
            label: 'shadow',
            renderContent: () => {
                return (
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <g fill='currentColor'>
                            <rect width={15} height={17} x={3} y={2} opacity='0.2' rx={4} />
                            <path
                                fillRule='evenodd'
                                d='M6.133 18.866c.065.364.16.671.302.949a4 4 0 0 0 1.749 1.749C9.039 22 10.159 22 12.4 22h2.2c2.24 0 3.36 0 4.215-.436a4 4 0 0 0 1.749-1.749C21 18.96 21 17.84 21 15.6v-4.2c0-2.241 0-3.361-.436-4.216a4 4 0 0 0-1.749-1.749 3.3 3.3 0 0 0-.949-.302C18 5.888 18 6.889 18 8.399V12.6c0 2.24 0 3.36-.436 4.215a4 4 0 0 1-1.749 1.749C14.96 19 13.84 19 11.6 19H9.399c-1.51 0-2.511 0-3.266-.134'
                            />
                        </g>
                    </svg>
                )
            },
        },
        {
            label: 'details',
            renderContent: () => {
                return (
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            fillRule='evenodd'
                            d='M11.877 7.709a1.8 1.8 0 0 1-1.81-1.798c0-1.019.805-1.824 1.81-1.824.993 0 1.785.805 1.785 1.824 0 .993-.792 1.798-1.785 1.798m-2.25 11.518c-.59 0-1.055-.427-1.055-1.031 0-.566.465-1.019 1.055-1.019h1.559V11.77h-1.32c-.591 0-1.056-.427-1.056-1.031 0-.566.465-1.018 1.056-1.018h2.489c.754 0 1.143.528 1.143 1.32v6.136h1.433c.604 0 1.069.453 1.069 1.019 0 .604-.465 1.031-1.069 1.031z'
                        />
                    </svg>
                )
            },
        },
    ]

    const handleTabClick = (tab: { label: string }) => {
        if (activeTab === tab.label) {
            setActiveTab('')
            return
        }

        setActiveTab(tab.label)
    }

    return (
        <div className='panel-control-switcher'>
            <div className='stack'>
                <Public_MockUp_ModelSelector
                    isActiveModel={isActiveModel}
                    setIsActiveModel={setIsActiveModel}
                />
                <div className='divider' />

                {mobileMockupTabs.map((tab, index) => {
                    return (
                        <button
                            onClick={() => handleTabClick(tab)}
                            key={tab.label}
                            type='button'
                            className={`button default-button large-button undefined-button undefined-blur undefined-round ${activeTab === tab.label ? 'true-active' : 'false-active'} mobile-control-switcher-button`}
                            style={{ flexDirection: 'row' }}
                        >
                            {tab.renderContent()}
                            <span>{tab.label}</span>
                        </button>
                    )
                })}
            </div>
        </div>
    )
}
