'use client'

import { useState } from 'react'
import { useColorGenerationFromImage } from '@/app/shared/hooks/useColorGenerationFromImage'
import type { IColorItem, IMeshItem } from '@/app/shared/types/colorTypes'

/**
 * 颜色生成Hook使用示例组件
 *
 * 展示如何使用 useColorGenerationFromImage Hook 来根据图片生成各种背景样式
 */
export function ColorGenerationExample() {
    // 使用示例图片URL
    const [imageUrl, setImageUrl] = useState<string | null>('/__壁纸测试/walller__1.jpg')

    // 使用Hook获取颜色数据
    const { solidColors, gradientColors, meshColors, sourceColors, isLoading, error } =
        useColorGenerationFromImage(imageUrl, {
            colorCount: 6,
            quality: 8,
        })

    if (error) {
        return (
            <div className='p-4 bg-red-50 rounded-lg'>
                <h3 className='text-red-800 font-semibold'>错误</h3>
                <p className='text-red-600'>{error.message}</p>
            </div>
        )
    }

    return (
        <div className='p-4 space-y-6'>
            {/* 图片URL输入 */}
            <div className='space-y-2'>
                <label className='block text-sm font-medium'>图片URL:</label>
                <input
                    type='text'
                    value={imageUrl || ''}
                    onChange={e => setImageUrl(e.target.value || null)}
                    placeholder='输入图片URL'
                    className='w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                />
                <button
                    onClick={() => setImageUrl('/__壁纸测试/walller__1.jpg')}
                    className='px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600'
                >
                    使用默认图片
                </button>
            </div>

            {/* 加载状态 */}
            {isLoading && (
                <div className='flex items-center space-x-2 text-gray-600'>
                    <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900'></div>
                    <span>正在提取颜色...</span>
                </div>
            )}

            {/* 原始颜色数据 */}
            {sourceColors && (
                <div>
                    <h3 className='text-lg font-semibold mb-2'>
                        提取的原始颜色 ({sourceColors.hexColors.length}个)
                    </h3>
                    <div className='flex space-x-2'>
                        {sourceColors.hexColors.map((color, index) => (
                            <div
                                key={index}
                                className='w-8 h-8 rounded border border-gray-300'
                                style={{ backgroundColor: color }}
                                title={color}
                            />
                        ))}
                    </div>
                </div>
            )}

            {/* 纯色背景 */}
            {solidColors.length > 0 && (
                <div>
                    <h3 className='text-lg font-semibold mb-2'>
                        纯色背景 ({solidColors.length}个)
                    </h3>
                    <div className='grid grid-cols-6 gap-2'>
                        {solidColors.map((color, index) => (
                            <ColorSwatch key={index} color={color} label={`纯色 ${index + 1}`} />
                        ))}
                    </div>
                </div>
            )}

            {/* 渐变背景 */}
            {gradientColors.length > 0 && (
                <div>
                    <h3 className='text-lg font-semibold mb-2'>
                        渐变背景 ({gradientColors.length}个)
                    </h3>
                    <div className='grid grid-cols-3 gap-4'>
                        {gradientColors.map((color, index) => (
                            <GradientSwatch key={index} color={color} label={`渐变 ${index + 1}`} />
                        ))}
                    </div>
                </div>
            )}

            {/* 网格背景 */}
            {meshColors.length > 0 && (
                <div>
                    <h3 className='text-lg font-semibold mb-2'>网格背景 ({meshColors.length}个)</h3>
                    <div className='grid grid-cols-2 gap-4'>
                        {meshColors.map((color, index) => (
                            <MeshSwatch key={index} color={color} label={`网格 ${index + 1}`} />
                        ))}
                    </div>
                </div>
            )}

            {/* 空状态 */}
            {!isLoading && !error && solidColors.length === 0 && (
                <div className='text-gray-500 text-center py-8'>请输入图片URL来生成颜色样式</div>
            )}
        </div>
    )
}

// 子组件：纯色色块
function ColorSwatch({ color, label }: { color: IColorItem; label: string }) {
    return (
        <div className='space-y-1'>
            <div
                className='w-full h-16 rounded border border-gray-300'
                style={{ background: color.background }}
                title={label}
            />
            <p className='text-xs text-center text-gray-600'>{label}</p>
        </div>
    )
}

// 子组件：渐变色块
function GradientSwatch({ color, label }: { color: IColorItem; label: string }) {
    return (
        <div className='space-y-1'>
            <div
                className='w-full h-20 rounded border border-gray-300'
                style={{ background: color.background }}
                title={label}
            />
            <p className='text-xs text-center text-gray-600'>{label}</p>
        </div>
    )
}

// 子组件：网格色块
function MeshSwatch({ color, label }: { color: IMeshItem; label: string }) {
    return (
        <div className='space-y-1'>
            <div
                className={`w-full h-24 rounded border border-gray-300 ${color.className}`}
                style={{
                    backgroundColor: color.backgroundColor,
                    backgroundImage: color.backgroundImage,
                }}
                title={label}
            />
            <p className='text-xs text-center text-gray-600'>{label}</p>
        </div>
    )
}
