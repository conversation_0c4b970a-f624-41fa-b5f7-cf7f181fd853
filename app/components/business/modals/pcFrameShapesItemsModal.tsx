import { useIsMobile } from '@/app/shared/hooks/useAppState'

export const PcFrame_ShapesItemsModal = () => {
    return (
        <div
            className='popover frame-shapes-scene-popover'
            style={{
                top: '281.383px',
                right: 'unset',
                bottom: 'unset',
                left: 240,
                width: 236,
                height: 380,
                margin: '-160px 0px 0px',
                filter: 'blur(0px)',
                opacity: 1,
                transform: 'none',
            }}
        >
            <div className='active-shape'>
                <div className='list'>
                    <button
                        type='button'
                        className='button icon-button medium-button undefined-button undefined-blur undefined-round true-active undefined'
                        style={{ flexDirection: 'row', aspectRatio: '1 / 1', padding: 4 }}
                    >
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                            style={{ width: '100%', height: '100%' }}
                        />
                    </button>
                    <button
                        type='button'
                        className='button icon-button medium-button undefined-button undefined-blur undefined-round false-active undefined'
                        style={{ flexDirection: 'row', aspectRatio: '1 / 1', padding: 4 }}
                    >
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                            style={{ width: '100%', height: '100%' }}
                        />
                    </button>
                    <button
                        type='button'
                        className='button icon-button medium-button undefined-button undefined-blur undefined-round false-active undefined'
                        style={{ flexDirection: 'row', aspectRatio: '1 / 1', padding: 4 }}
                    >
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/display-assets/3d-shapes/glass-blue/w200/2.avif'
                            style={{ width: '100%', height: '100%' }}
                        />
                    </button>
                    <button
                        type='button'
                        className='button icon-button medium-button undefined-button undefined-blur undefined-round false-active undefined'
                        style={{ flexDirection: 'row', aspectRatio: '1 / 1', padding: 4 }}
                    >
                        <img
                            crossOrigin='anonymous'
                            loading='lazy'
                            decoding='async'
                            src='/display-assets/3d-shapes/glass-blue/w200/6.avif'
                            style={{ width: '100%', height: '100%' }}
                        />
                    </button>
                </div>
            </div>
            <div className='v-stack'>
                <div className='scroll'>
                    <div className='v-stack-content' style={{ gap: 10, padding: 10 }}>
                        <div className='panel-control-grid col-4'>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/2.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/3.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/4.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined true-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/6.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/7.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/8.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/9.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/10.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/11.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className='panel-button undefined false-active '>
                                <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                    <div className='image-wrapper'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            src='/display-assets/3d-shapes/glass-blue/w200/12.avif'
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <a
                            href='https://www.figma.com/community/file/1092431047678134762/abstract-3d-shapes'
                            target='_blank'
                            rel='noreferrer'
                        >
                            <p className='footnote gray-text' style={{ textAlign: 'center' }}>
                                Glass Blue by Кристина
                            </p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    )
}
