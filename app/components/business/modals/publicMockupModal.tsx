import { useIsMobile } from '@/app/shared/hooks/useAppState'

export const Public_MockupModal = ({
    isActiveModel,
    setIsActiveModel,
}: {
    isActiveModel: boolean
    setIsActiveModel: (isActiveModel: boolean) => void
}) => {
    const isMobile = useIsMobile()
    const publicRender = () => {
        return (
            <>
                <div className='picker-filters'>
                    <div className='buttons-wrapper flex'>
                        <button
                            type='button'
                            className='button default-button small-button undefined-button true-blur true-round undefined-active text-capitalize bg-gray-icon'
                            style={{ flexDirection: 'row' }}
                        >
                            <span>all</span>
                        </button>
                        <button
                            type='button'
                            className='button default-button small-button undefined-button false-blur true-round undefined-active text-capitalize gray-text2'
                            style={{ flexDirection: 'row' }}
                        >
                            <span>phone</span>
                        </button>
                        <button
                            type='button'
                            className='button default-button small-button undefined-button false-blur true-round undefined-active text-capitalize gray-text2'
                            style={{ flexDirection: 'row' }}
                        >
                            <span>tablet</span>
                        </button>
                        <button
                            type='button'
                            className='button default-button small-button undefined-button false-blur true-round undefined-active text-capitalize gray-text2'
                            style={{ flexDirection: 'row' }}
                        >
                            <span>laptop</span>
                        </button>
                        <button
                            type='button'
                            className='button default-button small-button undefined-button false-blur true-round undefined-active text-capitalize gray-text2'
                            style={{ flexDirection: 'row' }}
                        >
                            <span>desktop</span>
                        </button>
                        <button
                            type='button'
                            className='button default-button small-button undefined-button false-blur true-round undefined-active text-capitalize gray-text2'
                            style={{ flexDirection: 'row' }}
                        >
                            <span>wearable</span>
                        </button>
                    </div>
                </div>
                <div className='mockups-list'>
                    <section className='mock-picker-section'>
                        <div className='head'>
                            <span className='h4'>Essentials</span>
                            <button
                                type='button'
                                className='button default-button tiny-button secondary-button undefined-blur true-round undefined-active undefined'
                                style={{
                                    flexDirection: 'row',
                                    minWidth: 64,
                                    visibility: 'visible',
                                }}
                            >
                                <span>See all</span>
                            </button>
                        </div>
                        {/* 点击展开加下面的类 */}
                        {/* <div id='horizontalStack' className='h-stack show-grid'> */}
                        <div id='horizontalStack' className='h-stack show-list'>
                            <div className='content' style={{ gap: 10 }}>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Screenshot/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Screenshot</span>
                                            <p className='footnote gray-text'>13 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Screenshot/styles/default.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Screenshot/styles/glass-light.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Screenshot/styles/glass-dark.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 6</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Browser/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Browser</span>
                                            <p className='footnote gray-text'>10 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Browser/styles/safari-light.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Browser/styles/chrome-light.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Browser/styles/arc-light.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Minimal Desktop/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Minimal Desktop</span>
                                            <p className='footnote gray-text'>7 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Minimal Desktop/styles/default.png'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section className='mock-picker-section'>
                        <div className='head'>
                            <span className='h4'>iPhone 16 Lineup</span>
                            <button
                                type='button'
                                className='button default-button tiny-button secondary-button undefined-blur true-round undefined-active undefined'
                                style={{
                                    flexDirection: 'row',
                                    minWidth: 64,
                                    visibility: 'visible',
                                }}
                            >
                                <span>See all</span>
                            </button>
                        </div>
                        <div id='horizontalStack' className='h-stack show-list'>
                            <div className='content' style={{ gap: 10 }}>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 16/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 16</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16/styles/black.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16/styles/white.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16/styles/ultramarine.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 16 Plus/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 16 Plus</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16 Plus/styles/black.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16 Plus/styles/white.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16 Plus/styles/ultramarine.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 16 Pro/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 16 Pro</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16 Pro/styles/black-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16 Pro/styles/natural-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16 Pro/styles/white-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 16 Pro Max/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 16 Pro Max</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16 Pro Max/styles/black-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16 Pro Max/styles/natural-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 16 Pro Max/styles/white-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section className='mock-picker-section'>
                        <div className='head'>
                            <span className='h4'>iPhone 15 &amp; earlier</span>
                            <button
                                type='button'
                                className='button default-button tiny-button secondary-button undefined-blur true-round undefined-active undefined'
                                style={{
                                    flexDirection: 'row',
                                    minWidth: 64,
                                    visibility: 'visible',
                                }}
                            >
                                <span>See all</span>
                            </button>
                        </div>
                        <div id='horizontalStack' className='h-stack show-list'>
                            <div className='content' style={{ gap: 10 }}>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 15/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 15</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15/styles/black.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15/styles/blue.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15/styles/green.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item active'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 15 Plus/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 15 Plus</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15 Plus/styles/black.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15 Plus/styles/blue.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15 Plus/styles/green.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 15 Pro/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 15 Pro</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15 Pro/styles/black-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15 Pro/styles/dark-blue.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15 Pro/styles/natural-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 15 Pro Max/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 15 Pro Max</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15 Pro Max/styles/black-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15 Pro Max/styles/dark-blue.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 15 Pro Max/styles/natural-titanium.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 14/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 14</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14/styles/midnight.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14/styles/blue.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14/styles/purple.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 14 Plus/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 14 Plus</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14 Plus/styles/midnight.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14 Plus/styles/blue.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14 Plus/styles/purple.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 14 Pro/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 14 Pro</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14 Pro/styles/space-black.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14 Pro/styles/silver.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14 Pro/styles/deep-purple.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPhone 14 Pro Max/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPhone 14 Pro Max</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14 Pro Max/styles/space-black.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14 Pro Max/styles/silver.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPhone 14 Pro Max/styles/deep-purple.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section className='mock-picker-section'>
                        <div className='head'>
                            <span className='h4'>Android phones</span>
                            <button
                                type='button'
                                className='button default-button tiny-button secondary-button undefined-blur true-round undefined-active undefined'
                                style={{
                                    flexDirection: 'row',
                                    minWidth: 64,
                                    visibility: 'visible',
                                }}
                            >
                                <span>See all</span>
                            </button>
                        </div>
                        <div id='horizontalStack' className='h-stack show-list'>
                            <div className='content' style={{ gap: 10 }}>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Nothing Phone/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Nothing Phone</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Nothing Phone/styles/white.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Nothing Phone/styles/black.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Nothing Phone/styles/display.png'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Pixel 7 Pro/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Pixel 7 Pro</span>
                                            <p className='footnote gray-text'>19 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Pixel 7 Pro/styles/hazel.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Pixel 7 Pro/styles/obsidian.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Pixel 7 Pro/styles/snow.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section className='mock-picker-section'>
                        <div className='head'>
                            <span className='h4'>Tablets</span>
                            <button
                                type='button'
                                className='button default-button tiny-button secondary-button undefined-blur true-round undefined-active undefined'
                                style={{
                                    flexDirection: 'row',
                                    minWidth: 64,
                                    visibility: 'visible',
                                }}
                            >
                                <span>See all</span>
                            </button>
                        </div>
                        <div id='horizontalStack' className='h-stack show-list'>
                            <div className='content' style={{ gap: 10 }}>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPad Pro 13/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPad Pro 13</span>
                                            <p className='footnote gray-text'>13 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Pro 13/styles/space-gray.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Pro 13/styles/silver.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Pro 13/styles/display.png'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPad Pro 11/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPad Pro 11</span>
                                            <p className='footnote gray-text'>13 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Pro 11/styles/space-gray.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Pro 11/styles/silver.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Pro 11/styles/display.png'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPad Air/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPad Air</span>
                                            <p className='footnote gray-text'>13 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Air/styles/space-gray.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Air/styles/starlight.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Air/styles/blue.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 3</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iPad Mini/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iPad Mini</span>
                                            <p className='footnote gray-text'>13 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Mini/styles/space-gray.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Mini/styles/starlight.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iPad Mini/styles/pink.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section className='mock-picker-section'>
                        <div className='head'>
                            <span className='h4'>Laptops</span>
                            <button
                                type='button'
                                className='button default-button tiny-button secondary-button undefined-blur true-round undefined-active undefined'
                                style={{
                                    flexDirection: 'row',
                                    minWidth: 64,
                                    visibility: 'visible',
                                }}
                            >
                                <span>See all</span>
                            </button>
                        </div>
                        <div id='horizontalStack' className='h-stack show-list'>
                            <div className='content' style={{ gap: 10 }}>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Macbook Pro 16/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Macbook Pro 16</span>
                                            <p className='footnote gray-text'>9 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Macbook Pro 16/styles/silver.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Macbook Pro 16/styles/display.png'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Macbook Air M2/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Macbook Air M2</span>
                                            <p className='footnote gray-text'>9 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Macbook Air M2/styles/midnight.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Macbook Air M2/styles/silver.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Macbook Air M2/styles/space-gray.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Macbook Air 13/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Macbook Air 13</span>
                                            <p className='footnote gray-text'>9 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Macbook Air 13/styles/space-gray.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Macbook Air 13/styles/silver.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Macbook Air 13/styles/gold.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 1</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section className='mock-picker-section'>
                        <div className='head'>
                            <span className='h4'>Desktop</span>
                            <button
                                type='button'
                                className='button default-button tiny-button secondary-button undefined-blur true-round undefined-active undefined'
                                style={{
                                    flexDirection: 'row',
                                    minWidth: 64,
                                    visibility: 'visible',
                                }}
                            >
                                <span>See all</span>
                            </button>
                        </div>
                        <div id='horizontalStack' className='h-stack show-list'>
                            <div className='content' style={{ gap: 10 }}>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iMac 24/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iMac 24</span>
                                            <p className='footnote gray-text'>9 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iMac 24/styles/silver.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iMac 24/styles/blue.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iMac 24/styles/purple.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 5</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Pro Display XDR/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Pro Display XDR</span>
                                            <p className='footnote gray-text'>9 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Pro Display XDR/styles/silver.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Pro Display XDR/styles/display.png'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/iMac Pro/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>iMac Pro</span>
                                            <p className='footnote gray-text'>9 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iMac Pro/styles/space-gray.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/iMac Pro/styles/display.png'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section className='mock-picker-section'>
                        <div className='head'>
                            <span className='h4'>Wearables</span>
                            <button
                                type='button'
                                className='button default-button tiny-button secondary-button undefined-blur true-round undefined-active undefined'
                                style={{
                                    flexDirection: 'row',
                                    minWidth: 64,
                                    visibility: 'visible',
                                }}
                            >
                                <span>See all</span>
                            </button>
                        </div>
                        <div id='horizontalStack' className='h-stack show-list'>
                            <div className='content' style={{ gap: 10 }}>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Apple Watch Ultra/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Apple Watch Ultra</span>
                                            <p className='footnote gray-text'>11 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Apple Watch Ultra/styles/black-ocean-band-navy.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Apple Watch Ultra/styles/black-titanium-loop.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Apple Watch Ultra/styles/black-trail-loop-black.png'
                                                />
                                            </div>
                                            <div>
                                                <span className='caption gray-text2'>+ 4</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Apple Watch 10 46mm/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Apple Watch 10 46mm</span>
                                            <p className='footnote gray-text'>11 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Apple Watch 10 46mm/styles/jet-black.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Apple Watch 10 46mm/styles/rose-gold.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Apple Watch 10 46mm/styles/display.png'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='mock-item false'>
                                    <div className='preview'>
                                        <img
                                            crossOrigin='anonymous'
                                            loading='lazy'
                                            decoding='async'
                                            alt='Thumb'
                                            src='/mockups/Apple Watch 10 42mm/thumbs/1.png'
                                        />
                                    </div>
                                    <div className='details'>
                                        <div className='copy'>
                                            <span className='h5 truncate'>Apple Watch 10 42mm</span>
                                            <p className='footnote gray-text'>11 layouts</p>
                                        </div>
                                        <div className='variants'>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Apple Watch 10 42mm/styles/jet-black.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Apple Watch 10 42mm/styles/rose-gold.png'
                                                />
                                            </div>
                                            <div>
                                                <img
                                                    crossOrigin='anonymous'
                                                    loading='lazy'
                                                    decoding='async'
                                                    alt='Style'
                                                    src='/mockups/Apple Watch 10 42mm/styles/display.png'
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </>
        )
    }

    const mobileInViewRender = () => {
        return (
            <div className='modal-container ' style={{ justifyContent: 'center', opacity: 1 }}>
                <div
                    className='modal-sheet full-sheet-type mockup-picker-mobile-modal'
                    style={{
                        width: 'max-content',
                        maxWidth: 'max-content',
                        height: 'max-content',
                        maxHeight: 'max-content',
                        transform: 'none',
                    }}
                >
                    <div className='modal-head modal-title-bar'>
                        <h4>Mockups</h4>
                        <button
                            onClick={() => {
                                setIsActiveModel(false)
                            }}
                            type='button'
                            className='button icon-button small-button secondary-button undefined-blur true-round undefined-active undefined'
                            style={{ flexDirection: 'row' }}
                        >
                            <svg
                                xmlns='http://www.w3.org/2000/svg'
                                fill='currentColor'
                                viewBox='0 0 24 24'
                            >
                                <path d='M4.362 17.793c-.48.48-.49 1.332.01 1.831.51.5 1.361.49 1.832.02L12 13.846l5.788 5.788c.49.49 1.332.49 1.831-.01.5-.51.5-1.341.01-1.831l-5.788-5.788 5.788-5.798c.49-.49.5-1.332-.01-1.831-.499-.5-1.341-.5-1.83-.01L12 10.154 6.204 4.366c-.47-.48-1.332-.5-1.832.01-.5.5-.49 1.361-.01 1.831l5.788 5.798z' />
                            </svg>
                        </button>
                    </div>
                    <div id='modalScrollView' className='modal-scroll-view'>
                        <div className='content-view scroll-view'>{publicRender()}</div>
                    </div>
                </div>
            </div>
        )
    }

    const pcInViewRender = () => {
        return (
            <div
                className='drop-menu'
                style={{
                    top: 'calc(100% + 6px)',
                    bottom: 'unset',
                    right: 'unset',
                    left: 'unset',
                    width: 440,
                    filter: 'blur(0px)',
                    opacity: 1,
                    transform: 'none',
                }}
            >
                {publicRender()}
            </div>
        )
    }
    if (!isActiveModel) {
        return null
    }

    if (isMobile) {
        return mobileInViewRender()
    }

    return pcInViewRender()
}
