import { useIsMobile } from '@/app/shared/hooks/useAppState'
import { useState } from 'react'
import { boxKey, sizeInfo, viewSizes } from '@/app/features/viewDimensions/utils/重构/dimensions'
import { DimensionSourceType } from '@/app/features/viewDimensions/utils/重构/状态管理'
import {
    useIsDefaultDimensionsAndMatches,
    useViewDimensions,
    useViewDimensionsWithActions,
    useCalculatedDimensions,
} from '@/app/shared/hooks/useAppState'

/**
 * 框架选择器标签页枚举
 * 定义了可用的标签页类型
 */
enum FrameTabType {
    /** 预设尺寸标签页 */
    PRESETS = 'presets',
    /** 自定义尺寸标签页 */
    CUSTOM = 'custom',
}

/**
 * 框架选择器标签页配置接口
 */
interface IFrameTabConfig {
    /** 标签页值 */
    value: FrameTabType
    /** 标签页显示标签 */
    label: string
}

/**
 * 自定义尺寸状态接口
 */
interface ICustomSizeState {
    /** 使用宽度 */
    useWidth: number
    /** 使用高度 */
    useHeight: number
}

/**
 * 公共框架尺寸模态框组件
 * 提供预设尺寸和自定义尺寸选择功能
 * 支持移动端和桌面端不同的显示方式
 * @returns {React.ReactElement} 框架尺寸选择器组件
 */
export const Public_FrameSizeModal = (): React.ReactElement => {
    // 获取当前视图尺寸状态
    const viewDimensions = useViewDimensions()
    // 获取计算后的尺寸信息
    const { calculatedWidth, calculatedHeight, calculationMethod } = useCalculatedDimensions()
    // 判断是否是默认尺寸匹配函数
    const isDefaultAndMatches = useIsDefaultDimensionsAndMatches()
    // 获取更新尺寸的操作函数（使用新的带计算的方法）
    const { setWithCalculation } = useViewDimensionsWithActions()

    /**
     * 活动标签页配置
     */
    const activeTabs: Record<string, IFrameTabConfig> = {
        presets: {
            value: FrameTabType.PRESETS,
            label: 'Presets',
        },
        custom: {
            value: FrameTabType.CUSTOM,
            label: 'Custom',
        },
    }

    // 当前激活的标签页状态
    const [activeFrameTab, setActiveFrameTab] = useState<FrameTabType>(FrameTabType.PRESETS)

    // 自定义尺寸状态
    const [customSize, setCustomSize] = useState<ICustomSizeState>({
        useWidth: viewDimensions.useWidth,
        useHeight: viewDimensions.useHeight,
    })

    /**
     * 处理自定义尺寸输入变化
     * @param {string} key - 要更新的属性键名
     * @param {number} value - 新的数值
     */
    const handleSetCustomSize = (key: keyof ICustomSizeState, value: number): void => {
        setCustomSize({
            ...customSize,
            [key]: value,
        })
    }

    /**
     * 处理自定义尺寸表单提交
     * @param {React.FormEvent} e - 表单事件对象
     */
    const handleCustomSize = (e: React.FormEvent): void => {
        // 1. 阻止默认表单提交行为
        e.preventDefault()

        // 2. 优先处理错误情况：校验必填项
        if (!customSize.useWidth || !customSize.useHeight) {
            console.error('自定义尺寸的宽度和高度不能为空')
            return
        }

        // 3. 校验尺寸范围
        if (customSize.useWidth < 128 || customSize.useWidth > 7680) {
            console.error('宽度必须在 128 到 7680 像素之间')
            return
        }

        if (customSize.useHeight < 128 || customSize.useHeight > 7680) {
            console.error('高度必须在 128 到 7680 像素之间')
            return
        }

        // 4. 构建尺寸信息
        const width = customSize.useWidth
        const height = customSize.useHeight
        const useWidth = width
        const useHeight = height
        const ratioWidth = width // 用户自定义的，比例宽度暂时等于宽度
        const ratioHeight = height // 用户自定义的，比例高度暂时等于高度

        // 5. 应用自定义尺寸（使用新的带计算的方法）
        handleSize(
            {
                key: boxKey.user_custom,
                useWidth: useWidth,
                useHeight: useHeight,
                ratioWidth: ratioWidth,
                ratioHeight: ratioHeight,
            },
            DimensionSourceType.Custom,
        )
    }

    /**
     * 处理尺寸选择和应用（使用新的带计算的方法）
     * @param {sizeInfo} sizeInfo - 尺寸信息对象
     * @param {DimensionSourceType} sourceType - 尺寸来源类型
     */
    const handleSize = (sizeInfo: sizeInfo, sourceType: DimensionSourceType): void => {
        // 优先处理错误情况
        if (!sizeInfo) {
            console.error('尺寸信息不能为空')
            return
        }

        if (!sizeInfo.key) {
            console.error('尺寸信息缺少必要的 key 字段')
            return
        }

        if (!sizeInfo.useWidth || !sizeInfo.useHeight) {
            console.error('尺寸信息缺少必要的宽度或高度字段')
            return
        }

        // 当点击预设尺寸时，让 custom 的 宽度 和 高度 也跟当前点击的尺寸一致
        if (sourceType === DimensionSourceType.Default) {
            setCustomSize({
                useWidth: sizeInfo.useWidth,
                useHeight: sizeInfo.useHeight,
            })
        }

        // 使用新的带自动计算的方法更新当前视图尺寸状态
        setWithCalculation({
            key: sizeInfo.key,
            useWidth: sizeInfo.useWidth,
            useHeight: sizeInfo.useHeight,
            ratioWidth: sizeInfo.ratioWidth,
            ratioHeight: sizeInfo.ratioHeight,
            sourceType: sourceType,
        })

        console.log('📏 Public_FrameSizeModal: 尺寸设置完成，将自动计算显示尺寸')
    }

    /**
     * 渲染自定义尺寸输入区域
     * @returns {React.ReactElement} 自定义尺寸输入组件
     */
    const publicCustom = (): React.ReactElement => {
        return (
            <section className='custom-frame'>
                {/* 不是 plus 会员显示的徽章（当前隐藏） */}
                {/* <div style={{ position: 'absolute', inset: 6, zIndex: 5, cursor: 'pointer' }}>
                    <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                        <div className='plus-badge' style={{ width: 16, height: 16 }}>
                            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                <path
                                    fill='currentColor'
                                    d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                />
                            </svg>
                        </div>
                    </div>
                </div> */}
                <form autoComplete='off' onSubmit={handleCustomSize}>
                    <div className='custom-frame-input'>
                        <h6>W</h6>
                        <input
                            className='input-text'
                            placeholder='1920'
                            min={128}
                            max={7680}
                            type='number'
                            name='width'
                            value={customSize.useWidth}
                            onChange={e => handleSetCustomSize('useWidth', Number(e.target.value))}
                        />
                    </div>
                    <div className='custom-frame-input'>
                        <h6>H</h6>
                        <input
                            className='input-text'
                            placeholder='1440'
                            min={128}
                            max={7680}
                            type='number'
                            name='height'
                            value={customSize.useHeight}
                            onChange={e => handleSetCustomSize('useHeight', Number(e.target.value))}
                        />
                    </div>
                    <button
                        disabled={!customSize.useWidth || !customSize.useHeight}
                        type='submit'
                        className='button default-button medium-button primary-button undefined-blur undefined-round undefined-active set-button'
                        style={{ flexDirection: 'row' }}
                    >
                        <span>Set</span>
                    </button>
                </form>
            </section>
        )
    }

    /**
     * 渲染预设尺寸列表
     * 使用动态数据源 viewSizes 替代硬编码列表
     * @returns {React.ReactElement} 预设尺寸列表组件
     */
    const publicList = (): React.ReactElement => {
        return (
            <>
                {viewSizes.map((element, index) => {
                    // 根据不同渠道设置不同的类名
                    let platformClassName = ''
                    if (element.name === 'Default') {
                        platformClassName = 'default-presets'
                    } else if (element.name === 'Instagram') {
                        platformClassName = 'instagram-presets'
                    } else if (element.name === 'Twitter') {
                        platformClassName = 'twitter-presets'
                    } else if (element.name === 'Dribbble') {
                        platformClassName = 'dribbble-presets'
                    } else if (element.name === 'YouTube') {
                        platformClassName = 'youTube-presets'
                    } else if (element.name === 'Pinterest') {
                        platformClassName = 'pinterest-presets'
                    } else if (element.name === 'App Store') {
                        platformClassName = 'appstore-presets'
                    } else {
                        // 如果是其他未知渠道，抛出错误警告
                        console.error(`未知的平台类型: ${element.name}`)
                        platformClassName = 'default-presets'
                    }

                    return (
                        <section
                            key={index}
                            className={`frame-platform-presets ${platformClassName}`}
                        >
                            {/* 平台标题区域 */}
                            <div
                                className='section-head'
                                style={{ display: element.title ? '' : 'none' }}
                            >
                                {element.cover && (
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        src={element.cover}
                                        alt={`${element.title} icon`}
                                    />
                                )}
                                <h5>{element.title}</h5>
                            </div>

                            {/* 尺寸网格 */}
                            <div className='frames-grid'>
                                {element.sizes.map((size, sizeIndex) => {
                                    const ratioWidth = size.ratioWidth
                                    const ratioHeight = size.ratioHeight

                                    // 判断当前尺寸是否为激活状态
                                    let activeClassName = ''
                                    if (
                                        isDefaultAndMatches(size.key, size.useWidth, size.useHeight)
                                    ) {
                                        activeClassName = 'active'
                                    } else {
                                        activeClassName = 'false'
                                    }

                                    return (
                                        <button
                                            onClick={() =>
                                                handleSize(size, DimensionSourceType.Default)
                                            }
                                            key={sizeIndex}
                                            className={`frame-item ${activeClassName}`}
                                        >
                                            <div className='icon-wrapper'>
                                                <div className='frame-icon-display'>
                                                    <div
                                                        className='frame-item-icon'
                                                        style={{
                                                            aspectRatio: `${ratioWidth} / ${ratioHeight}`,
                                                        }}
                                                    >
                                                        <span className='caption gray-text2'>
                                                            {ratioWidth} : {ratioHeight}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className='details'>
                                                {size.title && (
                                                    <p className='h6 primary truncate'>
                                                        {size.title}
                                                    </p>
                                                )}
                                                <p className='footnote gray-text2'>
                                                    {size.useWidth} x {size.useHeight}
                                                </p>
                                            </div>
                                        </button>
                                    )
                                })}
                            </div>
                        </section>
                    )
                })}
            </>
        )
    }

    /**
     * 渲染移动端视图
     * 包含 Tab 切换按钮和对应的内容区域
     * @returns {React.ReactElement} 移动端框架尺寸选择器
     */
    const mobileInViewRender = (): React.ReactElement => {
        return (
            <div
                id='panel-undefined-control-mobile'
                className='panel-control-mobile frame-picker-control-mobile'
                style={{ opacity: 1, transform: 'none' }}
            >
                <div className='panel-control-segment-wrapper' style={{ flexDirection: 'column' }}>
                    {/* 预设尺寸内容区域 */}
                    {activeFrameTab === FrameTabType.PRESETS && (
                        <section className='segment-section'>
                            <div className='panel-control-stack'>
                                <div className='stack-content'>{publicList()}</div>
                            </div>
                        </section>
                    )}

                    {/* 自定义尺寸内容区域 */}
                    {activeFrameTab === FrameTabType.CUSTOM && (
                        <section className='segment-section'>{publicCustom()}</section>
                    )}

                    {/* 标签页切换按钮 */}
                    <div className='segment-buttons'>
                        {Object.keys(activeTabs).map(key => {
                            const tabConfig = activeTabs[key as keyof typeof activeTabs]

                            // 判断按钮激活状态
                            let buttonActiveClass = ''
                            if (activeFrameTab === tabConfig.value) {
                                buttonActiveClass = 'true-active'
                            } else {
                                buttonActiveClass = 'false-active'
                            }

                            return (
                                <button
                                    onClick={() => {
                                        setActiveFrameTab(tabConfig.value)
                                    }}
                                    key={key}
                                    type='button'
                                    className={`button default-button small-button undefined-button undefined-blur true-round ${buttonActiveClass} undefined`}
                                    style={{ flexDirection: 'row', minWidth: 88 }}
                                >
                                    <span>{tabConfig.label}</span>
                                </button>
                            )
                        })}
                    </div>
                </div>
            </div>
        )
    }

    /**
     * 渲染桌面端视图
     * 显示下拉菜单样式的框架尺寸选择器
     * @returns {React.ReactElement} 桌面端框架尺寸选择器
     */
    const pcInViewRender = (): React.ReactElement => {
        return (
            <>
                <div
                    className='drop-menu'
                    style={{
                        top: 'calc(100% + 6px)',
                        bottom: 'unset',
                        right: 'unset',
                        left: 'unset',
                        width: 340,
                        filter: 'blur(0px)',
                        opacity: 1,
                        transform: 'none',
                    }}
                >
                    {publicCustom()}
                    {publicList()}
                </div>
            </>
        )
    }

    // 根据设备类型返回对应的视图
    if (useIsMobile()) {
        return mobileInViewRender()
    } else {
        return pcInViewRender()
    }
}
