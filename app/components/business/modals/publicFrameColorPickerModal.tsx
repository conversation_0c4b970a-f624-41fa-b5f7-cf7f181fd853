import { useIsMobile } from '@/app/shared/hooks/useAppState'
import { useState, useEffect, useRef, useCallback } from 'react'
import { HexAlphaColorPicker, HexColorInput } from 'react-colorful'
import { useBackgroundStore } from '@/app/shared/hooks/useBackgroundStore'

/**
 * 组件属性接口
 * 现在所有状态都通过 useBackgroundStore 管理，不需要props
 */
interface PublicFrameColorPickerModalProps {
    // 所有状态现在通过 useBackgroundStore 管理，不需要props
}

/**
 * 颜色选择器组件 - 支持移动端和PC端不同的UI展示
 * 移动端使用模态框样式，PC端使用弹出框样式
 * 使用 react-colorful 库提供完整的颜色选择功能
 *
 * @param {PublicFrameColorPickerModalProps} props - 组件属性
 * @returns {JSX.Element} 渲染的颜色选择器组件
 */
export const PublicFrame_ColorPicker_Modal = ({}: PublicFrameColorPickerModalProps) => {
    // 检测是否为移动端设备
    const isMobile = useIsMobile()

    // 使用背景存储获取状态
    const { color, setColor, isColorPickerVisible, toggleColorPicker } = useBackgroundStore()

    // 管理本地颜色状态，用于颜色选择器，默认使用store中的颜色值
    const [localColor, setLocalColor] = useState<string>(color || '#ffffff')

    // 使用ref来跟踪是否正在拖动颜色选择器
    const isDraggingRef = useRef<boolean>(false)
    // 防抖定时器
    const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
    // 动画帧ID，用于控制更新频率
    const animationFrameRef = useRef<number | null>(null)
    // 组件是否已经初始化完成
    const isInitializedRef = useRef<boolean>(false)
    // 上次更新的颜色值，避免重复更新
    const lastColorRef = useRef<string>(color)

    // 同步store中的颜色到本地状态
    useEffect(() => {
        setLocalColor(color)
    }, [color])

    // 初始化状态
    useEffect(() => {
        if (isColorPickerVisible && !isInitializedRef.current) {
            const defaultColor = color || '#ffffff'
            setLocalColor(defaultColor)
            lastColorRef.current = defaultColor
            isInitializedRef.current = true
        }
    }, [isColorPickerVisible, color])

    /**
     * 使用 requestAnimationFrame 优化的颜色变化处理
     * 将更新频率限制在显示器刷新率内，避免过度渲染
     */
    const throttledColorChange = useCallback(
        (newColor: string) => {
            // 避免重复更新相同的颜色
            if (newColor === lastColorRef.current) {
                return
            }

            // 取消之前的动画帧
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current)
            }

            // 使用 requestAnimationFrame 确保更新频率不超过显示器刷新率
            animationFrameRef.current = requestAnimationFrame(() => {
                if (newColor !== lastColorRef.current) {
                    setColor(newColor)
                    lastColorRef.current = newColor
                }
            })
        },
        [setColor],
    )

    /**
     * 防抖处理拖动结束状态
     * 只用于重置拖动状态，不处理颜色更新
     */
    const debouncedDragEnd = useCallback(() => {
        // 清除之前的定时器
        if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current)
        }

        // 设置拖动结束标记
        debounceTimerRef.current = setTimeout(() => {
            isDraggingRef.current = false
        }, 150) // 增加到150ms，确保拖动状态稳定
    }, [])

    /**
     * 处理拖动开始事件
     * 预先设置拖动状态，避免初次拖动时的状态冲突
     */
    const handleDragStart = useCallback(() => {
        if (!isDraggingRef.current) {
            isDraggingRef.current = true
            // 清除可能存在的拖动结束定时器
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current)
            }
        }
    }, [])

    /**
     * 处理颜色选择器的颜色变化事件
     * 使用节流机制避免过度更新
     * @param {string} newColor - 新选择的颜色值（十六进制格式）
     */
    const handleColorPickerChange = (newColor: string): void => {
        // 确保拖动状态正确设置
        handleDragStart()

        // 立即更新本地状态，保证UI响应流畅
        setLocalColor(newColor)

        // 使用节流机制更新store
        throttledColorChange(newColor)

        // 重置拖动结束定时器
        debouncedDragEnd()
    }

    /**
     * 处理输入框的颜色变化事件
     * 直接更新父组件状态，因为输入框不存在拖动问题
     * @param {string} newColor - 新输入的颜色值（十六进制格式）
     */
    const handleInputChange = (newColor: string): void => {
        setLocalColor(newColor)
        lastColorRef.current = newColor

        // 输入框变化时直接更新store，无需节流
        setColor(newColor)
    }

    /**
     * 处理关闭按钮点击事件
     * 关闭颜色选择器模态框
     */
    const handleCloseClick = (): void => {
        // 清除防抖定时器
        if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current)
        }

        // 清除动画帧
        if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current)
        }

        // 重置所有状态
        isDraggingRef.current = false
        isInitializedRef.current = false

        // 关闭颜色选择器
        toggleColorPicker(false)
    }

    // 组件卸载时清理定时器和动画帧
    useEffect(() => {
        return () => {
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current)
            }
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current)
            }
        }
    }, [])

    // 如果弹窗不可见，则不渲染任何内容
    if (!isColorPickerVisible) {
        return null
    }

    /**
     * 渲染移动端版本的颜色选择器
     * 使用模态框样式，包含标题栏和关闭按钮
     *
     * @returns {JSX.Element} 移动端颜色选择器
     */
    const mobileRender = () => {
        return (
            <div
                className='modal-container'
                style={{ background: 'transparent', justifyContent: 'center', opacity: 1 }}
            >
                <div
                    className='modal-sheet sheet-type '
                    style={{
                        width: 'max-content',
                        maxWidth: 'max-content',
                        height: 'max-content',
                        maxHeight: 'max-content',
                        transform: 'none',
                    }}
                >
                    <div className='modal-head modal-title-bar'>
                        <h4>Color picker</h4>
                        <button
                            type='button'
                            className='button icon-button small-button secondary-button undefined-blur true-round undefined-active undefined'
                            style={{ flexDirection: 'row' }}
                            onClick={handleCloseClick}
                        >
                            <svg
                                xmlns='http://www.w3.org/2000/svg'
                                fill='currentColor'
                                viewBox='0 0 24 24'
                            >
                                <path d='M4.362 17.793c-.48.48-.49 1.332.01 1.831.51.5 1.361.49 1.832.02L12 13.846l5.788 5.788c.49.49 1.332.49 1.831-.01.5-.51.5-1.341.01-1.831l-5.788-5.788 5.788-5.798c-.49-.49.5-1.332-.01-1.831-.499-.5-1.341-.5-1.83-.01L12 10.154 6.204 4.366c-.47-.48-1.332-.5-1.832.01-.5.5-.49 1.361-.01 1.831l5.788 5.798z' />
                            </svg>
                        </button>
                    </div>
                    <div id='modalScrollView' className='modal-scroll-view'>
                        <div className='content-view undefined'>
                            <div className='color-picker'>
                                <HexAlphaColorPicker
                                    color={localColor}
                                    onChange={handleColorPickerChange}
                                />
                            </div>
                            <HexColorInput
                                color={localColor}
                                onChange={handleInputChange}
                                className='input-text'
                                placeholder='#ffffff'
                                style={{ textAlign: 'center' }}
                                prefixed
                            />
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    /**
     * 渲染PC端版本的颜色选择器
     * 使用弹出框样式，结构更加紧凑
     *
     * @returns {JSX.Element} PC端颜色选择器
     */
    const pcRender = () => {
        return (
            <div
                className='popover undefined'
                style={{
                    top: '421.383px',
                    right: 'unset',
                    bottom: 'unset',
                    left: 240,
                    width: 228,
                    height: 'auto',
                    margin: '-80px 0px 0px',
                    filter: 'blur(0px)',
                    opacity: 1,
                    transform: 'none',
                }}
            >
                <div className='v-stack'>
                    <div className='scroll'>
                        <div className='v-stack-content' style={{ gap: 12, padding: 12 }}>
                            <span className='h5'>Color picker</span>
                            <div className='color-picker'>
                                <HexAlphaColorPicker
                                    color={localColor}
                                    onChange={handleColorPickerChange}
                                />
                            </div>
                            <HexColorInput
                                color={localColor}
                                onChange={handleInputChange}
                                className='input-text'
                                placeholder='#ffffff'
                                style={{ textAlign: 'center' }}
                                prefixed
                            />
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    // 根据设备类型返回对应的渲染结果
    if (isMobile) {
        return mobileRender()
    } else {
        return pcRender()
    }
}
