export const PcMockup_Visibility = () => {
    return (
        <div className='panel-control undefined '>
            <span className='label gray-text'>visibility</span>
            <div className='controls'>
                <button
                    type='button'
                    className='button default-button small-button undefined-button undefined-blur undefined-round undefined-active hide-show-btn bg-panel-dim'
                    style={{ flexDirection: 'row', minWidth: '100%' }}
                >
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <g fill='currentColor'>
                            <path d='M6.573 8.319c-2.094 1.318-3.422 3.015-3.422 3.556 0 1.062 3.907 5.251 8.85 5.251a9.2 9.2 0 0 0 2.906-.482l.852.851c-1.135.42-2.399.675-3.758.675C6.173 18.17 2 13.366 2 11.875c0-.871 1.419-2.862 3.759-4.369zM22 11.875c0 .846-1.323 2.759-3.545 4.254l-.802-.801c1.956-1.273 3.189-2.856 3.189-3.453 0-.928-3.907-5.256-8.841-5.256-.928 0-1.819.153-2.655.407l-.848-.848a10.6 10.6 0 0 1 3.503-.598c5.876 0 9.999 4.798 9.999 6.295m-11.104.762q.137.197.335.335l2.603 2.599a4.06 4.06 0 0 1-1.833.432 4.145 4.145 0 0 1-4.144-4.144c0-.657.153-1.278.43-1.828zm5.249-.778c0 .578-.119 1.129-.333 1.628L10.371 8.05a4.145 4.145 0 0 1 5.774 3.809M17.858 18.274a.486.486 0 0 0 .71 0 .483.483 0 0 0 0-.71L6.147 5.147a.51.51 0 0 0-.716 0 .51.51 0 0 0 0 .712z' />
                        </g>
                    </svg>
                    <span>Hide Mockup</span>
                </button>
            </div>
        </div>
    )
}
