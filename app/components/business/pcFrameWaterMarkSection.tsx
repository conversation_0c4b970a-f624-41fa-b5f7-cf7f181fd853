import { useIsMobile } from './hooks/useAppState'

export const PcFrame_WaterMarkSection = ({
    isActiveWatermarkModal,
    setIsActiveWatermarkModal,
}: {
    isActiveWatermarkModal: boolean
    setIsActiveWatermarkModal: (isActive: boolean) => void
}) => {
    return (
        <div className='panel-control undefined '>
            <span className='label gray-text'>watermark</span>
            <div className='controls'>
                <div
                    className='panel-button watermark-layout-button undefined-active '
                    onClick={() => setIsActiveWatermarkModal(!isActiveWatermarkModal)}
                >
                    <div className='preview' style={{ aspectRatio: '5 / 1' }}>
                        <div className='custom-watermark-display'>
                            <div className='watermark-element watermark-none'>
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        fillRule='evenodd'
                                        d='M3.575 7.088A9.7 9.7 0 0 0 2.25 12c0 5.384 4.365 9.75 9.75 9.75 1.79 0 3.468-.483 4.911-1.326l-1.104-1.104A8.25 8.25 0 0 1 3.75 12a8.2 8.2 0 0 1 .929-3.808zm15.686 8.831A8.25 8.25 0 0 0 12 3.75a8.2 8.2 0 0 0-3.92.988L6.981 3.639A9.7 9.7 0 0 1 12 2.25c5.384 0 9.75 4.365 9.75 9.75a9.7 9.7 0 0 1-1.39 5.018z'
                                    />
                                    <rect
                                        width='1.89'
                                        height='26.833'
                                        x='1.788'
                                        y='3.211'
                                        fill='currentColor'
                                        rx='0.945'
                                        ry='0.945'
                                        transform='rotate(-45 1.789 3.211)'
                                    />
                                </svg>
                                <div className='texts-wrapper'>
                                    <span className='title-text'>No watermark</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
