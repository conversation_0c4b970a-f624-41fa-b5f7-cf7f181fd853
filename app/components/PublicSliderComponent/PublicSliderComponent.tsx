import { useState, useRef, useCallback } from 'react'
import { SliderConfig, SliderType, SLIDER_PRESETS } from './SliderConfig'

/**
 * 通用滑动条组件属性接口
 * 定义了组件的所有可配置属性
 */
interface UniversalSliderProps {
    /** 当前滑动条的值 - 必须在min和max范围内 */
    value: number
    /** 值变化时的回调函数 - 当用户拖动、点击或键盘操作时触发 */
    setValue: (value: number) => void
    /** 滑动条类型或自定义配置 - 可以是预设类型或完全自定义的配置对象 */
    config: SliderType | SliderConfig
    /** 额外的CSS类名 - 用于自定义样式 */
    className?: string
    /** 是否隐藏轨道覆盖 - 优先级高于配置中的hideRail，用于特殊情况覆盖默认配置 */
    hideRailOverride?: boolean
    /** 是否禁用 - true时用户无法交互，false时正常交互 */
    disabled?: boolean
}

/**
 * 通用滑动条组件
 * 支持鼠标拖拽、触摸操作、键盘控制等多种交互方式
 * 具备完整的无障碍支持和响应式布局
 */
export const UniversalSlider = ({
    value,
    setValue,
    config,
    className = '', // 默认为空字符串，避免undefined导致的className拼接问题
    hideRailOverride, // 可选的轨道隐藏覆盖设置
    disabled = false, // 默认启用交互，保证组件可用性
}: UniversalSliderProps) => {
    // ================== 配置解析和状态初始化 ==================

    /**
     * 解析滑动条配置
     * 情况1：如果config是字符串，从预设配置中获取对应配置
     * 情况2：如果config是对象，直接使用自定义配置
     * 这种设计允许用户既可以快速使用预设，也可以完全自定义
     */
    const sliderConfig = typeof config === 'string' ? SLIDER_PRESETS[config] : config

    /**
     * 从配置对象中解构出所有必要的参数
     * 使用默认值确保即使配置不完整也能正常工作
     */
    const {
        min, // 最小值，用于边界检查和百分比计算
        max, // 最大值，用于边界检查和百分比计算
        label, // 标签，用于显示和无障碍
        unit = '', // 单位，默认空字符串避免undefined
        step = 1, // 步进，默认1保证基本功能
        ariaLabel, // 无障碍标签，可选
        hideRail: configHideRail = false, // 从配置中获取hideRail设置，默认为false
    } = sliderConfig

    /**
     * 确定最终的hideRail值
     * 优先级：hideRailOverride > 配置中的hideRail > 默认值false
     */
    const hideRail = hideRailOverride !== undefined ? hideRailOverride : configHideRail

    /**
     * 拖拽状态管理
     * true: 用户正在拖拽，此时鼠标样式变为grabbing，移动时实时更新值
     * false: 用户未拖拽，鼠标样式为grab，等待用户操作
     */
    const [isDragging, setIsDragging] = useState(false)

    /**
     * 滑动条DOM引用
     * 用于获取滑动条的位置信息，计算鼠标/触摸位置对应的数值
     * 只有获取到实际DOM元素才能进行位置计算
     */
    const sliderRef = useRef<HTMLSpanElement>(null)

    // ================== 核心计算函数 ==================

    /**
     * 计算当前值在滑动条上的位置百分比
     *
     * 计算逻辑：
     * 1. 当前值为最小值时：返回0%（滑动条最左端）
     * 2. 当前值为最大值时：返回100%（滑动条最右端）
     * 3. 当前值在中间时：按比例计算百分比位置
     *
     * 公式：(当前值 - 最小值) / (最大值 - 最小值) * 100
     *
     * 示例：
     * - min=10, max=70, value=40 → (40-10)/(70-10)*100 = 50%
     * - min=75, max=400, value=100 → (100-75)/(400-75)*100 ≈ 7.69%
     */
    const getPositionPercentage = useCallback(
        (currentValue: number) => {
            // 防止除零错误：如果min等于max，返回0
            if (max === min) return 0

            // 计算当前值在范围中的相对位置
            const relativePosition = (currentValue - min) / (max - min)

            // 转换为百分比并确保在0-100范围内
            return Math.max(0, Math.min(100, relativePosition * 100))
        },
        [min, max], // 依赖最小值和最大值，当这些值改变时重新计算
    )

    /**
     * 根据鼠标/触摸位置计算对应的滑动条数值
     *
     * 计算过程：
     * 1. 获取滑动条DOM元素的位置信息
     * 2. 计算鼠标/触摸点相对于滑动条的百分比位置
     * 3. 将百分比转换为实际数值
     * 4. 根据步进值调整到最近的有效值
     * 5. 确保结果在min-max范围内
     *
     * 边界情况处理：
     * - 如果滑动条DOM不存在：返回当前值，避免错误
     * - 如果鼠标在滑动条左侧：返回最小值
     * - 如果鼠标在滑动条右侧：返回最大值
     * - 如果鼠标在滑动条上方/下方：按X坐标正常计算
     */
    const getValueFromPosition = useCallback(
        (clientX: number) => {
            // 安全检查：如果滑动条DOM引用不存在，返回当前值避免崩溃
            if (!sliderRef.current) return value

            // 获取滑动条的边界矩形信息
            const rect = sliderRef.current.getBoundingClientRect()

            // 计算鼠标相对于滑动条左边缘的百分比位置
            // (clientX - rect.left) 得到相对位置
            // 除以 rect.width 得到百分比
            const rawPercentage = ((clientX - rect.left) / rect.width) * 100

            // 限制百分比在0-100范围内
            // 情况1：鼠标在滑动条左侧时，percentage为0
            // 情况2：鼠标在滑动条右侧时，percentage为100
            // 情况3：鼠标在滑动条上时，percentage为实际百分比
            const percentage = Math.max(0, Math.min(100, rawPercentage))

            // 将百分比转换为实际数值
            // 公式：最小值 + (百分比/100) * (最大值-最小值)
            const rawValue = min + (percentage / 100) * (max - min)

            // 根据步进值调整到最近的有效值
            // 例如：step=5时，rawValue=27会调整为25，rawValue=28会调整为30
            const steppedValue = Math.round(rawValue / step) * step

            // 最终边界检查：确保结果在有效范围内
            return Math.max(min, Math.min(max, steppedValue))
        },
        [value, min, max, step], // 依赖当前值、范围和步进，变化时重新计算
    )

    // ================== 鼠标事件处理 ==================

    /**
     * 鼠标按下事件处理器
     *
     * 触发时机：用户在滑动条区域按下鼠标左键时
     *
     * 处理流程：
     * 1. 检查是否禁用状态，禁用时直接返回不处理
     * 2. 阻止默认行为，避免文本选择等干扰
     * 3. 设置拖拽状态为true，改变鼠标样式
     * 4. 根据点击位置立即更新滑动条值
     * 5. 添加全局鼠标移动和释放监听器
     * 6. 在鼠标释放时清理监听器并重置拖拽状态
     *
     * 全局监听的原因：
     * - 如果只监听滑动条元素，鼠标快速移动到外部时会失去跟踪
     * - 全局监听确保在整个屏幕范围内都能响应拖拽
     */
    const handleMouseDown = useCallback(
        (e: React.MouseEvent) => {
            // 禁用状态检查：如果组件被禁用，不响应任何鼠标操作
            if (disabled) return

            // 阻止默认行为：避免拖拽时选择文本或触发其他浏览器行为
            e.preventDefault()

            // 设置拖拽状态：改变视觉反馈，鼠标样式变为grabbing
            setIsDragging(true)

            // 立即根据点击位置更新值：提供即时反馈
            const newValue = getValueFromPosition(e.clientX)
            setValue(newValue)

            // 定义鼠标移动处理器：在拖拽过程中实时更新值
            const handleMouseMove = (e: MouseEvent) => {
                const newValue = getValueFromPosition(e.clientX)
                setValue(newValue)
            }

            // 定义鼠标释放处理器：结束拖拽并清理监听器
            const handleMouseUp = () => {
                // 重置拖拽状态：鼠标样式恢复为grab
                setIsDragging(false)
                // 移除全局监听器：避免内存泄露和意外触发
                document.removeEventListener('mousemove', handleMouseMove)
                document.removeEventListener('mouseup', handleMouseUp)
            }

            // 添加全局监听器：确保在整个屏幕范围内都能响应拖拽
            // 监听mousemove：实时跟踪鼠标位置更新值
            document.addEventListener('mousemove', handleMouseMove)
            // 监听mouseup：在任何地方释放鼠标都能结束拖拽
            document.addEventListener('mouseup', handleMouseUp)
        },
        [getValueFromPosition, setValue, disabled], // 依赖计算函数、设值函数和禁用状态
    )

    // ================== 触摸事件处理 ==================

    /**
     * 触摸开始事件处理器
     *
     * 触发时机：用户在触摸设备上开始触摸滑动条时
     *
     * 与鼠标事件的区别：
     * - 触摸事件通过e.touches[0]获取位置信息
     * - 需要设置{passive: false}阻止默认滚动行为
     * - 触摸事件名称不同：touchmove、touchend
     *
     * 处理流程与鼠标事件基本相同：
     * 1. 禁用检查 → 2. 阻止默认 → 3. 设置拖拽状态 → 4. 立即更新值 → 5. 添加全局监听
     */
    const handleTouchStart = useCallback(
        (e: React.TouchEvent) => {
            // 禁用状态检查：禁用时不响应触摸操作
            if (disabled) return

            // 阻止默认行为：特别重要，避免触摸时页面滚动
            e.preventDefault()

            // 设置拖拽状态：提供视觉反馈
            setIsDragging(true)

            // 获取第一个触摸点的位置信息
            const touch = e.touches[0]
            // 立即根据触摸位置更新值
            const newValue = getValueFromPosition(touch.clientX)
            setValue(newValue)

            // 定义触摸移动处理器：在滑动过程中实时更新值
            const handleTouchMove = (e: TouchEvent) => {
                // 阻止默认滚动行为：避免滑动条操作时页面滚动
                e.preventDefault()
                // 获取当前触摸点位置
                const touch = e.touches[0]
                const newValue = getValueFromPosition(touch.clientX)
                setValue(newValue)
            }

            // 定义触摸结束处理器：结束滑动并清理监听器
            const handleTouchEnd = () => {
                // 重置拖拽状态
                setIsDragging(false)
                // 清理全局监听器
                document.removeEventListener('touchmove', handleTouchMove)
                document.removeEventListener('touchend', handleTouchEnd)
            }

            // 添加全局触摸监听器
            // {passive: false}：允许preventDefault()工作，阻止默认滚动
            document.addEventListener('touchmove', handleTouchMove, { passive: false })
            document.addEventListener('touchend', handleTouchEnd)
        },
        [getValueFromPosition, setValue, disabled], // 依赖相同的函数和状态
    )

    // ================== 样式和位置计算 ==================

    /**
     * 计算当前值对应的位置百分比
     * 用于确定滑动条thumb（拖拽手柄）的位置
     */
    const positionPercentage = getPositionPercentage(value)

    /**
     * 判断是否在起始位置
     * 起始位置时的特殊处理：
     * - thumb位置需要特殊的像素偏移
     * - 轨道填充可能需要特殊样式
     */
    const isAtStart = value === min

    /**
     * 判断是否在结束位置
     * 结束位置时的特殊处理：
     * - thumb位置需要特殊的像素偏移
     * - 确保thumb不会超出滑动条边界
     */
    const isAtEnd = value === max

    // ================== ID和标签生成 ==================

    /**
     * 生成唯一的滑动条ID
     *
     * 生成规则：
     * 1. 将标签转换为小写
     * 2. 使用if else判断是否包含空格，进行替换处理
     * 3. 添加'-slider'后缀
     *
     * 示例：
     * - 'Opacity' → 'opacity-slider'
     * - 'Custom Label' → 'custom-label-slider'
     *
     * 用途：用于无障碍标签关联和CSS样式定位
     */
    let sliderId: string
    const lowerLabel = label.toLowerCase()
    if (lowerLabel.includes(' ')) {
        // 如果标签包含空格，将空格替换为连字符
        sliderId = lowerLabel.split(' ').join('-') + '-slider'
    } else {
        // 如果标签不包含空格，直接添加后缀
        sliderId = lowerLabel + '-slider'
    }

    /**
     * 生成标签元素的ID
     * 用于aria-labelledby属性，建立滑动条与标签的关联
     */
    const labelId = sliderId + '-label'

    /**
     * 格式化显示值
     *
     * 显示规则：
     * - 有单位时：数值+单位，如"100%"
     * - 无单位时：纯数值，如"40"
     *
     * 用于显示当前值给用户查看
     */
    const displayValue = unit ? `${value}${unit}` : `${value}`

    // ================== 轨道样式计算 ==================

    /**
     * 计算轨道填充样式
     *
     * 轨道填充逻辑：
     * - left始终为'0%'：填充从左边开始
     * - right的计算：
     *   情况1：在起始位置时，right为'100%'（完全不填充）
     *   情况2：在其他位置时，right为(100-当前百分比)%
     *
     * 视觉效果：
     * - 起始位置：无填充色
     * - 中间位置：部分填充色
     * - 结束位置：完全填充色
     */
    const railStyle = {
        left: '0%', // 填充区域从左边开始
        right: isAtStart ? '100%' : `${100 - positionPercentage}%`, // 根据位置计算右边界
    }

    // ================== 拇指位置计算 ==================

    /**
     * 计算拇指(thumb)的精确位置
     *
     * 位置计算规则：
     * 1. 最小值位置：根据hideRail设置决定偏移量
     *    - hideRail为true：calc(0% + 3.5px) - 向右偏移3.5px
     *    - hideRail为false：calc(0% + 1px) - 向右偏移1px
     * 2. 最大值位置：calc(100% - 3.5px) - 向左偏移3.5px避免超出
     * 3. 中间位置：calc(百分比% + 0px) - 按百分比精确定位
     *
     * 偏移量差异的原因：
     * - hideRail为true时，没有轨道背景，需要更大偏移避免视觉贴边
     * - hideRail为false时，有轨道背景，较小偏移即可保持视觉平衡
     * - 提供更好的视觉对齐效果和用户体验
     *
     * 使用calc()的优势：
     * - 支持百分比和像素的混合计算
     * - 浏览器会自动重新计算，响应式友好
     */
    const getThumbPosition = () => {
        if (isAtStart) {
            // 最小值位置：根据hideRail设置决定偏移量
            if (hideRail) {
                // 隐藏轨道时使用较大偏移
                return 'calc(0% + 3.5px)'
            } else {
                // 显示轨道时使用较小偏移
                return 'calc(0% + 1px)'
            }
        } else if (isAtEnd) {
            // 最大值位置：统一向左偏移，避免超出右边界
            return 'calc(100% - 3.5px)'
        } else {
            // 中间位置：按百分比精确定位，无需偏移
            return `calc(${positionPercentage}% + 0px)`
        }
    }

    /**
     * 拇指容器的样式对象
     * transform使用CSS变量实现水平居中对齐
     */
    const thumbPositionStyle = {
        transform: 'var(--radix-slider-thumb-transform)', // 使用CSS变量实现translateX(-50%)
        position: 'absolute' as const, // 绝对定位，相对于滑动条容器
        left: getThumbPosition(), // 使用计算函数确定精确位置
    }

    // ================== CSS类名组合 ==================

    /**
     * 组合所有CSS类名
     *
     * 类名组合逻辑：
     * 1. 基础类名：'slider-component'（始终存在）
     * 2. 禁用状态：禁用时为'disabled'，启用时为'false-disabled'
     * 3. 轨道隐藏：hideRail为true时添加'hide-rail'
     * 4. 起始位置：值等于最小值时添加'is-at-start'
     * 5. 自定义类名：用户传入的额外类名
     *
     * 过滤和拼接：
     * - filter(Boolean)：移除空字符串和undefined
     * - join(' ')：用空格连接所有有效类名
     */
    const containerClasses = [
        'slider-component', // 基础组件类名
        disabled ? 'true-disabled' : 'false-disabled', // 根据禁用状态选择类名
        hideRail ? 'hide-rail' : '', // 轨道隐藏状态类名
        isAtStart ? 'is-at-start' : '', // 起始位置状态类名
        className, // 用户自定义类名
    ]
        .filter(Boolean) // 过滤掉falsy值（空字符串、null、undefined等）
        .join(' ') // 用空格连接成最终的className字符串

    // ================== 组件渲染 ==================

    return (
        <form>
            {/* 滑动条主容器 */}
            <span
                ref={sliderRef} // DOM引用，用于位置计算
                dir='ltr' // 文本方向：从左到右
                data-orientation='horizontal' // 滑动条方向：水平
                aria-disabled={disabled} // 无障碍：禁用状态
                className={containerClasses} // 组合后的CSS类名
                style={
                    {
                        width: '100%', // 宽度：填满父容器
                        '--radix-slider-thumb-transform': 'translateX(-50%)', // CSS变量：拇指居中变换
                    } as React.CSSProperties
                }
                onMouseDown={handleMouseDown} // 鼠标按下事件
                onTouchStart={handleTouchStart} // 触摸开始事件
            >
                {/* 滑动条轨道容器 */}
                <span data-orientation='horizontal' className='SliderTrack track'>
                    {/* 滑动条填充区域 */}
                    <span
                        data-orientation='horizontal'
                        className='SliderRange rail'
                        style={railStyle} // 填充区域的样式
                    />
                </span>

                {/* 拇指(滑块)容器 */}
                <span style={thumbPositionStyle}>
                    {/* 可拖拽的拇指元素 */}
                    <span
                        role='slider' // 无障碍角色：滑动条
                        aria-label={ariaLabel || label} // 无障碍标签：优先使用自定义，否则使用显示标签
                        aria-valuemin={min} // 无障碍：最小值
                        aria-valuemax={max} // 无障碍：最大值
                        aria-orientation='horizontal' // 无障碍：水平方向
                        data-orientation='horizontal' // 数据属性：水平方向
                        tabIndex={disabled ? -1 : 0} // Tab索引：禁用时不可聚焦，启用时可聚焦
                        className='SliderThumb thumb' // 拇指样式类名
                        data-radix-collection-item='' // Radix UI兼容属性
                        aria-valuenow={value} // 无障碍：当前值
                        aria-labelledby={labelId} // 无障碍：关联的标签ID
                        style={{
                            // 鼠标样式：禁用时为不允许，拖拽时为抓取中，正常时为可抓取
                            cursor: disabled ? 'not-allowed' : isDragging ? 'grabbing' : 'grab',
                        }}
                    />

                    {/* 隐藏的原生input元素 */}
                    {/* 用于表单集成和辅助功能 */}
                    <input
                        value={value} // 当前值
                        onChange={e => !disabled && setValue(Number(e.target.value))} // 值变化处理：禁用时不响应
                        style={{ display: 'none' }} // 完全隐藏，仅用于数据同步
                        type='number' // 数字输入类型
                        min={min} // HTML最小值约束
                        max={max} // HTML最大值约束
                        step={step} // HTML步进约束
                        disabled={disabled} // HTML禁用状态
                    />
                </span>

                {/* 标签显示区域 */}
                <div className='labels'>
                    {/* 功能标签 */}
                    <span id={labelId}>{label}</span>
                    {/* 当前值显示 */}
                    <span>{displayValue}</span>
                </div>
            </span>
        </form>
    )
}

// ================== 向后兼容组件 ==================

/**
 * 向后兼容的原始组件
 *
 * 设计目的：
 * - 保持API向后兼容，现有代码无需修改
 * - 默认使用zoom模式和hideRail=true
 * - 简化使用，减少配置复杂度
 *
 * 内部实现：
 * - 直接调用UniversalSlider组件
 * - 固定配置为zoom模式
 * - 固定启用hideRail选项
 */
export const PublicSliderComponent = ({
    value,
    setValue,
    config,
    disabled,
}: {
    value: number
    setValue: (value: number) => void
    config: SliderType | SliderConfig
    disabled?: boolean
}) => {
    return <UniversalSlider value={value} setValue={setValue} config={config} disabled={disabled} />

    // const [opacityValue, setOpacityValue] = useState(40)
    // const [zoomValue, setZoomValue] = useState(150)
    // return (
    //     <>
    //         <UniversalSlider value={opacityValue} setValue={setOpacityValue} config="opacity" />
    //         <UniversalSlider
    //             value={zoomValue}
    //             setValue={setZoomValue}
    //             config='zoom'
    //             hideRail={true}
    //         />
    //     </>
    // )
}

// ================== 使用示例和文档 ==================

/**
 * 使用示例代码注释
 * 提供了各种使用场景的完整代码示例
 */
/*
// ================== 基础使用方式 ==================

// 1. Opacity 滑动条（不透明度控制）
const [opacity, setOpacity] = useState(40) // 初始值40，范围10-70
<UniversalSlider value={opacity} setValue={setOpacity} config="opacity" />

// 2. Zoom 滑动条（缩放控制）
const [zoom, setZoom] = useState(100) // 初始值100%，范围75%-400%
<UniversalSlider value={zoom} setValue={setZoom} config="zoom" />

// 3. 覆盖配置中的轨道显示设置（隐藏轨道）
<UniversalSlider value={zoom} setValue={setZoom} config="zoom" hideRailOverride={true} />

// 4. 完全自定义配置（音量控制示例，包含hideRail设置）
<UniversalSlider 
    value={volume} 
    setValue={setVolume} 
    config={{
        min: 0,           // 最小音量
        max: 100,         // 最大音量  
        label: '音量',     // 显示标签
        step: 1,          // 精确到1%
        unit: '%',        // 百分比单位
        hideRail: true,   // 隐藏轨道背景
        ariaLabel: 'Volume Control' // 英文无障碍标签
    }}
/>

// 5. 禁用状态（只读显示）
<UniversalSlider 
    value={readOnlyValue} 
    setValue={() => {}} 
    config="opacity"
    disabled={true}
/>

// 6. 向后兼容用法（推荐逐步迁移到新API）
<PublicSliderComponent value={zoom} setValue={setZoom} config="zoom" />

// ================== 高级用法示例 ==================

// 7. 带验证的滑动条
const [brightness, setBrightness] = useState(50)
const handleBrightnessChange = (value: number) => {
    // 自定义验证逻辑
    if (value < 10) {
        console.warn('亮度过低可能影响可读性')
    }
    setBrightness(value)
}
<UniversalSlider 
    value={brightness} 
    setValue={handleBrightnessChange} 
    config={{ min: 0, max: 100, label: '亮度', unit: '%', hideRail: false }}
/>

// 8. 条件禁用
const [contrast, setContrast] = useState(50)
const [isAutoContrast, setIsAutoContrast] = useState(false)
<UniversalSlider 
    value={contrast} 
    setValue={setContrast} 
    config={{ min: 0, max: 100, label: '对比度', unit: '%', hideRail: false }}
    disabled={isAutoContrast} // 自动模式时禁用手动调节
/>

// 9. 响应式步进值
const getStepSize = (range: number) => {
    // 范围大时使用大步进，范围小时使用小步进
    return range > 100 ? 5 : 1
}
const stepSize = getStepSize(max - min)
<UniversalSlider 
    value={value} 
    setValue={setValue} 
    config={{ min, max, label: '响应式步进', step: stepSize, hideRail: false }}
/>

// 10. 预设配置的hideRail覆盖示例
// 使用zoom预设，但临时隐藏轨道
<UniversalSlider 
    value={zoomValue} 
    setValue={setZoomValue} 
    config="zoom" 
    hideRailOverride={true} // 覆盖zoom预设中的hideRail: false
/>

// 11. 自定义预设配置示例
const customSliderPreset: SliderConfig = {
    min: 0,
    max: 255,
    label: 'RGB Value',
    step: 1,
    hideRail: true, // 在配置中直接设置hideRail
}
<UniversalSlider 
    value={redValue} 
    setValue={setRedValue} 
    config={customSliderPreset}
/>
*/
