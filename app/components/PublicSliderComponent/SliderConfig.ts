/**
 * 滑动条配置接口
 * 定义了滑动条组件所需的所有配置参数
 */
export interface SliderConfig {
    /** 滑动条的最小值 - 用户无法拖动到此值以下 */
    min: number
    /** 滑动条的最大值 - 用户无法拖动到此值以上 */
    max: number
    /** 滑动条显示的标签文字 - 会显示在滑动条下方 */
    label: string
    /** 值的单位，如 '%' - 会追加在数值后面显示 */
    unit?: string
    /** 步进值，默认为1 - 键盘操作和值计算时的最小增减单位 */
    step?: number
    /** 无障碍标签，如果与label不同的话 - 用于屏幕阅读器 */
    ariaLabel?: string
    /** 是否隐藏轨道 - true时轨道背景不可见，false时显示轨道背景，默认为false */
    hideRail?: boolean
}

/**
 * 滑动条类型枚举 - 支持多种预设模式
 * opacity: 通用不透明度模式，范围10-70，无单位
 * zoom: 缩放模式，范围75-400，单位为%
 * scene_shadow_opacity: 场景阴影不透明度，范围10-80，用于Public_FrameScene
 * mockup_shadow_opacity: 模型阴影不透明度，范围10-70，用于Public_MockupShadow
 */
export type SliderType = 'opacity' | 'zoom' | 'noise' | 'blur' | 'scene_shadow_opacity' | 'mockup_shadow_opacity'

/**
 * 预定义的滑动条配置
 * 根据不同的使用场景预设了多种配置
 */
export const SLIDER_PRESETS: Record<SliderType, SliderConfig> = {
    // 不透明度滑动条：用于控制元素透明度，值越小越透明
    opacity: {
        min: 10, // 最小值10，避免完全透明导致用户找不到元素
        max: 70, // 最大值70，避免完全不透明影响美观
        label: 'Opacity', // 显示标签为"Opacity"
        step: 1, // 步进值1，允许精确控制
        hideRail: false, // 默认显示轨道背景
    },
    // 缩放滑动条：用于控制元素缩放比例，值越大元素越大
    zoom: {
        min: 75, // 最小值75%，保证元素不会过小
        max: 400, // 最大值400%，允许放大到4倍
        label: 'Zoom', // 显示标签为"Zoom"
        unit: '%', // 单位为百分比
        step: 5, // 步进值5，减少过度敏感的调整
        hideRail: true, // 默认显示轨道背景
    },
    noise: {
        min: 0, // 最小值0，避免完全透明导致用户找不到元素
        max: 100, // 最大值100，避免完全不透明影响美观
        label: 'Noise', // 显示标签为"Noise"
        step: 1, // 步进值1，允许精确控制
        hideRail: false, // 默认显示轨道背景
    },
    blur: {
        min: 0, // 最小值0，避免完全透明导致用户找不到元素
        max: 100, // 最大值100，避免完全不透明影响美观
        label: 'Blur', // 显示标签为"Blur"
        step: 1, // 步进值1，允许精确控制
        hideRail: false, // 默认显示轨道背景
    },
    // 场景阴影不透明度：用于Public_FrameScene中的阴影控制，范围10-80
    scene_shadow_opacity: {
        min: 10, // 最小值10，避免完全透明
        max: 80, // 最大值80，支持场景阴影的更大范围
        label: 'Opacity',
        step: 1, // 步进值1，允许精确控制
        hideRail: false, // 默认显示轨道背景
    },
    // 模型阴影不透明度：用于Public_MockupShadow中的阴影控制，范围10-70
    mockup_shadow_opacity: {
        min: 10, // 最小值10，避免完全透明
        max: 70, // 最大值70，适合模型阴影的保守范围
        label: 'Opacity',
        step: 1, // 步进值1，允许精确控制
        hideRail: false, // 默认显示轨道背景
    },
}

/**
 * 获取指定类型的滑动条配置
 * @param {SliderType} type - 滑动条类型
 * @returns {SliderConfig} 对应的配置对象
 */
export const getSliderConfig = (type: SliderType): SliderConfig => {
    return SLIDER_PRESETS[type]
}

/**
 * 获取指定类型的滑动条范围值
 * @param {SliderType} type - 滑动条类型
 * @returns {{min: number, max: number}} 最小值和最大值
 */
export const getSliderRange = (type: SliderType): { min: number; max: number } => {
    const config = SLIDER_PRESETS[type]
    return {
        min: config.min,
        max: config.max,
    }
}
