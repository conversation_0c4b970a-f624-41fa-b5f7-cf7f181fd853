import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// 场景标签页类型枚举
export enum SceneTabType {
    NONE = 'none',
    SHADOW = 'shadow',
    SHAPES = 'shapes',
}

// 阴影层级类型枚举
export enum ShadowLayerType {
    UNDERLAY = 'underlay',
    OVERLAY = 'overlay',
}

// 阴影类型定义
export interface ShadowType {
    index: number
    src: string
}

// 场景状态定义
interface SceneState {
    // 当前激活的场景类型
    sceneType: SceneTabType
    // 当前选中的阴影样式
    shadowType: ShadowType
    // 阴影透明度
    shadowOpacity: number
    // 阴影渲染层级
    shadowLayer: ShadowLayerType
}

// 场景动作定义
interface SceneActions {
    // 设置场景类型
    setSceneType: (type: SceneTabType) => void
    // 设置阴影样式
    setShadowType: (shadow: ShadowType) => void
    // 设置阴影透明度
    setShadowOpacity: (opacity: number) => void
    // 设置阴影层级
    setShadowLayer: (layer: ShadowLayerType) => void
}

// 组合状态和动作类型
export type SceneStore = SceneState & SceneActions

// 创建场景状态存储
export const useSceneStore = create<SceneStore>()(
    devtools(
        set => ({
            // 初始状态
            sceneType: SceneTabType.NONE,
            shadowType: {
                index: 1,
                src: '/display-assets/shadow-overlays/051.png',
            },
            shadowOpacity: 40,
            shadowLayer: ShadowLayerType.OVERLAY,

            // 动作方法
            setSceneType: type => set({ sceneType: type }),
            setShadowType: shadow => set({ shadowType: shadow }),
            setShadowOpacity: opacity => set({ shadowOpacity: opacity }),
            setShadowLayer: layer => set({ shadowLayer: layer }),
        }),
        {
            name: 'scene-store',
        },
    ),
)
