
# Hook: useResizeImage

## 需求

创建一个React Hook，该Hook接收一个图片文件或图片URL，并使用 `pica` 库来放大图片的尺寸。

## 功能分解

1.  **输入**:
    *   `image`: 可以是 `File` 对象, `Image` 对象, 或者一个图片的 `URL`。
    *   `scaleFactor`: 一个数字，表示图片放大的倍数 (e.g., 2 for 2x)。

2.  **核心逻辑**:
    *   Hook内部会维护几个状态:
        *   `originalImage`: 原始图片。
        *   `resizedImage`: 放大后的图片 (可以是 data URL 或者 `HTMLCanvasElement`)。
        *   `isLoading`: một `boolean` 值，表示是否正在处理图片。
        *   `error`: 保存处理过程中可能发生的任何错误。
    *   当 `image` 或 `scaleFactor` 改变时，Hook会触发图片放大流程。
    *   使用 `pica` 库来执行高质量的图片缩放。
    *   将处理后的图片结果存放在 `resizedImage` 状态中。

3.  **输出**:
    *   一个对象，包含:
        *   `resizedImage`: 放大后的图片。
        *   `isLoading`: 当前是否在处理中。
        *   `error`: 错误信息。
        *   `resize`: 一个可以手动触发缩放的函数。

## 使用示例

```tsx
import React from 'react';
import useResizeImage from './useResizeImage';

const ImageResizerComponent = ({ imageUrl, scale }) => {
  const { resizedImage, isLoading, error } = useResizeImage(imageUrl, scale);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div>
      <h2>Resized Image</h2>
      {resizedImage && <img src={resizedImage} alt="Resized" />}
    </div>
  );
};
```
