/**
 * @fileoverview 用于管理粘贴上传功能的 Zustand Store (p-limit optimized)
 * @description 该 Store 集中了所有处理粘贴图片、管理上传队列以及以并发批次处理上传的核心逻辑。
 *              使用 p-limit 库来优化并发控制。
 *
 * <AUTHOR> Team (Refactored by AI)
 * @version 2.1.0
 * @since 2025.01
 */
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import pLimit from 'p-limit'
import {
    EXTENDED_IMAGE_TYPES,
    FILE_SIZE_LIMITS,
    UPLOAD_CONCURRENCY_CONFIG,
    validateImageFile,
} from '@/app/features/imageManagement/core/imageConfig'

/**
 * @enum PasteStatus
 * @description 粘贴和上传过程的生命周期状态枚举。
 *
 * @property {string} IDLE - 'idle' - 空闲状态，准备接收粘贴事件。
 * @property {string} PROCESSING - 'processing' - 正在从剪贴板提取和验证文件。
 * @property {string} UPLOADING - 'uploading' - 文件正在被积极上传。
 * @property {string} COMPLETED - 'completed' - 队列中的所有文件都已处理完毕。
 * @property {string} ERROR - 'error' - 发生了不可恢复的错误。
 *
 * 状态流转:
 * IDLE -> PROCESSING -> UPLOADING -> COMPLETED
 *                      |         |
 *                      +---------+-----> ERROR
 */
export enum PasteStatus {
    IDLE = 'idle', // 空闲状态，准备接收粘贴事件
    PROCESSING = 'processing', // 正在从剪贴板提取和验证文件
    UPLOADING = 'uploading', // 文件正在被积极上传
    COMPLETED = 'completed', // 队列中的所有文件都已处理完毕
    ERROR = 'error', // 发生了不可恢复的错误
}

/**
 * @interface PasteImageOptions
 * @description 粘贴功能的配置选项接口。
 */
export interface PasteImageOptions {
    /** 是否启用粘贴功能。 */
    enabled?: boolean
    /** 最大文件大小（字节），默认为 10MB。 */
    maxFileSize?: number
    /** 允许的图片 MIME 类型数组。 */
    allowedTypes?: string[]
    /** 最大并发上传批次数，默认为 3。 */
    maxConcurrentUploads?: number
    /** 每个上传批次包含的文件数量，默认为 5。 */
    batchSize?: number
    /** 执行文件批次上传的函数。 */
    onUpload?: (files: File[]) => Promise<void>
    /** 所有文件成功处理后的回调函数。 */
    onSuccess?: (result: { uploadedCount: number; ignoredCount: number }) => void
    /** 发生严重错误时的回调函数。 */
    onError?: (error: string) => void
    /** 发生非严重警告时的回调函数。 */
    onWarning?: (message: string) => void
    /** 单个文件批次成功上传后的回调函数。 */
    onBatchSuccess?: (files: File[]) => void
    /** 单个文件批次上传失败时的回调函数。 */
    onBatchError?: (error: any, files: File[]) => void
}

/**
 * @interface PasteUploadState
 * @description 粘贴上传 Store 的状态结构。
 */
interface PasteUploadState {
    /** 当前的状态 */
    status: PasteStatus
    /** 等待上传的文件队列 */
    queue: File[]
    /** 当前的错误信息 */
    error: string | null
    /** @deprecated 已被 p-limit 替代 */
    activeUploads: number // 将被移除
    /** 一个锁，确保同一时间只有一个队列处理任务在运行 */
    isProcessingQueue: boolean
    /** 本次粘贴事件需要处理的总文件数 */
    totalFilesToProcess: number
    /** 已处理的文件数量（无论成功或失败） */
    processedFilesCount: number
    /** 存储的配置选项 */
    options: PasteImageOptions
}

/**
 * @interface PasteUploadActions
 * @description 粘贴上传 Store 的可用操作。
 */
interface PasteUploadActions {
    /** 使用用户提供的选项初始化 Store。 */
    initialize: (options: PasteImageOptions) => void
    /** 处理粘贴事件，并将有效文件添加到队列中。 */
    handlePaste: (clipboardData: DataTransfer) => Promise<void>
    /** 处理文件队列，创建并管理并发批次。 */
    processQueue: () => void
    /** 清除任何现有的错误信息。 */
    clearError: () => void
    /** 清空整个上传队列。 */
    clearQueue: () => void
}

/**
 * @function convertDataTransferToFiles
 * @description 将来自粘贴事件的 DataTransfer 对象转换为 File 对象列表。
 * @param {DataTransfer} dataTransfer - 剪贴板数据。
 * @returns {Promise<File[]>} 一个解析为文件数组的 Promise。
 */
const convertDataTransferToFiles = async (dataTransfer: DataTransfer): Promise<File[]> => {
    const files: File[] = []
    if (dataTransfer.items && dataTransfer.items.length > 0) {
        for (const item of Array.from(dataTransfer.items)) {
            if (item.kind === 'file' && item.type.startsWith('image/')) {
                const file = item.getAsFile()
                if (file) {
                    files.push(file)
                }
            }
        }
    } else if (dataTransfer.files && dataTransfer.files.length > 0) {
        for (const file of Array.from(dataTransfer.files)) {
            if (file.type.startsWith('image/')) {
                files.push(file)
            }
        }
    }
    return files
}

/**
 * @constant usePasteUploadStore
 * @description 用于管理整个粘贴到上传工作流程的 Zustand Store。
 * 这个重构后的实现使用一个集中的 Store 来处理状态和逻辑，
 * 并利用 p-limit 库来简化和稳定并发处理，
 * 使其更加健壮、可测试，并与 React 组件的生命周期解耦。
 */
export const usePasteUploadStore = create<PasteUploadState & PasteUploadActions>()(
    devtools(
        (set, get) => ({
            // ==================== 初始状态 (Initial State) ====================
            /** @property {PasteStatus} status - 初始状态为空闲 */
            status: PasteStatus.IDLE,
            /** @property {File[]} queue - 初始上传队列为空 */
            queue: [],
            /** @property {string | null} error - 初始无错误 */
            error: null,
            /** @property {number} activeUploads - @deprecated 不再使用 */
            activeUploads: 0,
            /** @property {boolean} isProcessingQueue - 初始队列未在处理中 */
            isProcessingQueue: false,
            /** @property {number} totalFilesToProcess - 初始待处理文件总数为0 */
            totalFilesToProcess: 0,
            /** @property {number} processedFilesCount - 初始已处理文件数为0 */
            processedFilesCount: 0,
            /** @property {PasteImageOptions} options - 初始配置为空对象 */
            options: {},

            // ==================== 操作 (Actions) ====================

            /**
             * @method initialize
             * @description 初始化或更新 Store 的配置选项。
             * @param {PasteImageOptions} options - 要应用的配置。
             */
            initialize: (options: PasteImageOptions) => {
                set({ options })
            },

            /**
             * @method handlePaste
             * @description 协调整个粘贴处理流程。
             * @param {DataTransfer} clipboardData - 来自粘贴事件的数据。
             */
            handlePaste: async (clipboardData: DataTransfer) => {
                const { status, options } = get()
                const {
                    maxFileSize = FILE_SIZE_LIMITS.DEFAULT_MAX_SIZE, // 默认 10MB
                    allowedTypes = EXTENDED_IMAGE_TYPES,
                    onWarning,
                    onError,
                } = options

                // 防止在处理过程中重复触发
                if (status === PasteStatus.PROCESSING || status === PasteStatus.UPLOADING) {
                    onWarning?.('正在处理中，请稍候...')
                    return
                }

                set({ status: PasteStatus.PROCESSING, error: null })
                console.log('📋 开始处理粘贴内容...')

                try {
                    // 1. 从剪贴板提取文件
                    const extractedFiles = await convertDataTransferToFiles(clipboardData)
                    if (extractedFiles.length === 0) {
                        set({ status: PasteStatus.IDLE })
                        return // 没有找到图片文件，流程结束
                    }

                    // 2. 验证文件 - 使用统一的验证函数，包含 sonner 错误提示
                    const validFiles: File[] = []
                    let rejectedCount = 0

                    for (const file of extractedFiles) {
                        const validationResult = validateImageFile(file, {
                            allowedTypes: allowedTypes,
                            maxFileSize: maxFileSize,
                            showToast: true, // 显示 sonner 错误提示
                        })

                        if (validationResult.isValid) {
                            validFiles.push(file)
                        } else {
                            rejectedCount++
                            // 错误提示已由 validateImageFile 函数处理
                            // 这里只记录统计信息
                            console.log(
                                `📋 粘贴文件验证失败: ${file.name} - ${validationResult.errorMessage}`,
                            )
                        }
                    }

                    // 3. 发出警告（如果有被拒绝的文件）
                    if (rejectedCount > 0) {
                        onWarning?.(
                            `已拒绝 ${rejectedCount} 个不符合要求的文件（仅支持 JPG 和 PNG 格式）`,
                        )
                    }

                    // 4. 将有效文件加入队列并开始处理
                    if (validFiles.length > 0) {
                        set(state => ({
                            queue: [...state.queue, ...validFiles],
                            totalFilesToProcess: state.totalFilesToProcess + validFiles.length,
                            status: PasteStatus.UPLOADING,
                        }))
                        console.log(
                            `[Queue] 已添加 ${validFiles.length} 个文件到队列，总数: ${
                                get().queue.length
                            }`,
                        )
                        // 触发队列处理器
                        get().processQueue()
                    } else {
                        // 如果没有有效文件，则返回空闲状态
                        set({ status: PasteStatus.IDLE })
                    }
                } catch (processError) {
                    const message =
                        processError instanceof Error ? processError.message : '处理粘贴内容时出错'
                    set({ status: PasteStatus.ERROR, error: message })
                    onError?.(message)
                }
            },

            /**
             * @method processQueue
             * @description Store 的核心工作函数。它使用 p-limit 来处理队列，创建并分派批次进行上传，同时遵守并发限制。
             * 通过一个锁 (isProcessingQueue) 来确保原子性，防止并发冲突。
             */
            processQueue: async () => {
                // 检查锁，如果已在处理，则直接返回
                if (get().isProcessingQueue) {
                    return
                }

                // 检查队列是否为空，如果为空，检查是否所有任务都完成了
                if (get().queue.length === 0) {
                    // 如果所有任务都完成了，则重置状态
                    if (
                        get().totalFilesToProcess > 0 &&
                        get().totalFilesToProcess === get().processedFilesCount
                    ) {
                        console.log('[Queue] 所有上传任务完成。')
                        set({ status: PasteStatus.COMPLETED })

                        const { onSuccess } = get().options
                        onSuccess?.({
                            uploadedCount: get().processedFilesCount,
                            ignoredCount: get().totalFilesToProcess - get().processedFilesCount,
                        })

                        setTimeout(() => {
                            set({
                                status: PasteStatus.IDLE,
                                totalFilesToProcess: 0,
                                processedFilesCount: 0,
                                error: null,
                            })
                        }, 2000)
                    }
                    return
                }

                // 设置锁，表示开始处理队列
                set({ isProcessingQueue: true })

                const { options } = get()
                const {
                    onUpload,
                    maxConcurrentUploads = UPLOAD_CONCURRENCY_CONFIG.DEFAULT_MAX_CONCURRENT,
                    batchSize = UPLOAD_CONCURRENCY_CONFIG.DEFAULT_BATCH_SIZE,
                    onBatchSuccess,
                    onBatchError,
                } = options

                if (!onUpload) {
                    console.error('onUpload function is not provided.')
                    set({ isProcessingQueue: false })
                    return
                }

                // 从队列中取出当前所有文件进行处理
                const filesToProcess = [...get().queue]
                set({ queue: [] }) // 清空队列

                // 创建 p-limit 实例
                const limit = pLimit(maxConcurrentUploads)

                // 将文件分割成批次 (chunks)
                const chunks: File[][] = []
                for (let i = 0; i < filesToProcess.length; i += batchSize) {
                    chunks.push(filesToProcess.slice(i, i + batchSize))
                }

                // 为每个批次创建被 p-limit 管理的 Promise
                const uploadPromises = chunks.map(chunk =>
                    limit(() =>
                        onUpload(chunk)
                            .then(() => {
                                console.log(`[Upload] 批次成功: ${chunk.length} 个文件`)
                                onBatchSuccess?.(chunk)
                            })
                            .catch(error => {
                                console.error(`[Upload] 批次失败: ${chunk.length} 个文件`, error)
                                onBatchError?.(error, chunk)
                            })
                            .finally(() => {
                                // 更新已处理文件计数
                                set(state => ({
                                    processedFilesCount: state.processedFilesCount + chunk.length,
                                }))
                            }),
                    ),
                )

                try {
                    // 等待当前所有批次处理完成
                    await Promise.all(uploadPromises)
                } finally {
                    // 解除锁，并准备处理可能在期间新加入队列的任务
                    set({ isProcessingQueue: false })
                    // 递归调用，处理在本次执行期间可能新加入队列的任务，或结束整个流程
                    get().processQueue()
                }
            },

            /**
             * @method clearError
             * @description 重置错误状态。
             */
            clearError: () => {
                set({ error: null })
                if (get().status === PasteStatus.ERROR) {
                    set({ status: PasteStatus.IDLE })
                }
            },

            /**
             * @method clearQueue
             * @description 清空上传队列。
             */
            clearQueue: () => {
                set({
                    queue: [],
                    totalFilesToProcess: 0,
                    processedFilesCount: 0,
                    isProcessingQueue: false,
                })
                // 由于 p-limit 实例是局部的，这里不需要清理它
                set({ status: PasteStatus.IDLE })
            },
        }),
        {
            name: 'PasteUploadStore', // Redux DevTools 中显示的名称
        },
    ),
)
