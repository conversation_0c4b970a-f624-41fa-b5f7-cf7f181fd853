# useColorGenerationFromImage Hook 使用指南

## 概述

`useColorGenerationFromImage` 是一个强大的 React Hook，用于从图片中动态提取颜色并生成多种背景样式。它提供了灵活、响应式的颜色生成功能。

## 主要特性

1. **响应式更新**：当图片URL变化时自动重新生成颜色
2. **加载状态管理**：提供明确的加载和错误状态
3. **可配置参数**：支持自定义颜色数量、质量等参数
4. **更好的错误处理**：完整的错误捕获和状态管理
5. **React集成**：完全遵循React Hook规范

## 快速开始

### 1. 基本使用

```tsx
import { useColorGenerationFromImage } from '@/app/hooks/useColorGenerationFromImage'

function MyComponent() {
    const { solidColors, gradientColors, meshColors, isLoading, error } =
        useColorGenerationFromImage('/path/to/image.jpg')

    if (isLoading) return <div>加载中...</div>
    if (error) return <div>错误: {error.message}</div>

    return (
        <div>
            {solidColors.map((color, index) => (
                <div key={index} style={{ background: color.background }} />
            ))}
        </div>
    )
}
```

### 2. 带配置参数的使用

```tsx
const { solidColors, gradientColors, meshColors } = useColorGenerationFromImage(
    '/path/to/image.jpg',
    {
        colorCount: 8, // 提取8种颜色（默认5种）
        quality: 5, // 提高质量（默认10）
        crossOrigin: 'anonymous',
    },
)
```

### 3. 动态图片切换

```tsx
function DynamicColorComponent() {
    const [imageUrl, setImageUrl] = useState('/default.jpg')
    const colors = useColorGenerationFromImage(imageUrl)

    return (
        <div>
            <input
                type='text'
                value={imageUrl}
                onChange={e => setImageUrl(e.target.value)}
                placeholder='输入图片URL'
            />

            {/* 使用colors数据 */}
            {colors.solidColors.map((color, index) => (
                <div key={index} style={{ background: color.background }} />
            ))}
        </div>
    )
}
```

## 实际应用示例

### 使用自定义Hook包装

你可以创建自定义Hook来包装 `useColorGenerationFromImage` 以适应特定需求：

```tsx
// 自定义Hook示例
import { useColorData } from '@/app/MobileFrame_Magic/hooks/useColorData'

function ColorComponent() {
    const { solidColors, gradientColors, meshColors, isLoading, error } = useColorData()

    if (isLoading) return <div>动态生成颜色中...</div>
    if (error) return <div>颜色生成失败: {error.message}</div>

    return (
        <div>
            <h3>纯色背景</h3>
            {solidColors.map((color, index) => (
                <div key={index} style={{ background: color.background, width: 50, height: 50 }} />
            ))}

            <h3>渐变背景</h3>
            {gradientColors.map((color, index) => (
                <div key={index} style={{ background: color.background, width: 100, height: 50 }} />
            ))}

            <h3>网格背景</h3>
            {meshColors.map((color, index) => (
                <div
                    key={index}
                    style={{
                        background: color.backgroundImage || color.background,
                        width: 100,
                        height: 50,
                    }}
                />
            ))}
        </div>
    )
}
```

### 高级配置

```tsx
const colors = useColorGenerationFromImage(imageUrl, {
    colorCount: 10, // 提取更多颜色
    quality: 1, // 最高质量（但处理时间更长）
    crossOrigin: 'use-credentials', // 处理跨域图片
})
```

### 错误处理和重试

```tsx
function ColorPicker({ imageUrl }) {
    const [currentUrl, setCurrentUrl] = useState(imageUrl)
    const colors = useColorGenerationFromImage(currentUrl)

    const handleRetry = () => {
        // 重新设置URL会触发Hook重新执行
        setCurrentUrl(currentUrl + '?retry=' + Date.now())
    }

    if (colors.error) {
        return (
            <div>
                <p>错误: {colors.error.message}</p>
                <button onClick={handleRetry}>重试</button>
            </div>
        )
    }

    return <ColorGrid colors={colors.solidColors} />
}
```

## 注意事项

1. **图片URL必须是有效的**：确保图片URL可访问且格式正确
2. **跨域问题**：如果图片来自其他域名，确保服务器支持CORS
3. **性能考虑**：大量颜色提取可能影响性能，建议缓存结果
4. **错误处理**：始终处理可能的错误状态
5. **清理**：Hook会自动清理，无需手动处理

## 最佳实践

### 创建专门的Hook

对于特定场景，可以创建专门的Hook：

```tsx
// 用于特定图片的动态颜色生成
export function useWallpaperColors() {
    return useColorGenerationFromImage('/__壁纸测试/walller__1.jpg', {
        colorCount: 8,
        quality: 8,
    })
}

// 用于用户上传图片的颜色生成
export function useUserImageColors(imageUrl: string | null) {
    return useColorGenerationFromImage(imageUrl, {
        colorCount: 5,
        quality: 10,
    })
}
```

### 组合使用

```tsx
function ColorDashboard({ imageUrl }) {
    const colors = useColorGenerationFromImage(imageUrl)

    return (
        <div>
            <ColorSection title='纯色' colors={colors.solidColors} />
            <ColorSection title='渐变' colors={colors.gradientColors} />
            <ColorSection title='网格' colors={colors.meshColors} />
        </div>
    )
}
```
