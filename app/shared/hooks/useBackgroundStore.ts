import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

/**
 * 背景类型枚举
 * 定义所有可用的背景选项类型
 */
export enum BackgroundTypeEnum {
    /** 透明背景 */
    TRANSPARENT = 'transparent',
    /** 颜色背景 */
    COLOR = 'color',
    /** 图片背景 */
    IMAGE = 'image',
    /** Unsplash图片背景 */
    UNSPLASH = 'unsplash',
    /** 魔法背景 - 由 useMagicBackgroundStore 管理具体样式 */
    MAGIC = 'magic',
}

/**
 * Unsplash图片信息接口
 */
interface UnsplashImage {
    url: string
    author: string
    // 其他相关元数据
}

/**
 * 背景状态接口
 * 定义背景相关的所有状态
 */
interface BackgroundState {
    /**
     * 当前选中的背景类型
     * 默认为 'transparent'
     * 这是决定背景渲染方式的核心状态
     */
    backgroundType: BackgroundTypeEnum

    /**
     * 当 backgroundType 为 'color' 时，存储当前选择的颜色值
     * 默认为 '#ffffff'
     */
    color: string

    /**
     * 当 backgroundType 为 'unsplash' 时，存储 Unsplash 图片的信息
     * 此为未来扩展预留，初期可以为 null
     */
    unsplashImage: UnsplashImage | null

    /**
     * 颜色选择器是否可见
     * 用于控制颜色选择器的显示/隐藏
     */
    isColorPickerVisible: boolean

    /**
     * 图片选择器是否可见
     * 用于控制图片选择器的显示/隐藏
     */
    isImagePickerVisible: boolean
}

/**
 * 背景动作接口
 * 定义修改背景状态的所有动作
 */
interface BackgroundActions {
    /**
     * 设置背景类型
     * 这是最核心的切换函数
     * @param type - 新的背景类型
     */
    setBackgroundType: (type: BackgroundTypeEnum) => void

    /**
     * 设置背景颜色
     * 这个动作会自动将 backgroundType 设置为 'color'
     * @param newColor - 新的颜色值
     */
    setColor: (newColor: string) => void

    /**
     * 选择 Unsplash 图片
     * 这个动作会自动将 backgroundType 设置为 'unsplash'
     * @param image - Unsplash 图片对象
     */
    setUnsplashImage: (image: { url: string; author: string }) => void

    /**
     * 重置为透明背景
     * 这个动作会将 backgroundType 设置为 'transparent'
     */
    setTransparent: () => void

    /**
     * 设置为图片背景
     * 这个动作会将 backgroundType 设置为 'image'
     * 图片数据本身由 useCustomImageStore 管理
     */
    setImageBackground: () => void

    /**
     * 设置为魔法背景
     * 这个动作会将 backgroundType 设置为 'magic'
     * 具体的背景配置由 useMagicBackgroundStore 管理
     */
    setMagicBackground: () => void

    /**
     * 切换颜色选择器可见性
     * @param visible - 是否可见，不传则取反
     */
    toggleColorPicker: (visible?: boolean) => void

    /**
     * 切换图片选择器可见性
     * @param visible - 是否可见，不传则取反
     */
    toggleImagePicker: (visible?: boolean) => void
}

/**
 * 背景存储类型
 * 合并状态和动作接口
 */
type BackgroundStore = BackgroundState & BackgroundActions

/**
 * 背景状态管理器
 *
 * 使用 Zustand 创建的全局状态管理器，用于集中管理所有与背景相关的状态。
 * 包括背景类型、颜色值、Unsplash图片信息等。
 *
 * 优势：
 * 1. 状态集中化：所有背景相关的状态统一在全局管理
 * 2. 组件解耦：消除组件间的状态传递和prop drilling
 * 3. 易于扩展：新增背景类型只需修改store和枚举
 * 4. 性能优化：Zustand的选择器机制避免不必要的重渲染
 *
 * 使用示例：
 * ```typescript
 * const { backgroundType, color, setBackgroundType } = useBackgroundStore()
 * ```
 */
export const useBackgroundStore = create<BackgroundStore>()(
    devtools(
        (set, get) => ({
            // 状态
            backgroundType: BackgroundTypeEnum.MAGIC,
            color: '',
            unsplashImage: null,
            isColorPickerVisible: false,
            isImagePickerVisible: false,

            // 动作
            setBackgroundType: type => {
                set({ backgroundType: type })
            },

            setColor: newColor => {
                set({
                    color: newColor,
                    backgroundType: BackgroundTypeEnum.COLOR,
                })
            },

            setUnsplashImage: image => {
                set({
                    unsplashImage: image,
                    backgroundType: BackgroundTypeEnum.UNSPLASH,
                })
            },

            setTransparent: () => {
                set({ backgroundType: BackgroundTypeEnum.TRANSPARENT })
            },

            setImageBackground: () => {
                set({ backgroundType: BackgroundTypeEnum.IMAGE })
            },

            toggleColorPicker: visible => {
                const current = get().isColorPickerVisible
                set({ isColorPickerVisible: visible ?? !current })
            },

            toggleImagePicker: visible => {
                const current = get().isImagePickerVisible
                set({ isImagePickerVisible: visible ?? !current })
            },

            setMagicBackground: () => {
                set({ backgroundType: BackgroundTypeEnum.MAGIC })
            },
        }),
        {
            name: 'background-store',
        },
    ),
)
