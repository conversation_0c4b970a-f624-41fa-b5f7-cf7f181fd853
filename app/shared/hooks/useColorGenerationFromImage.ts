import { useState, useEffect, useCallback, useMemo } from 'react'
import { imageToColors } from '@/app/features/colorExtraction/getColor/imageToColors'
import { GradientGenerator } from '@/app/features/colorExtraction/testColor/createGradientColors'
import { MeshGenerator } from '@/app/features/colorExtraction/testColor/createMeshColors'
import type {
    IColorItem,
    IMeshItem,
    IColorExtractionResult,
    IColorGenerationOptions,
    IColorGenerationState,
} from '@/app/types/colorTypes'

// 默认配置常量
const DEFAULT_OPTIONS: Required<IColorGenerationOptions> = {
    colorCount: 5,
    quality: 10,
    crossOrigin: 'anonymous',
}

// 错误处理工具
const createError = (message: string) => new Error(`[useColorGenerationFromImage] ${message}`)

// 颜色转换工具
const rgbToBackgroundItem = (rgbColor: number[]): IColorItem => {
    const [r, g, b] = rgbColor
    return { background: `rgb(${r}, ${g}, ${b})` }
}

/**
 * 从图片URL生成多种背景样式的React Hook
 *
 * 这个Hook会根据输入的图片URL，异步提取颜色并生成：
 * - 纯色背景列表
 * - 渐变背景列表
 * - 网格背景列表
 *
 * @param imageUrl - 图片URL路径
 * @param options - 可选配置参数
 * @returns 包含颜色数据和加载状态的对象
 *
 * @example
 * ```tsx
 * const { solidColors, gradientColors, meshColors, isLoading, error } =
 *   useColorGenerationFromImage('/path/to/image.jpg')
 *
 * if (isLoading) return <div>加载中...</div>
 * if (error) return <div>错误: {error.message}</div>
 *
 * return (
 *   <div>
 *     {solidColors.map((color, index) => (
 *       <div key={index} style={{ background: color.background }} />
 *     ))}
 *   </div>
 * )
 * ```
 */
export function useColorGenerationFromImage(
    imageUrl: string | null,
    options: IColorGenerationOptions = {},
): IColorGenerationState {
    const [state, setState] = useState<IColorGenerationState>({
        solidColors: [],
        gradientColors: [],
        meshColors: [],
        sourceColors: null,
        isLoading: false,
        error: null,
    })

    // 合并配置选项 - 使用useMemo避免每次渲染都创建新对象
    const mergedOptions = useMemo(() => ({ ...DEFAULT_OPTIONS, ...options }), [options])

    // 提取颜色的异步函数
    const extractColors = useCallback(
        async (url: string): Promise<IColorExtractionResult> => {
            if (!imageToColors?.extractColorsFromUrl) {
                throw createError('imageToColors.extractColorsFromUrl 方法不存在')
            }

            const result = await imageToColors.extractColorsFromUrl(url, {
                colorCount: mergedOptions.colorCount,
                crossOrigin: mergedOptions.crossOrigin,
            })

            if (!result?.success) {
                throw createError(`无法从图片提取颜色: ${result?.error || '未知错误'}`)
            }

            return result
        },
        [mergedOptions.colorCount, mergedOptions.quality, mergedOptions.crossOrigin],
    )

    // 生成纯色背景
    const generateSolidColors = useCallback((colors: IColorExtractionResult): IColorItem[] => {
        return colors.rgbColors.map(rgbToBackgroundItem)
    }, [])

    // 生成渐变背景
    const generateGradientColors = useCallback(
        async (colors: IColorExtractionResult): Promise<IColorItem[]> => {
            try {
                const generator = new GradientGenerator(colors.hexColors)
                return await generator.generateGradients()
            } catch (error) {
                throw createError(
                    `生成渐变背景失败: ${error instanceof Error ? error.message : '未知错误'}`,
                )
            }
        },
        [],
    )

    // 生成网格背景
    const generateMeshColors = useCallback(
        async (colors: IColorExtractionResult): Promise<IMeshItem[]> => {
            try {
                const generator = new MeshGenerator(colors.hexColors)
                return await generator.generateMeshColors()
            } catch (error) {
                throw createError(
                    `生成网格背景失败: ${error instanceof Error ? error.message : '未知错误'}`,
                )
            }
        },
        [],
    )

    // 主要的数据处理逻辑
    useEffect(() => {
        // 如果没有提供图片URL，重置状态
        if (!imageUrl) {
            setState({
                solidColors: [],
                gradientColors: [],
                meshColors: [],
                sourceColors: null,
                isLoading: false,
                error: null,
            })
            return
        }

        let isCancelled = false

        const processImage = async () => {
            // 开始加载
            setState(prev => ({ ...prev, isLoading: true, error: null }))

            try {
                // 步骤1: 提取颜色
                const sourceColors = await extractColors(imageUrl)

                if (isCancelled) return

                // 步骤2: 并行生成各种背景类型
                const [solid, gradient, mesh] = await Promise.all([
                    Promise.resolve(generateSolidColors(sourceColors)),
                    generateGradientColors(sourceColors),
                    generateMeshColors(sourceColors),
                ])

                if (isCancelled) return

                // 步骤3: 更新状态
                setState({
                    solidColors: solid,
                    gradientColors: gradient,
                    meshColors: mesh,
                    sourceColors,
                    isLoading: false,
                    error: null,
                })
            } catch (error) {
                if (isCancelled) return

                console.error('颜色生成失败:', error)
                setState(prev => ({
                    ...prev,
                    solidColors: [],
                    gradientColors: [],
                    meshColors: [],
                    sourceColors: null,
                    isLoading: false,
                    error: error instanceof Error ? error : createError('未知错误'),
                }))
            }
        }

        processImage()

        // 清理函数：如果组件卸载或imageUrl变化，取消当前操作
        return () => {
            isCancelled = true
        }
    }, [imageUrl, extractColors, generateSolidColors, generateGradientColors, generateMeshColors])

    return state
}
