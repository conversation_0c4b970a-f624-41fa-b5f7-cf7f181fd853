/**
 * 应用状态钩子集合
 *
 * 这个文件将所有与应用状态相关的钩子集中在一起，
 * 方便在组件中使用，并提供一个统一的导入点。
 */

import { useEffect, useState } from 'react'
import {
    initMobileDetection,
    ModalKey,
    useAppStore,
} from '@/app/features/viewDimensions/utils/重构/状态管理'

// ==============================
// 直接状态访问 (供非React组件使用)
// ==============================

/**
 * 直接获取应用状态
 * 可以在非React组件中使用，如工具函数
 * @returns 当前完整的应用状态
 */
export const getAppState = () => useAppStore.getState()

/**
 * 直接获取当前视图尺寸
 * @returns 当前视图尺寸状态
 */
export const getViewDimensions = () => useAppStore.getState().currentViewDimensions

/**
 * 直接获取当前导出设置
 * @returns 当前导出设置状态
 */
export const getExportSettings = () => useAppStore.getState().currentExportSettings

/**
 * 直接获取UI状态
 * @returns 当前UI状态
 */
export const getUIState = () => useAppStore.getState().ui

/**
 * 直接获取弹窗状态
 * @returns 当前弹窗状态
 */
export const getModalState = () => useAppStore.getState().modal

/**
 * 直接获取指定弹窗状态
 * @param key - 弹窗的key（使用 ModalKey 枚举）
 * @returns 指定弹窗是否显示
 */
export const getModalByKey = (key: ModalKey) => {
    return useAppStore.getState().modal[key]
}

/**
 * 获取当前 DimensionSourceType
 * @returns 当前 DimensionSourceType
 */
export const getCurrentDimensionSourceType = () =>
    useAppStore.getState().currentViewDimensions.sourceType

// ==============================
// UI 状态钩子
// ==============================

/**
 * 获取UI是否处于移动设备模式
 * @returns 是否为移动设备模式
 */
export const useIsMobile = () => useAppStore(state => state.ui.isMobile)

/**
 * 获取并初始化移动设备检测
 * 此钩子会自动在组件挂载时初始化移动设备检测，并在卸载时清理
 * 使用安全的客户端检测策略，避免 SSR 和客户端不一致的问题
 *
 * @returns {{ isMobile: boolean, isInitialized: boolean }} 移动设备状态和初始化状态
 */
export const useMobileDetection = () => {
    const isMobile = useIsMobile()
    const [isInitialized, setIsInitialized] = useState(false)

    // 使用 useEffect 确保在客户端执行
    useEffect(() => {
        // 确保在客户端环境中运行
        if (typeof window === 'undefined') return

        // 立即执行移动设备检测
        const cleanup = initMobileDetection()

        // 标记初始化完成
        setIsInitialized(true)

        return () => {
            cleanup()
            setIsInitialized(false)
        }
    }, [])

    // 在初始化完成之前，返回保守的状态
    return {
        isMobile: isInitialized ? isMobile : false,
        isInitialized,
    }
}

// ==============================
// 视图尺寸钩子
// ==============================

/**
 * 获取当前视图尺寸
 */
export const useViewDimensions = () => useAppStore(state => state.currentViewDimensions)

/**
 * 获取当前视图宽度
 * @returns 当前视图宽度
 */
export const useViewWidth = () => useAppStore(state => state.currentViewDimensions.useWidth)

/**
 * 获取当前视图高度
 * @returns 当前视图高度
 */
export const useViewHeight = () => useAppStore(state => state.currentViewDimensions.useHeight)

/**
 * 获取当前视图宽高比例
 * @returns 宽高比例对象 {ratioWidth, ratioHeight}
 */
export const useViewRatio = () => {
    const { ratioWidth, ratioHeight } = useAppStore(state => state.currentViewDimensions)
    return { ratioWidth, ratioHeight }
}

/**
 * 获取视图尺寸相关的所有数据和操作
 * 返回当前尺寸状态和修改尺寸的方法
 */
export const useViewDimensionsWithActions = () => {
    const dimensions = useViewDimensions()
    const setDimensions = useAppStore(state => state.setViewDimensions)
    const resetDimensions = useAppStore(state => state.resetToDefaultDimensions)
    const calculateAndUpdate = useAppStore(state => state.calculateAndUpdateViewDimensions)
    const setWithCalculation = useAppStore(state => state.setViewDimensionsWithCalculation)

    return {
        dimensions,
        setDimensions,
        resetDimensions,
        calculateAndUpdate,
        setWithCalculation,
    }
}

/**
 * 获取计算后的视图尺寸信息
 * @returns 计算后的尺寸和方法信息
 */
export const useCalculatedDimensions = () => {
    const { calculatedWidth, calculatedHeight, calculationMethod } = useViewDimensions()
    return {
        calculatedWidth,
        calculatedHeight,
        calculationMethod,
    }
}

/**
 * 获取计算相关的操作方法
 * @returns 计算相关的操作函数
 */
export const useCalculationActions = () => {
    const calculateAndUpdate = useAppStore(state => state.calculateAndUpdateViewDimensions)
    const setWithCalculation = useAppStore(state => state.setViewDimensionsWithCalculation)

    return {
        calculateAndUpdate,
        setWithCalculation,
    }
}

/**
 * 检查当前画布尺寸是否为默认来源，并且其宽度和高度与传入的参数匹配
 * @returns 用于检查尺寸是否匹配的函数
 */
export const useIsDefaultDimensionsAndMatches = () =>
    useAppStore(state => state.isDefaultDimensionsAndMatches)

/**
 * 获取设置视图尺寸的操作方法
 * @returns 设置视图尺寸的函数
 */
export const useSetViewDimensions = () => useAppStore(state => state.setViewDimensions)

/**
 * 获取重置为默认尺寸的操作方法
 * @returns 重置为默认尺寸的函数
 */
export const useResetToDefaultDimensions = () =>
    useAppStore(state => state.resetToDefaultDimensions)

// ==============================
// 弹窗状态钩子
// ==============================

/**
 * 获取当前弹窗状态
 * @returns 当前弹窗状态对象
 */
export const useModalState = () => useAppStore(state => state.modal)

/**
 * 获取指定弹窗状态
 * @param key - 弹窗的key（使用 ModalKey 枚举）
 * @returns 指定弹窗是否显示
 */
export const useModal = (key: ModalKey) => {
    return useAppStore(state => state.modal[key])
}

/**
 * 获取弹窗管理的核心操作方法
 * @returns 弹窗管理的核心方法对象
 */
export const useModalActions = () => {
    const setModal = useAppStore(state => state.setModal)
    const toggleModal = useAppStore(state => state.toggleModal)
    const setModalState = useAppStore(state => state.setModalState)
    const resetModalState = useAppStore(state => state.resetModalState)

    return {
        setModal, // setModal(ModalKey.ExportPopover, true)
        toggleModal, // toggleModal(ModalKey.ExportPopover)
        setModalState, // setModalState({ [ModalKey.ExportPopover]: true, [ModalKey.SuccessPopover]: false })
        resetModalState, // resetModalState()
    }
}

/**
 * 获取弹窗相关的所有数据和操作（完整版本）
 * @returns 完整的弹窗状态和操作方法
 */
export const useModalStateWithActions = () => {
    const modalState = useModalState()
    const actions = useModalActions()

    return {
        modalState,
        ...actions,
    }
}

// ==============================
// 导出设置钩子
// ==============================

/**
 * 获取当前导出设置
 */
export const useExportSettings = () => {
    const currentExportSettings = useAppStore(state => state.currentExportSettings)
    const setExportFormat = useAppStore(state => state.setExportFormat)
    const setExportQuality = useAppStore(state => state.setExportQuality)
    const setFullExportSettings = useAppStore(state => state.setFullExportSettings)

    return {
        currentExportSettings,
        setExportFormat,
        setExportQuality,
        setFullExportSettings,
    }
}

/**
 * 获取当前导出格式
 */
export const useExportFormat = () => useAppStore(state => state.currentExportSettings.format)

/**
 * 获取当前导出质量
 */
export const useExportQuality = () => useAppStore(state => state.currentExportSettings.quality)

/**
 * 获取设置导出格式的操作方法
 * @returns 设置导出格式的函数
 */
export const useSetExportFormat = () => useAppStore(state => state.setExportFormat)

/**
 * 获取设置导出质量的操作方法
 * @returns 设置导出质量的函数
 */
export const useSetExportQuality = () => useAppStore(state => state.setExportQuality)

/**
 * 获取导出设置相关的所有数据和操作
 * 返回当前导出设置和修改设置的方法
 */
export const useExportSettingsWithActions = () => {
    const settings = useExportSettings()
    const setFormat = useAppStore(state => state.setExportFormat)
    const setQuality = useAppStore(state => state.setExportQuality)
    const setFullSettings = useAppStore(state => state.setFullExportSettings)

    return {
        settings,
        setFormat,
        setQuality,
        setFullSettings,
    }
}

// ==============================
// 使用示例和最佳实践
// ==============================

/*
使用 ModalKey enum 的完整示例：

import { 
    useModalStateWithActions, 
    useModal, 
    ModalKey 
} from '@/hooks/useAppState'

function MyComponent() {
    // 方式1：获取完整的状态和操作
    const { modalState, setModal, toggleModal, resetModalState } = useModalStateWithActions()

    // 方式2：只获取特定弹窗状态（推荐方式）
    const isExportOpen = useModal(ModalKey.ExportPopover)
    const isSuccessOpen = useModal(ModalKey.SuccessPopover)

    // 事件处理函数
    const handleShowExport = () => {
        setModal(ModalKey.ExportPopover, true)
    }

    const handleToggleSuccess = () => {
        toggleModal(ModalKey.SuccessPopover)
    }

    const handleExportComplete = () => {
        // 隐藏导出弹窗，显示成功弹窗
        setModal(ModalKey.ExportPopover, false)
        setModal(ModalKey.SuccessPopover, true)
    }

    const handleResetAll = () => {
        resetModalState()
    }

    return (
        <div>
            {isExportOpen && <ExportPopover />}
            {isSuccessOpen && <SuccessPopover />}
            
            <button onClick={handleShowExport}>显示导出弹窗</button>
            <button onClick={handleToggleSuccess}>切换成功弹窗</button>
            <button onClick={handleExportComplete}>导出完成</button>
            <button onClick={handleResetAll}>重置所有弹窗</button>
        </div>
    )
}

// 在非React组件中使用：
import { getModalByKey, ModalKey } from '@/hooks/useAppState'

function someUtilFunction() {
    const isExportOpen = getModalByKey(ModalKey.ExportPopover)
    if (isExportOpen) {
        console.log('导出弹窗当前是打开状态')
    }
}

// 添加新弹窗的步骤：
// 1. 在 ModalKey enum 中添加新的键值：
//    enum ModalKey {
//        ExportPopover = 'exportPopover',
//        SuccessPopover = 'successPopover',
//        ConfirmDeletePopover = 'confirmDeletePopover',  // ✅ 新增
//    }
//
// 2. 在 ModalState 接口中添加对应字段：
//    interface ModalState {
//        exportPopover: boolean
//        successPopover: boolean
//        confirmDeletePopover: boolean  // ✅ 新增
//    }
//
// 3. 在 resetModalState 中添加默认值：
//    modal: {
//        exportPopover: true,
//        successPopover: false,
//        confirmDeletePopover: false,  // ✅ 新增默认值
//    }
//
// 4. 立即可以使用：
//    const isConfirmDeleteOpen = useModal(ModalKey.ConfirmDeletePopover)
//    setModal(ModalKey.ConfirmDeletePopover, true)
//    toggleModal(ModalKey.ConfirmDeletePopover)
*/
