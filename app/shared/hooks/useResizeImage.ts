import { useState, useEffect, useCallback } from 'react'
import { loadImage } from '../utils/imageLoader'
import { resizeImage, canResizeImage } from '@/app/shared/services/imageResizeService'

/**
 * 图片缩放Hook配置选项
 *
 * @property scaleFactor - 缩放因子，1.0表示原尺寸，2.0表示放大一倍，0.5表示缩小一半
 * @property quality - 质量等级，0-3，3为最高质量，默认3
 * @property unsharpAmount - 锐化强度，范围0-200，默认80
 * @property unsharpRadius - 锐化半径，范围0-2，默认0.6
 * @property unsharpThreshold - 锐化阈值，范围0-255，默认2
 *
 * @example
 * ```typescript
 * const options = {
 *   scaleFactor: 1.5,    // 放大到1.5倍
 *   quality: 3,          // 最高质量
 *   unsharpAmount: 100   // 强锐化
 * }
 * ```
 */
interface UseResizeImageOptions {
    scaleFactor: number
    quality?: number
    unsharpAmount?: number
    unsharpRadius?: number
    unsharpThreshold?: number
}

/**
 * Hook返回值接口
 *
 * @property resizedImage - 缩放后的图片data URL，初始为null
 * @property originalImage - 原始图片HTML元素，加载完成后可用
 * @property isLoading - 加载状态，true表示正在处理
 * @property error - 错误信息，处理失败时包含错误详情
 * @property resize - 手动触发缩放的函数，可用于重新处理
 *
 * @example
 * ```typescript
 * const { resizedImage, isLoading, error, resize } = useResizeImage(file, options)
 *
 * if (isLoading) return <div>处理中...</div>
 * if (error) return <div>错误: {error.message}</div>
 * if (resizedImage) return <img src={resizedImage} alt="处理后图片" />
 * ```
 */
interface UseResizeImageReturn {
    resizedImage: string | null
    originalImage: HTMLImageElement | null
    isLoading: boolean
    error: Error | null
    resize: () => Promise<void>
}

/**
 * 使用Pica库进行高质量图片缩放的React Hook
 *
 * 功能特性：
 * - 🎯 支持多种输入格式：File对象、图片URL、HTMLImageElement
 * - 🔧 使用Pica库提供专业级图片缩放质量
 * - ⚡ 自动处理图片加载和缩放流程
 * - 🎨 支持锐化、质量调整等高级选项
 * - 🔄 提供手动重新缩放能力
 * - 📱 响应式状态管理
 *
 * 使用场景：
 * - 图片预览放大查看细节
 * - 生成不同尺寸的缩略图
 * - 图片编辑工具的缩放功能
 * - 批量图片处理流水线
 *
 * 性能优化：
 * - 使用单例模式避免重复创建Pica实例
 * - 自动清理内存，避免内存泄漏
 * - 防抖处理，避免频繁计算
 *
 * @param image - 图片源，支持File对象、图片URL字符串、HTMLImageElement
 * @param options - 缩放配置选项
 * @returns 包含缩放结果、状态和操作函数的对象
 *
 * @example
 * ```typescript
 * // 基本用法
 * const { resizedImage, isLoading, error } = useResizeImage(
 *   'https://example.com/photo.jpg',
 *   { scaleFactor: 2.0 }
 * )
 *
 * // 高级用法
 * const { resizedImage, resize } = useResizeImage(
 *   selectedFile,
 *   {
 *     scaleFactor: 1.5,
 *     quality: 3,
 *     unsharpAmount: 120,
 *     unsharpRadius: 0.8,
 *     unsharpThreshold: 1
 *   }
 * )
 *
 * // 手动触发重新缩放
 * const handleReprocess = async () => {
 *   await resize()
 * }
 * ```
 */
const useResizeImage = (
    image: File | HTMLImageElement | string | null,
    options: UseResizeImageOptions,
): UseResizeImageReturn => {
    const {
        scaleFactor,
        quality = 3,
        unsharpAmount = 80,
        unsharpRadius = 0.6,
        unsharpThreshold = 2,
    } = options

    // 状态管理
    const [resizedImage, setResizedImage] = useState<string | null>(null)
    const [originalImage, setOriginalImage] = useState<HTMLImageElement | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<Error | null>(null)

    /**
     * 执行图片缩放的异步函数
     * 封装了完整的缩放流程：验证 → 处理 → 结果处理
     *
     * 流程说明：
     * 1. 验证输入参数合法性
     * 2. 设置加载状态
     * 3. 调用resizeImage服务进行缩放
     * 4. 处理成功/失败结果
     *
     * @example
     * ```typescript
     * try {
     *   await performResize()
     *   console.log("缩放完成，图片已更新")
     * } catch (error) {
     *   console.error("缩放失败:", error)
     * }
     * ```
     */
    const performResize = useCallback(async () => {
        // 验证是否可以进行缩放
        if (!originalImage || !canResizeImage(originalImage, scaleFactor)) {
            console.warn('跳过缩放：参数无效或图片未加载')
            return
        }

        setIsLoading(true)
        setError(null)

        try {
            // 调用专业的图片缩放服务
            const dataUrl = await resizeImage(originalImage, {
                scaleFactor,
                quality,
                unsharpAmount,
                unsharpRadius,
                unsharpThreshold,
            })

            // 更新缩放结果
            setResizedImage(dataUrl)
        } catch (err) {
            // 统一错误处理
            const error = err instanceof Error ? err : new Error('图片缩放过程中发生未知错误')
            console.error('图片缩放失败:', error.message)
            setError(error)
            setResizedImage(null)
        } finally {
            // 无论成功失败都结束加载状态
            setIsLoading(false)
        }
    }, [originalImage, scaleFactor, quality, unsharpAmount, unsharpRadius, unsharpThreshold])

    /**
     * 加载并设置原始图片
     * 处理图片源的异步加载流程
     *
     * 工作流程：
     * 1. 清空之前的状态（当image为null时）
     * 2. 设置加载状态
     * 3. 使用loadImage工具加载图片
     * 4. 更新原始图片状态
     *
     * @example
     * ```typescript
     * // 当image参数变化时自动触发
     * useEffect(() => {
     *   loadAndSetImage()
     * }, [image])
     * ```
     */
    const loadAndSetImage = useCallback(async () => {
        // 清空状态（当传入null时）
        if (!image) {
            setOriginalImage(null)
            setResizedImage(null)
            setError(null)
            return
        }

        setIsLoading(true)
        setError(null)

        try {
            console.log('开始加载图片资源...')

            // 使用专门的图片加载工具
            const loadedImage = await loadImage(image)

            console.log('图片加载成功:', {
                原始宽度: loadedImage.naturalWidth,
                原始高度: loadedImage.naturalHeight,
                图片来源: loadedImage.src.substring(0, 50) + '...',
            })

            // 更新原始图片状态，触发自动缩放
            setOriginalImage(loadedImage)
        } catch (err) {
            // 统一的错误处理
            const error = err instanceof Error ? err : new Error('图片加载失败')
            console.error('图片加载错误:', error.message)
            setError(error)

            // 重置相关状态
            setOriginalImage(null)
            setResizedImage(null)
        } finally {
            setIsLoading(false)
        }
    }, [image])

    // 监听图片源变化，自动重新加载
    useEffect(() => {
        loadAndSetImage()
    }, [loadAndSetImage])

    // 监听原始图片或缩放参数变化，自动重新缩放
    useEffect(() => {
        performResize()
    }, [performResize])

    /**
     * 手动触发重新缩放的函数
     * 用于用户手动点击"重新处理"按钮的场景
     *
     * @example
     * ```typescript
     * const handleManualResize = async () => {
     *   try {
     *     await resize()
     *     toast.success("图片已重新处理")
     *   } catch (error) {
     *     toast.error("处理失败，请重试")
     *   }
     * }
     * ```
     */
    const resize = useCallback(async () => {
        await performResize()
    }, [performResize])

    // 资源清理：组件卸载时不需要特殊处理
    // Data URL是内联数据，不需要手动释放
    // 图片元素由浏览器自动管理

    return {
        resizedImage,
        originalImage,
        isLoading,
        error,
        resize,
    }
}

export default useResizeImage
