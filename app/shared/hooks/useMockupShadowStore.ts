import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// 1. 创建并导出 ShadowTypeEnum 枚举
export enum ShadowTypeEnum {
    None = 'None',
    Spread = 'Spread',
    Hug = 'Hug',
    Adaptive = 'Adaptive',
}

// 定义单个阴影样式对象的接口
export interface ShadowStyle {
    src: string
    label: ShadowTypeEnum // 使用枚举替代 string
}

// 定义阴影状态的接口
interface MockupShadowState {
    shadowStyles: ShadowStyle[]
    shadowType: ShadowTypeEnum // 使用枚举替代 string
    opacity: number
    position: { x: number; y: number }
}

// 定义阴影操作的接口
interface MockupShadowActions {
    setShadowType: (type: ShadowTypeEnum) => void // Action 现在接受枚举类型
    setOpacity: (opacity: number) => void
    setPosition: (position: { x: number; y: number }) => void
}

// 合并状态和操作的类型
type MockupShadowStore = MockupShadowState & MockupShadowActions

// 创建阴影状态管理的 zustand store
export const useMockupShadowStore = create<MockupShadowStore>()(
    devtools(
        set => ({
            // 2. 更新 store 中的数据以使用枚举
            shadowStyles: [
                {
                    src: '/image/shadow-modes/shadow-none.jpeg',
                    label: ShadowTypeEnum.None,
                },
                {
                    src: '/image/shadow-modes/shadow-spread.jpeg',
                    label: ShadowTypeEnum.Spread,
                },
                {
                    src: '/image/shadow-modes/shadow-hug.jpeg',
                    label: ShadowTypeEnum.Hug,
                },
                {
                    src: '/image/shadow-modes/shadow-adaptive.jpeg',
                    label: ShadowTypeEnum.Adaptive,
                },
            ],
            // 初始状态也使用枚举
            shadowType: ShadowTypeEnum.Hug,
            opacity: 70,
            position: { x: 48, y: 24 },

            // 3. 更新 action 以接受枚举
            setShadowType: (type: ShadowTypeEnum) => {
                set({ shadowType: type }, false, 'setShadowType')
            },

            // 设置不透明度的 action
            setOpacity: (opacity: number) => {
                set({ opacity }, false, 'setOpacity')
            },

            // 设置光源位置的 action
            setPosition: (position: { x: number; y: number }) => {
                set({ position }, false, 'setPosition')
            },
        }),
        {
            name: 'mockup-shadow-store',
        },
    ),
)
