import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// 1. 创建并导出 MockupStyleEnum 枚举
export enum MockupStyleEnum {
    Black = 'black',
    Blue = 'blue',
    Green = 'green',
    Pink = 'pink',
    Yellow = 'yellow',
    Display = 'display',
}

// 样式项接口定义
export interface MockupStyleItem {
    label: MockupStyleEnum
    src: string
    deviceSrc: string
}

// Mockup状态接口定义
interface MockupState {
    // 所有可选的样式列表
    styles: MockupStyleItem[]
    // 当前激活的样式标签
    activeStyle: MockupStyleEnum
}

// Mockup动作接口定义
interface MockupActions {
    // 设置当前激活的样式
    setActiveStyle: (style: MockupStyleEnum) => void
}

// 组合状态和动作类型
export type MockupStore = MockupState & MockupActions

// 创建mockup样式存储
export const useMockupStore = create<MockupStore>()(
    devtools(
        set => ({
            // 2. 更新 store 中的数据以使用枚举
            styles: [
                {
                    label: MockupStyleEnum.Black,
                    src: '/mockups/iPhone 15 Plus/styles/black.png',
                    deviceSrc: '/mockups/iPhone 15 Plus/portrait/black.png',
                },
                {
                    label: MockupStyleEnum.Blue,
                    src: '/mockups/iPhone 15 Plus/styles/blue.png',
                    deviceSrc: '/mockups/iPhone 15 Plus/portrait/blue.png',
                },
                {
                    label: MockupStyleEnum.Green,
                    src: '/mockups/iPhone 15 Plus/styles/green.png',
                    deviceSrc: '/mockups/iPhone 15 Plus/portrait/green.png',
                },
                {
                    label: MockupStyleEnum.Pink,
                    src: '/mockups/iPhone 15 Plus/styles/pink.png',
                    deviceSrc: '/mockups/iPhone 15 Plus/portrait/pink.png',
                },
                {
                    label: MockupStyleEnum.Yellow,
                    src: '/mockups/iPhone 15 Plus/styles/yellow.png',
                    deviceSrc: '/mockups/iPhone 15 Plus/portrait/yellow.png',
                },
                {
                    label: MockupStyleEnum.Display,
                    src: '/mockups/iPhone 15 Plus/styles/display.png',
                    deviceSrc: '/mockups/iPhone 15 Plus/portrait/display.png',
                },
            ],
            // 初始状态也使用枚举
            activeStyle: MockupStyleEnum.Black,

            // 3. 更新 action 以接受枚举
            setActiveStyle: style => set({ activeStyle: style }, false, 'setActiveStyle'),
        }),
        {
            name: 'mockup-store',
        },
    ),
)

// 便捷选择器：获取当前激活的完整样式对象
export const getActiveStyle = (state: MockupStore): MockupStyleItem | undefined => {
    return state.styles.find(style => style.label === state.activeStyle)
}
