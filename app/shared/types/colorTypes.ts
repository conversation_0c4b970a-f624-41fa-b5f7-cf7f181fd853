// 背景类型枚举
export enum BackgroundType {
    SOLID = 'solid',
    GRADIENT = 'gradient',
    MESH = 'mesh',
    IMAGE = 'image',
}

// 基础颜色项目接口
export interface IColorItem {
    background: string
    showPlusBadge?: boolean
}

// 网格颜色项目接口
export interface IMeshItem extends IColorItem {
    className: string
    backgroundColor: string
    backgroundImage: string
}

// 颜色提取结果接口
export interface IColorExtractionResult {
    success: boolean
    rgbColors: number[][]
    hexColors: string[]
    message?: string
}

// Hook 配置选项接口
export interface IColorGenerationOptions {
    colorCount?: number
    quality?: number
    crossOrigin?: string
}

// Hook 返回状态接口
export interface IColorGenerationState {
    solidColors: IColorItem[]
    gradientColors: IColorItem[]
    meshColors: IMeshItem[]
    sourceColors: IColorExtractionResult | null
    isLoading: boolean
    error: Error | null
}