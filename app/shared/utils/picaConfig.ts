/**
 * Pica图片处理库配置模块
 * 
 * 功能：提供Pica实例的创建和配置，避免重复实例化
 * 特点：单例模式、Web Worker优化、跨平台兼容
 */

import * as pica from 'pica'

/**
 * Pica实例单例存储
 * 避免在多次调用时重复创建实例，提升性能
 * 
 * @example
 * ```typescript
 * // 第一次调用创建实例
 * const pica1 = getPicaInstance() 
 * // 第二次调用返回相同实例
 * const pica2 = getPicaInstance()
 * console.log(pica1 === pica2) // true
 * ```
 */
let picaInstance: pica.Pica | null = null

/**
 * 创建并配置Pica实例
 * 
 * 配置说明：
 * - features: ['js', 'wasm'] 禁用Web Worker，避免开发服务器问题
 * - createCanvas: 自定义canvas创建函数，确保跨浏览器兼容
 * 
 * @example
 * ```typescript
 * const pica = createPicaInstance()
 * // 现在可以使用pica进行图片处理
 * ```
 */
const createPicaInstance = (): pica.Pica => {
    return new pica({
        // 配置特性支持，禁用Web Worker避免开发环境问题
        features: ['js', 'wasm'],
        
        // 自定义canvas创建函数
        createCanvas: (width: number, height: number): HTMLCanvasElement => {
            const canvas = document.createElement('canvas')
            canvas.width = width
            canvas.height = height
            
            // 返回标准HTML5 Canvas元素
            return canvas
        },
    })
}

/**
 * 获取Pica实例的单例访问点
 * 使用延迟初始化模式，只在第一次调用时创建实例
 * 
 * @returns pica.Pica - 配置好的Pica实例
 * 
 * @example
 * ```typescript
 * // 在任何地方获取相同的Pica实例
 * const pica = getPicaInstance()
 * const resized = await pica.resize(sourceCanvas, targetCanvas)
 * ```
 */
export const getPicaInstance = (): pica.Pica => {
    if (!picaInstance) {
        picaInstance = createPicaInstance()
    }
    
    return picaInstance
}

/**
 * 清理Pica实例（用于测试或内存管理）
 * 主要在单元测试或需要释放内存时调用
 * 
 * @example
 * ```typescript
 * // 测试完成后清理
 * afterEach(() => {
 *   cleanupPicaInstance()
 * })
 * ```
 */
export const cleanupPicaInstance = (): void => {
    picaInstance = null
}