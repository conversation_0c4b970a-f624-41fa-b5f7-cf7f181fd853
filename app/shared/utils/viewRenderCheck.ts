import React from 'react'
import { useIsMobile } from '../hooks/useAppState'

// 桌面渲染组件
const RenderDesktopView = ({ children }: { children: React.ReactNode }) => {
    // 使用统一的移动设备检测钩子
    const isMobile = useIsMobile()

    // 如果不是移动设备，则渲染内容
    return !isMobile ? children : null
}

// 移动端渲染组件
const RenderMobileView = ({ children }: { children: React.ReactNode }) => {
    // 使用统一的移动设备检测钩子
    const isMobile = useIsMobile()

    // 如果是移动设备，则渲染内容
    return isMobile ? children : null
}

export { RenderDesktopView, RenderMobileView }
