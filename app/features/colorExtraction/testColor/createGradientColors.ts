import chroma from 'chroma-js'

/**
 * @description 颜色项接口定义
 */
export interface IColorItem {
    background: string
    showPlusBadge?: boolean
}

/**
 * @description 颜色渐变生成器类
 * @description 基于chroma.js库实现逆向工程的颜色渐变生成系统
 */
export class GradientGenerator {
    private palette: string[]

    /**
     * @description 构造函数
     * @param {string[]} palette - 5个基础RGB颜色字符串数组
     */
    constructor(palette: string[]) {
        this.palette = palette
    }

    /**
     * @description 判断颜色是否为中性色（黑、白、灰）
     * @param {string} color - RGB颜色字符串
     * @returns {boolean} 是否为中性色
     */
    private isNeutralColor(color: string): boolean {
        try {
            const chromaColor = chroma(color)
            const hsl = chromaColor.hsl()
            const lightness = hsl[2]
            const saturation = hsl[1]

            // 如果饱和度很低且亮度很高或很低，则认为是中性色
            if (isNaN(saturation) || saturation < 0.15) {
                return true
            }

            // 检查是否接近黑色或白色
            if (lightness < 0.1 || lightness > 0.9) {
                return true
            }

            return false
        } catch (error) {
            console.error('Error checking neutral color:', error)
            return true
        }
    }

    /**
     * @description 判断颜色是否具有足够的饱和度（用于聚光灯效果）
     * @param {string} color - RGB颜色字符串
     * @returns {boolean} 是否具有足够饱和度
     */
    private hasHighSaturation(color: string): boolean {
        try {
            const chromaColor = chroma(color)
            const hsl = chromaColor.hsl()
            const saturation = hsl[1]
            const lightness = hsl[2]

            // 饱和度 > 0.3 且亮度适中的颜色适合做聚光灯效果
            return !isNaN(saturation) && saturation > 0.3 && lightness > 0.2 && lightness < 0.8
        } catch (error) {
            console.error('Error checking saturation:', error)
            return false
        }
    }

    /**
     * @description 创建单色色阶渐变（线性）
     * @param {string} baseColor - 基础颜色
     * @returns {string} CSS线性渐变字符串
     */
    private createLinearScaleGradient(baseColor: string): string {
        try {
            const chromaColor = chroma(baseColor)

            // 创建一个从基础色到更亮、更饱和版本的色阶
            const colors = chroma
                .scale([
                    chromaColor.darken(0.2),
                    chromaColor,
                    chromaColor.brighten(0.5).saturate(0.3),
                    chromaColor.brighten(1).saturate(0.5),
                    chromaColor.brighten(1.5).saturate(0.7),
                    chromaColor.brighten(2).saturate(0.8),
                ])
                .mode('rgb')
                .colors(6)

            return `linear-gradient(to top, ${colors[0]} 0%, ${colors[1]} 35%, ${colors[2]} 70%, ${colors[3]} 105%, ${colors[4]} 140%, ${colors[5]} 175%)`
        } catch (error) {
            console.error('Error creating linear scale gradient:', error)
            return `linear-gradient(to top, ${baseColor} 0%, ${baseColor} 100%)`
        }
    }

    /**
     * @description 创建单色色阶渐变（径向）
     * @param {string} baseColor - 基础颜色
     * @returns {string} CSS径向渐变字符串
     */
    private createRadialScaleGradient(baseColor: string): string {
        try {
            const chromaColor = chroma(baseColor)

            // 创建径向渐变的色阶
            const colors = chroma
                .scale([
                    chromaColor.darken(0.2),
                    chromaColor.brighten(0.5).saturate(0.3),
                    chromaColor.brighten(1).saturate(0.5),
                    chromaColor.brighten(1.5).saturate(0.7),
                    chromaColor.brighten(2).saturate(0.8),
                ])
                .mode('rgb')
                .colors(5)

            return `radial-gradient(circle at 50% 115%, ${colors[0]} 0%, ${colors[1]} 15%, ${colors[2]} 30%, ${colors[3]} 45%, ${colors[4]} 60%)`
        } catch (error) {
            console.error('Error creating radial scale gradient:', error)
            return `radial-gradient(circle at 50% 115%, ${baseColor} 0%, ${baseColor} 100%)`
        }
    }

    /**
     * @description 创建双色组合渐变
     * @param {string} color1 - 第一个颜色
     * @param {string} color2 - 第二个颜色
     * @param {number} angle - 渐变角度，默认140度
     * @returns {string} CSS线性渐变字符串
     */
    private createComboGradient(color1: string, color2: string, angle: number = 140): string {
        return `linear-gradient(${angle}deg, ${color1} 25%, ${color2} 90%)`
    }

    /**
     * @description 创建聚光灯效果渐变
     * @param {string} baseColor - 基础颜色
     * @returns {string} CSS径向渐变字符串
     */
    private createSpotlightGradient(baseColor: string): string {
        try {
            const chromaColor = chroma(baseColor)

            // 创建更亮、更饱和的版本作为聚光灯中心
            const brightColor = chromaColor.brighten(0.8).saturate(0.5)
            const mediumColor = chromaColor.brighten(0.3).saturate(0.3)

            return `radial-gradient(circle at 50% 100%, ${brightColor.hex()} 5%, ${mediumColor.hex()} 20%, black 70%)`
        } catch (error) {
            console.error('Error creating spotlight gradient:', error)
            return `radial-gradient(circle at 50% 100%, ${baseColor} 5%, ${baseColor} 20%, black 70%)`
        }
    }

    /**
     * @description 主要的渐变生成函数
     * @returns {IColorItem[]} 生成的渐变颜色数组
     */
    public generateGradients(): IColorItem[] {
        const gradients: IColorItem[] = []

        // 1. 单色色阶模板 - 为非中性色生成线性和径向渐变
        this.palette.forEach((color, index) => {
            if (!this.isNeutralColor(color)) {
                // 线性渐变
                gradients.push({
                    background: this.createLinearScaleGradient(color),
                    showPlusBadge: index === 0 ? false : true, // 第一个不显示Plus徽章
                })

                // 径向渐变
                gradients.push({
                    background: this.createRadialScaleGradient(color),
                    showPlusBadge: true,
                })
            }
        })

        // 2. 双色组合模板 - 固定生成4个双色组合
        const comboPairs = [
            [this.palette[0], this.palette[4]], // 第1色 + 第5色
            [this.palette[1], this.palette[3]], // 第2色 + 第4色
            [this.palette[2], this.palette[1]], // 第3色 + 第2色
            [this.palette[4], this.palette[0]], // 第5色 + 第1色
        ]

        comboPairs.forEach(([color1, color2]) => {
            gradients.push({
                background: this.createComboGradient(color1, color2, 140),
                showPlusBadge: true,
            })
        })

        // 再生成一组144度角的组合
        gradients.push({
            background: this.createComboGradient(this.palette[0], this.palette[2], 144),
            showPlusBadge: true,
        })

        gradients.push({
            background: this.createComboGradient(this.palette[1], this.palette[4], 144),
            showPlusBadge: true,
        })

        // 3. 聚光灯特效模板 - 为高饱和度颜色生成聚光灯效果
        this.palette.forEach(color => {
            if (this.hasHighSaturation(color)) {
                gradients.push({
                    background: this.createSpotlightGradient(color),
                    showPlusBadge: true,
                })
            }
        })

        return gradients
    }
}
