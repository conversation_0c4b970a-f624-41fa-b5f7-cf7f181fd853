import chroma from 'chroma-js'

/**
 * @description 颜色项接口定义
 */
export interface IColorItem {
    background: string
    showPlusBadge?: boolean
}

/**
 * @description 网格背景项接口定义
 */
export interface IMeshItem extends IColorItem {
    className: string
    backgroundColor: string
    backgroundImage: string
}

/**
 * @description 位置坐标接口定义
 */
interface IPosition {
    x: number
    y: number
}

/**
 * @description 网格模板接口定义
 */
interface IMeshTemplate {
    className: string
    positions: IPosition[]
    backgroundColorIndex: number // 使用调色板中第几个颜色作为背景
}

/**
 * @description 网格颜色生成器类
 * @description 基于固定位置模板和5色调色板生成复杂的网格背景
 */
export class MeshGenerator {
    private palette: string[]

    /**
     * @description 构造函数
     * @param {string[]} palette - 5个基础RGB颜色字符串数组
     */
    constructor(palette: string[]) {
        this.palette = palette
    }

    /**
     * @description 预定义的网格模板库
     * @description 每个模板定义了固定的位置坐标和背景色选择规则
     */
    private getMeshTemplates(): Record<string, IMeshTemplate[]> {
        return {
            // 5色网格模板 (1个背景色 + 4个blob)
            mesh5: [
                {
                    className: 'mesh-5-1',
                    positions: [
                        { x: 40, y: 20 },
                        { x: 80, y: 0 },
                        { x: 80, y: 100 },
                        { x: 0, y: 0 },
                    ],
                    backgroundColorIndex: 1, // 使用第2个颜色作为背景
                },
                {
                    className: 'mesh-5-2',
                    positions: [
                        { x: 72, y: 13 },
                        { x: 13, y: 87 },
                        { x: 58, y: 67 },
                        { x: 54, y: 83 },
                    ],
                    backgroundColorIndex: 1, // 使用第2个颜色作为背景
                },
                {
                    className: 'mesh-5-3',
                    positions: [
                        { x: 30, y: 23 },
                        { x: 93, y: 23 },
                        { x: 73, y: 26 },
                        { x: 56, y: 51 },
                    ],
                    backgroundColorIndex: 2, // 使用第3个颜色作为背景
                },
                {
                    className: 'mesh-5-4',
                    positions: [
                        { x: 41, y: 59 },
                        { x: 24, y: 34 },
                        { x: 66, y: 76 },
                        { x: 48, y: 53 },
                    ],
                    backgroundColorIndex: 1, // 使用第2个颜色作为背景
                },
                {
                    className: 'mesh-5-5',
                    positions: [
                        { x: 69, y: 53 },
                        { x: 5, y: 98 },
                        { x: 38, y: 49 },
                        { x: 30, y: 96 },
                    ],
                    backgroundColorIndex: 2, // 使用第3个颜色作为背景
                },
            ],
            // 4色网格模板 (1个背景色 + 3个blob)
            mesh4: [
                {
                    className: 'mesh-4-1',
                    positions: [
                        { x: 17, y: 61 },
                        { x: 72, y: 97 },
                        { x: 44, y: 75 },
                    ],
                    backgroundColorIndex: 3, // 使用第4个颜色作为背景
                },
                {
                    className: 'mesh-4-2',
                    positions: [
                        { x: 26, y: 73 },
                        { x: 59, y: 14 },
                        { x: 71, y: 72 },
                    ],
                    backgroundColorIndex: 0, // 使用第1个颜色作为背景
                },
            ],
            // 3色网格模板 (1个背景色 + 2个blob)
            mesh3: [
                {
                    className: 'mesh-3-1',
                    positions: [
                        { x: 31, y: 77 },
                        { x: 91, y: 12 },
                    ],
                    backgroundColorIndex: 0, // 使用第1个颜色作为背景
                },
                {
                    className: 'mesh-3-2',
                    positions: [
                        { x: 33, y: 75 },
                        { x: 70, y: 39 },
                    ],
                    backgroundColorIndex: 2, // 使用第3个颜色作为背景
                },
            ],
            // 2色网格模板 (1个背景色 + 1个blob)
            mesh2: [
                {
                    className: 'mesh-2-1',
                    positions: [
                        { x: 51, y: 86 },
                        { x: 29, y: 9 },
                    ],
                    backgroundColorIndex: 0, // 使用第1个颜色作为背景
                },
                {
                    className: 'mesh-2-2',
                    positions: [
                        { x: 61, y: 8 },
                        { x: 74, y: 89 },
                    ],
                    backgroundColorIndex: 0, // 使用第1个颜色作为背景
                },
                // 额外的变体 - 使用不同的背景色
                {
                    className: 'mesh-2-1',
                    positions: [
                        { x: 51, y: 86 },
                        { x: 29, y: 9 },
                    ],
                    backgroundColorIndex: 3, // 使用第4个颜色作为背景
                },
                {
                    className: 'mesh-2-2',
                    positions: [
                        { x: 61, y: 8 },
                        { x: 74, y: 89 },
                    ],
                    backgroundColorIndex: 2, // 使用第3个颜色作为背景
                },
            ],
        }
    }

    /**
     * @description 创建单个径向渐变blob
     * @param {IPosition} position - blob位置坐标
     * @param {string} color - blob颜色
     * @returns {string} CSS径向渐变字符串
     */
    private createRadialBlob(position: IPosition, color: string): string {
        return `radial-gradient(at ${position.x}% ${position.y}%, ${color} 0px, transparent 50%)`
    }

    /**
     * @description 生成颜色变体 (轻微调整亮度和饱和度)
     * @param {string} baseColor - 基础颜色
     * @param {number} variation - 变体索引 (0=原色, 1=变体1, 2=变体2等)
     * @returns {string} 调整后的颜色
     */
    private createColorVariation(baseColor: string, variation: number = 0): string {
        if (variation === 0) {
            return baseColor
        }

        try {
            const chromaColor = chroma(baseColor)

            // 根据变体索引应用不同的调整
            switch (variation) {
            case 1:
                // 变体1: 稍微提亮并增加饱和度
                return chromaColor.brighten(0.3).saturate(0.2).hex()
            case 2:
                // 变体2: 稍微变暗并减少饱和度
                return chromaColor.darken(0.2).desaturate(0.1).hex()
            default:
                return baseColor
            }
        } catch (error) {
            console.error('Error creating color variation:', error)
            return baseColor
        }
    }

    /**
     * @description 根据模板生成网格背景
     * @param {IMeshTemplate} template - 网格模板
     * @param {string[]} availableColors - 可用的颜色数组
     * @param {boolean} useColorVariations - 是否使用颜色变体
     * @returns {IMeshItem} 生成的网格项
     */
    private generateMeshFromTemplate(
        template: IMeshTemplate,
        availableColors: string[],
        useColorVariations: boolean = false,
    ): IMeshItem {
        const backgroundColor = this.palette[template.backgroundColorIndex] || this.palette[0]

        // 创建blob数组
        const blobs: string[] = []

        template.positions.forEach((position, index) => {
            let color = availableColors[index] || availableColors[0]

            // 对于mesh-2模板，有时使用颜色变体
            if (useColorVariations && template.className.startsWith('mesh-2')) {
                color = this.createColorVariation(color, index + 1)
            }

            blobs.push(this.createRadialBlob(position, color))
        })

        return {
            className: template.className,
            backgroundColor: backgroundColor,
            backgroundImage: blobs.join(', '),
            background: '',
            showPlusBadge: template.className !== 'mesh-5-1', // 第一个不显示Plus徽章
        }
    }

    /**
     * @description 获取除背景色外的可用颜色
     * @param {number} backgroundIndex - 背景色索引
     * @returns {string[]} 可用颜色数组
     */
    private getAvailableColors(backgroundIndex: number): string[] {
        return this.palette.filter((_, index) => index !== backgroundIndex)
    }

    /**
     * @description 主要的网格生成函数
     * @returns {IMeshItem[]} 生成的网格背景数组
     */
    public generateMeshColors(): IMeshItem[] {
        const meshItems: IMeshItem[] = []
        const templates = this.getMeshTemplates()

        // 生成5色网格
        templates.mesh5.forEach(template => {
            const availableColors = this.getAvailableColors(template.backgroundColorIndex)
            meshItems.push(this.generateMeshFromTemplate(template, availableColors))
        })

        // 生成4色网格
        templates.mesh4.forEach(template => {
            const availableColors = this.getAvailableColors(template.backgroundColorIndex)
            meshItems.push(this.generateMeshFromTemplate(template, availableColors))
        })

        // 生成3色网格
        templates.mesh3.forEach(template => {
            const availableColors = this.getAvailableColors(template.backgroundColorIndex)
            meshItems.push(this.generateMeshFromTemplate(template, availableColors))
        })

        // 生成2色网格
        templates.mesh2.forEach((template, index) => {
            const availableColors = this.getAvailableColors(template.backgroundColorIndex)
            // 对部分mesh-2模板使用颜色变体
            const useVariations = index >= 2 // 后两个模板使用变体
            meshItems.push(this.generateMeshFromTemplate(template, availableColors, useVariations))
        })

        return meshItems
    }
}
