'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

// 引入颜色渐变生成器
import { GradientGenerator, IColorItem } from './createGradientColors'
// 引入网格颜色生成器
import { MeshGenerator, IMeshItem } from './createMeshColors'

// 引入真实的颜色数据文件
import {
    solidColors as solidColors1,
    gradientColors as gradientColors1,
    meshColors as meshColors1,
    DEFAULT_WALLPAPER_PATH as wallpaper1,
    BackgroundType,
} from '../magicBackground/jsx结构与生成颜色结构体/colorData1'

import {
    solidColors as solidColors2,
    gradientColors as gradientColors2,
    meshColors as meshColors2,
    DEFAULT_WALLPAPER_PATH as wallpaper2,
} from '../magicBackground/jsx结构与生成颜色结构体/colorData2'

import {
    solidColors as solidColors5,
    gradientColors as gradientColors5,
    meshColors as meshColors5,
    DEFAULT_WALLPAPER_PATH as wallpaper5,
} from '../magicBackground/jsx结构与生成颜色结构体/colorData5'

/**
 * @description 数据集合接口定义
 */
interface IDataSet {
    name: string
    wallpaper: string
    solidColors: IColorItem[]
    gradientColors: IColorItem[]
    meshColors: IMeshItem[]
}

/**
 * @description 测试颜色生成器组件
 */
const TestColor = () => {
    const [selectedDataSet, setSelectedDataSet] = useState<string>('colorData1')
    const [generatedGradients, setGeneratedGradients] = useState<IColorItem[]>([])
    const [generatedMeshColors, setGeneratedMeshColors] = useState<IMeshItem[]>([])
    const [showComparison, setShowComparison] = useState<boolean>(false)
    const [activeTab, setActiveTab] = useState<'gradients' | 'mesh'>('gradients')
    const [selectedGeneratedColor, setSelectedGeneratedColor] = useState<string>('#f0f0f0')
    const [selectedOriginalColor, setSelectedOriginalColor] = useState<string>('#f0f0f0')
    const [selectedGeneratedMeshColor, setSelectedGeneratedMeshColor] = useState<string>('#f0f0f0')
    const [selectedOriginalMeshColor, setSelectedOriginalMeshColor] = useState<string>('#f0f0f0')

    // 三个数据集合
    const dataSets: Record<string, IDataSet> = {
        colorData1: {
            name: 'ColorData1',
            wallpaper: wallpaper1,
            solidColors: solidColors1,
            gradientColors: gradientColors1,
            meshColors: meshColors1,
        },
        colorData2: {
            name: 'ColorData2',
            wallpaper: wallpaper2,
            solidColors: solidColors2,
            gradientColors: gradientColors2,
            meshColors: meshColors2,
        },
        colorData5: {
            name: 'ColorData5',
            wallpaper: wallpaper5,
            solidColors: solidColors5,
            gradientColors: gradientColors5,
            meshColors: meshColors5,
        },
    }

    /**
     * @description 生成渐变颜色
     */
    const generateGradients = () => {
        const currentDataSet = dataSets[selectedDataSet]
        const palette = currentDataSet.solidColors.map(item => item.background)
        const generator = new GradientGenerator(palette)
        const gradients = generator.generateGradients()
        setGeneratedGradients(gradients)
        console.log('gradients===', gradients, gradients.length)

        // 自动选择第一个生成的渐变颜色
        if (gradients.length > 0) {
            setSelectedGeneratedColor(gradients[0].background)
        }
    }

    /**
     * @description 生成网格颜色
     */
    const generateMeshColors = () => {
        const currentDataSet = dataSets[selectedDataSet]
        const palette = currentDataSet.solidColors.map(item => item.background)
        const generator = new MeshGenerator(palette)
        const meshColors = generator.generateMeshColors()
        setGeneratedMeshColors(meshColors)
        console.log('meshColors===', meshColors, meshColors.length)

        // 自动选择第一个生成的网格颜色
        if (meshColors.length > 0) {
            const firstMesh = meshColors[0]
            const combinedStyle = `${firstMesh.backgroundColor}; background-image: ${firstMesh.backgroundImage};`
            setSelectedGeneratedMeshColor(combinedStyle)
        }
    }

    /**
     * @description 复制对比数据到剪贴板
     */
    const copyComparisonData = async () => {
        const currentDataSet = dataSets[selectedDataSet]

        const logData = {
            dataSetName: currentDataSet.name,
            wallpaper: currentDataSet.wallpaper,
            solidColors: currentDataSet.solidColors,
            originalGradients: {
                count: currentDataSet.gradientColors.length,
                data: currentDataSet.gradientColors,
            },
            generatedGradients: {
                count: generatedGradients.length,
                data: generatedGradients,
            },
            originalMeshColors: {
                count: currentDataSet.meshColors.length,
                data: currentDataSet.meshColors,
            },
            generatedMeshColors: {
                count: generatedMeshColors.length,
                data: generatedMeshColors,
            },
            comparisonAnalysis: {
                originalGradientCount: currentDataSet.gradientColors.length,
                generatedGradientCount: generatedGradients.length,
                gradientDifference:
                    generatedGradients.length - currentDataSet.gradientColors.length,
                originalMeshCount: currentDataSet.meshColors.length,
                generatedMeshCount: generatedMeshColors.length,
                meshDifference: generatedMeshColors.length - currentDataSet.meshColors.length,
                timestamp: new Date().toISOString(),
            },
        }

        try {
            await navigator.clipboard.writeText(JSON.stringify(logData, null, 2))
            alert('对比数据已复制到剪贴板！')
        } catch (error) {
            console.error('复制失败:', error)
            alert('复制失败，请手动复制控制台输出的数据')
            console.log('LOG数据:', logData)
        }
    }

    /**
     * @description 复制所有数据集的对比数据到剪贴板
     */
    const copyAllComparisonData = async () => {
        const allDataSets = []
        let totalOriginalCount = 0
        let totalGeneratedCount = 0

        // 遍历所有数据集，生成对比数据
        for (const [key, dataSet] of Object.entries(dataSets)) {
            const palette = dataSet.solidColors.map(item => item.background)
            const gradientGenerator = new GradientGenerator(palette)
            const meshGenerator = new MeshGenerator(palette)
            const gradients = gradientGenerator.generateGradients()
            const meshColors = meshGenerator.generateMeshColors()

            const dataSetComparison = {
                dataSetName: dataSet.name,
                wallpaper: dataSet.wallpaper,
                solidColors: dataSet.solidColors,
                originalGradients: {
                    count: dataSet.gradientColors.length,
                    data: dataSet.gradientColors,
                },
                generatedGradients: {
                    count: gradients.length,
                    data: gradients,
                },
                originalMeshColors: {
                    count: dataSet.meshColors.length,
                    data: dataSet.meshColors,
                },
                generatedMeshColors: {
                    count: meshColors.length,
                    data: meshColors,
                },
                comparisonAnalysis: {
                    originalGradientCount: dataSet.gradientColors.length,
                    generatedGradientCount: gradients.length,
                    gradientDifference: gradients.length - dataSet.gradientColors.length,
                    originalMeshCount: dataSet.meshColors.length,
                    generatedMeshCount: meshColors.length,
                    meshDifference: meshColors.length - dataSet.meshColors.length,
                    timestamp: new Date().toISOString(),
                },
            }

            allDataSets.push(dataSetComparison)
            totalOriginalCount += dataSet.gradientColors.length + dataSet.meshColors.length
            totalGeneratedCount += gradients.length + meshColors.length
        }

        // 创建综合对比报告
        const comprehensiveReport = {
            reportType: 'comprehensive_comparison',
            timestamp: new Date().toISOString(),
            summary: {
                totalDataSets: allDataSets.length,
                totalOriginalGradients: totalOriginalCount,
                totalGeneratedGradients: totalGeneratedCount,
                overallDifference: totalGeneratedCount - totalOriginalCount,
                averageOriginalPerDataSet: Math.round(totalOriginalCount / allDataSets.length),
                averageGeneratedPerDataSet: Math.round(totalGeneratedCount / allDataSets.length),
            },
            detailedComparisons: allDataSets,
            analysisNotes: {
                generationAlgorithm: 'Reverse Engineering Color Gradient Generation System',
                templates: [
                    'Single Color Scale Template (Linear/Radial)',
                    'Dual Color Combination Template (140°/144° angles)',
                    'Spotlight Effect Template (High saturation colors)',
                ],
                colorProcessing: [
                    'Neutral color detection (black/white/gray)',
                    'Saturation analysis for spotlight effects',
                    'Chroma.js color manipulation',
                    'Automatic brightness and saturation adjustments',
                ],
            },
        }

        try {
            await navigator.clipboard.writeText(JSON.stringify(comprehensiveReport, null, 2))
            alert('所有数据集对比数据已复制到剪贴板！')
        } catch (error) {
            console.error('复制失败:', error)
            alert('复制失败，请手动复制控制台输出的数据')
            console.log('全部LOG数据:', comprehensiveReport)
        }
    }

    /**
     * @description 处理生成的颜色点击
     * @param {string} background - 背景颜色样式
     */
    const handleGeneratedColorClick = (background: string) => {
        setSelectedGeneratedColor(background)
    }

    /**
     * @description 处理原始颜色点击
     * @param {string} background - 背景颜色样式
     */
    const handleOriginalColorClick = (background: string) => {
        setSelectedOriginalColor(background)
    }

    /**
     * @description 处理生成的网格颜色点击
     * @param {IMeshItem} meshItem - 网格项
     */
    const handleGeneratedMeshClick = (meshItem: IMeshItem) => {
        // 网格颜色的复合样式处理
        const combinedStyle = `${meshItem.backgroundColor}; background-image: ${meshItem.backgroundImage};`
        setSelectedGeneratedMeshColor(combinedStyle)
    }

    /**
     * @description 处理原始网格颜色点击
     * @param {IMeshItem} meshItem - 网格项
     */
    const handleOriginalMeshClick = (meshItem: IMeshItem) => {
        // 网格颜色的复合样式处理
        const combinedStyle = `${meshItem.backgroundColor}; background-image: ${meshItem.backgroundImage};`
        setSelectedOriginalMeshColor(combinedStyle)
    }

    /**
     * @description 解析网格颜色的复合样式
     * @param {string} combinedStyle - 复合样式字符串
     * @returns {object} 解析后的样式对象
     */
    const parseMeshStyle = (combinedStyle: string) => {
        if (!combinedStyle.includes(';')) {
            return { backgroundColor: combinedStyle, backgroundImage: 'none' }
        }

        const parts = combinedStyle.split(';')
        const backgroundColor = parts[0]?.trim() || '#f0f0f0'
        const backgroundImagePart = parts.find(part => part.includes('background-image:'))
        const backgroundImage = backgroundImagePart
            ? backgroundImagePart.replace('background-image:', '').trim()
            : 'none'

        return { backgroundColor, backgroundImage }
    }

    useEffect(() => {
        generateGradients()
        generateMeshColors()

        // 初始化默认选择的颜色
        const currentDataSet = dataSets[selectedDataSet]
        if (currentDataSet.gradientColors.length > 0) {
            setSelectedOriginalColor(currentDataSet.gradientColors[0].background)
        }
        if (currentDataSet.meshColors.length > 0) {
            const firstMesh = currentDataSet.meshColors[0]
            const combinedStyle = `${firstMesh.backgroundColor}; background-image: ${firstMesh.backgroundImage};`
            setSelectedOriginalMeshColor(combinedStyle)
        }
    }, [selectedDataSet])

    const currentDataSet = dataSets[selectedDataSet]

    return (
        <div
            style={{
                height: '100vh',
                display: 'flex',
                flexDirection: 'column',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                overflow: 'hidden',
            }}
        >
            {/* 顶部标题和控制区 */}
            <div
                style={{
                    padding: '15px 20px',
                    background: '#f8f9fa',
                    borderBottom: '1px solid #e9ecef',
                    flexShrink: 0,
                }}
            >
                <h1
                    style={{
                        fontSize: '20px',
                        fontWeight: 'bold',
                        margin: '0 0 12px 0',
                        color: '#333',
                        textAlign: 'center',
                    }}
                >
                    颜色渐变生成器 - 逆向工程测试
                </h1>

                {/* 数据集选择器 */}
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        gap: '20px',
                        marginBottom: '8px',
                    }}
                >
                    {Object.entries(dataSets).map(([key, dataSet]) => (
                        <label
                            key={key}
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '6px',
                                cursor: 'pointer',
                            }}
                        >
                            <input
                                type='radio'
                                name='dataSet'
                                value={key}
                                checked={selectedDataSet === key}
                                onChange={e => setSelectedDataSet(e.target.value)}
                            />
                            <span
                                style={{
                                    fontWeight: '500',
                                    fontSize: '14px',
                                    color: selectedDataSet === key ? '#007bff' : '#666',
                                }}
                            >
                                {dataSet.name}
                            </span>
                        </label>
                    ))}
                </div>

                {/* 控制按钮 */}
                <div
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        gap: '10px',
                    }}
                >
                    <button
                        onClick={() => setShowComparison(!showComparison)}
                        style={{
                            background: showComparison ? '#28a745' : '#6c757d',
                            color: 'white',
                            border: 'none',
                            padding: '5px 10px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            cursor: 'pointer',
                        }}
                    >
                        {showComparison ? '隐藏对比' : '显示对比'}
                    </button>
                    <button
                        onClick={() => {
                            generateGradients()
                            generateMeshColors()
                        }}
                        style={{
                            background: '#007bff',
                            color: 'white',
                            border: 'none',
                            padding: '5px 10px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            cursor: 'pointer',
                        }}
                    >
                        重新生成
                    </button>
                    <button
                        onClick={() =>
                            setActiveTab(activeTab === 'gradients' ? 'mesh' : 'gradients')
                        }
                        style={{
                            background: '#6f42c1',
                            color: 'white',
                            border: 'none',
                            padding: '5px 10px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            cursor: 'pointer',
                        }}
                    >
                        {activeTab === 'gradients' ? '切换到网格' : '切换到渐变'}
                    </button>
                    <button
                        onClick={copyComparisonData}
                        style={{
                            background: '#dc3545',
                            color: 'white',
                            border: 'none',
                            padding: '5px 10px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            cursor: 'pointer',
                        }}
                    >
                        复制LOG
                    </button>
                    <button
                        onClick={copyAllComparisonData}
                        style={{
                            background: '#17a2b8',
                            color: 'white',
                            border: 'none',
                            padding: '5px 10px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            cursor: 'pointer',
                        }}
                    >
                        复制全部LOG
                    </button>
                </div>
            </div>

            {/* 主体内容区 */}
            <div
                style={{
                    flex: 1,
                    display: 'flex',
                    overflow: 'hidden',
                }}
            >
                {/* 左侧：图片和基础调色板 */}
                <div
                    style={{
                        width: '280px',
                        padding: '15px',
                        background: '#ffffff',
                        borderRight: '1px solid #e9ecef',
                        flexShrink: 0,
                        overflowY: 'auto',
                    }}
                >
                    {/* 壁纸展示 */}
                    <div style={{ marginBottom: '15px' }}>
                        <h3
                            style={{
                                fontSize: '14px',
                                fontWeight: '600',
                                marginBottom: '8px',
                                color: '#333',
                            }}
                        >
                            源图片：
                        </h3>
                        <div
                            style={{
                                position: 'relative',
                                width: '100%',
                                height: '120px',
                                borderRadius: '6px',
                                overflow: 'hidden',
                                border: '1px solid #ddd',
                            }}
                        >
                            <Image
                                src={currentDataSet.wallpaper}
                                alt={`${currentDataSet.name} wallpaper`}
                                fill
                                style={{ objectFit: 'cover' }}
                            />
                        </div>
                    </div>

                    {/* 颜色选择器 */}
                    <div style={{ marginBottom: '15px' }}>
                        <h3
                            style={{
                                fontSize: '14px',
                                fontWeight: '600',
                                marginBottom: '8px',
                                color: '#333',
                            }}
                        >
                            颜色选择器：
                        </h3>
                        <div
                            style={{
                                display: 'grid',
                                gridTemplateColumns: '1fr 1fr',
                                gap: '8px',
                                marginBottom: '8px',
                            }}
                        >
                            {/* 渐变颜色 - 生成的 */}
                            <div
                                style={{
                                    textAlign: 'center',
                                }}
                            >
                                <div
                                    style={{
                                        fontSize: '10px',
                                        color: '#666',
                                        marginBottom: '4px',
                                        fontWeight: '500',
                                    }}
                                >
                                    渐变生成
                                </div>
                                <div
                                    style={{
                                        width: '100%',
                                        height: '40px',
                                        borderRadius: '6px',
                                        border: '2px solid #007bff',
                                        background: selectedGeneratedColor,
                                        cursor: 'pointer',
                                    }}
                                    title={`生成的渐变颜色: ${selectedGeneratedColor}`}
                                />
                            </div>

                            {/* 渐变颜色 - 原始的 */}
                            <div
                                style={{
                                    textAlign: 'center',
                                }}
                            >
                                <div
                                    style={{
                                        fontSize: '10px',
                                        color: '#666',
                                        marginBottom: '4px',
                                        fontWeight: '500',
                                    }}
                                >
                                    渐变原始
                                </div>
                                <div
                                    style={{
                                        width: '100%',
                                        height: '40px',
                                        borderRadius: '6px',
                                        border: '2px solid #28a745',
                                        background: selectedOriginalColor,
                                        cursor: 'pointer',
                                    }}
                                    title={`原始的渐变颜色: ${selectedOriginalColor}`}
                                />
                            </div>

                            {/* 网格颜色 - 生成的 */}
                            <div
                                style={{
                                    textAlign: 'center',
                                }}
                            >
                                <div
                                    style={{
                                        fontSize: '10px',
                                        color: '#666',
                                        marginBottom: '4px',
                                        fontWeight: '500',
                                    }}
                                >
                                    网格生成
                                </div>
                                <div
                                    style={{
                                        width: '100%',
                                        height: '40px',
                                        borderRadius: '6px',
                                        border: '2px solid #6f42c1',
                                        ...parseMeshStyle(selectedGeneratedMeshColor),
                                        cursor: 'pointer',
                                    }}
                                    title={`生成的网格颜色: ${selectedGeneratedMeshColor}`}
                                />
                            </div>

                            {/* 网格颜色 - 原始的 */}
                            <div
                                style={{
                                    textAlign: 'center',
                                }}
                            >
                                <div
                                    style={{
                                        fontSize: '10px',
                                        color: '#666',
                                        marginBottom: '4px',
                                        fontWeight: '500',
                                    }}
                                >
                                    网格原始
                                </div>
                                <div
                                    style={{
                                        width: '100%',
                                        height: '40px',
                                        borderRadius: '6px',
                                        border: '2px solid #fd7e14',
                                        ...parseMeshStyle(selectedOriginalMeshColor),
                                        cursor: 'pointer',
                                    }}
                                    title={`原始的网格颜色: ${selectedOriginalMeshColor}`}
                                />
                            </div>
                        </div>
                    </div>

                    {/* 基础调色板 */}
                    <div>
                        <h3
                            style={{
                                fontSize: '14px',
                                fontWeight: '600',
                                marginBottom: '8px',
                                color: '#333',
                            }}
                        >
                            基础调色板 ({currentDataSet.solidColors.length}个)：
                        </h3>
                        <div
                            style={{
                                display: 'grid',
                                gridTemplateColumns: 'repeat(2, 1fr)',
                                gap: '6px',
                            }}
                        >
                            {currentDataSet.solidColors.map((color, index) => (
                                <div
                                    key={index}
                                    style={{
                                        height: '35px',
                                        borderRadius: '4px',
                                        border: '1px solid #ddd',
                                        backgroundColor: color.background,
                                        position: 'relative',
                                        cursor: 'pointer',
                                    }}
                                    title={color.background}
                                >
                                    {color.showPlusBadge && (
                                        <div
                                            style={{
                                                position: 'absolute',
                                                top: '2px',
                                                right: '2px',
                                                background: '#007bff',
                                                color: 'white',
                                                padding: '1px 3px',
                                                borderRadius: '2px',
                                                fontSize: '8px',
                                            }}
                                        >
                                            +
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* 右侧：渐变对比区 */}
                <div
                    style={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        overflow: 'hidden',
                    }}
                >
                    {/* 标签页导航 */}
                    <div
                        style={{
                            display: 'flex',
                            background: '#f8f9fa',
                            borderBottom: '1px solid #e9ecef',
                            flexShrink: 0,
                        }}
                    >
                        <button
                            onClick={() => setActiveTab('gradients')}
                            style={{
                                flex: 1,
                                padding: '12px 16px',
                                border: 'none',
                                background: activeTab === 'gradients' ? '#ffffff' : 'transparent',
                                borderBottom:
                                    activeTab === 'gradients'
                                        ? '2px solid #007bff'
                                        : '2px solid transparent',
                                color: activeTab === 'gradients' ? '#007bff' : '#666',
                                fontWeight: activeTab === 'gradients' ? '600' : '400',
                                fontSize: '14px',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease',
                            }}
                        >
                            渐变颜色 ({generatedGradients.length}个)
                        </button>
                        <button
                            onClick={() => setActiveTab('mesh')}
                            style={{
                                flex: 1,
                                padding: '12px 16px',
                                border: 'none',
                                background: activeTab === 'mesh' ? '#ffffff' : 'transparent',
                                borderBottom:
                                    activeTab === 'mesh'
                                        ? '2px solid #007bff'
                                        : '2px solid transparent',
                                color: activeTab === 'mesh' ? '#007bff' : '#666',
                                fontWeight: activeTab === 'mesh' ? '600' : '400',
                                fontSize: '14px',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease',
                            }}
                        >
                            网格颜色 ({generatedMeshColors.length}个)
                        </button>
                    </div>

                    {/* 标签页内容区 */}
                    <div
                        style={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: showComparison ? 'row' : 'column',
                            overflow: 'hidden',
                        }}
                    >
                        {/* 渐变颜色标签页 */}
                        {activeTab === 'gradients' && (
                            <>
                                {/* 生成的渐变 */}
                                <div
                                    style={{
                                        flex: 1,
                                        padding: '15px',
                                        overflowY: 'auto',
                                    }}
                                >
                                    <h3
                                        style={{
                                            fontSize: '14px',
                                            fontWeight: '600',
                                            marginBottom: '12px',
                                            color: '#333',
                                            position: 'sticky',
                                            top: '0',
                                            background: 'white',
                                            padding: '8px 0',
                                            borderBottom: '1px solid #eee',
                                        }}
                                    >
                                        生成的渐变 ({generatedGradients.length}个)：
                                    </h3>
                                    <div
                                        style={{
                                            display: 'grid',
                                            gridTemplateColumns: showComparison
                                                ? '1fr'
                                                : 'repeat(auto-fit, minmax(180px, 1fr))',
                                            gap: '10px',
                                        }}
                                    >
                                        {generatedGradients.map((gradient, index) => (
                                            <div
                                                key={index}
                                                style={{
                                                    background: 'white',
                                                    borderRadius: '6px',
                                                    padding: '8px',
                                                    border: '1px solid #e9ecef',
                                                    cursor: 'pointer',
                                                    transition: 'transform 0.2s ease',
                                                }}
                                                onClick={() =>
                                                    handleGeneratedColorClick(gradient.background)
                                                }
                                                onMouseOver={e => {
                                                    e.currentTarget.style.transform = 'scale(1.02)'
                                                }}
                                                onMouseOut={e => {
                                                    e.currentTarget.style.transform = 'scale(1)'
                                                }}
                                            >
                                                <div
                                                    style={{
                                                        width: '100%',
                                                        height: showComparison ? '50px' : '60px',
                                                        borderRadius: '4px',
                                                        marginBottom: '6px',
                                                        border: '1px solid #ddd',
                                                        background: gradient.background,
                                                    }}
                                                />
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center',
                                                        fontSize: '11px',
                                                    }}
                                                >
                                                    <span style={{ color: '#666' }}>
                                                        #{index + 1}
                                                    </span>
                                                    {gradient.showPlusBadge && (
                                                        <span
                                                            style={{
                                                                background: '#007bff',
                                                                color: 'white',
                                                                padding: '2px 4px',
                                                                borderRadius: '3px',
                                                                fontSize: '9px',
                                                            }}
                                                        >
                                                            Plus
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* 原始渐变（仅在对比模式下显示） */}
                                {showComparison && (
                                    <div
                                        style={{
                                            flex: 1,
                                            padding: '15px',
                                            background: '#f8f9fa',
                                            borderLeft: '1px solid #e9ecef',
                                            overflowY: 'auto',
                                        }}
                                    >
                                        <h3
                                            style={{
                                                fontSize: '14px',
                                                fontWeight: '600',
                                                marginBottom: '12px',
                                                color: '#333',
                                                position: 'sticky',
                                                top: '0',
                                                background: '#f8f9fa',
                                                padding: '8px 0',
                                                borderBottom: '1px solid #ddd',
                                            }}
                                        >
                                            原始渐变 ({currentDataSet.gradientColors.length}个)：
                                        </h3>
                                        <div
                                            style={{
                                                display: 'grid',
                                                gridTemplateColumns: '1fr',
                                                gap: '10px',
                                            }}
                                        >
                                            {currentDataSet.gradientColors.map(
                                                (gradient, index) => (
                                                    <div
                                                        key={index}
                                                        style={{
                                                            background: 'white',
                                                            borderRadius: '6px',
                                                            padding: '8px',
                                                            border: '1px solid #e9ecef',
                                                            cursor: 'pointer',
                                                            transition: 'transform 0.2s ease',
                                                        }}
                                                        onClick={() =>
                                                            handleOriginalColorClick(
                                                                gradient.background,
                                                            )
                                                        }
                                                        onMouseOver={e => {
                                                            e.currentTarget.style.transform =
                                                                'scale(1.02)'
                                                        }}
                                                        onMouseOut={e => {
                                                            e.currentTarget.style.transform =
                                                                'scale(1)'
                                                        }}
                                                    >
                                                        <div
                                                            style={{
                                                                width: '100%',
                                                                height: '50px',
                                                                borderRadius: '4px',
                                                                marginBottom: '6px',
                                                                border: '1px solid #ddd',
                                                                background: gradient.background,
                                                            }}
                                                        />
                                                        <div
                                                            style={{
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                alignItems: 'center',
                                                                fontSize: '11px',
                                                            }}
                                                        >
                                                            <span style={{ color: '#666' }}>
                                                                #{index + 1}
                                                            </span>
                                                            {gradient.showPlusBadge && (
                                                                <span
                                                                    style={{
                                                                        background: '#28a745',
                                                                        color: 'white',
                                                                        padding: '2px 4px',
                                                                        borderRadius: '3px',
                                                                        fontSize: '9px',
                                                                    }}
                                                                >
                                                                    Plus
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                )}
                            </>
                        )}

                        {/* 网格颜色标签页 */}
                        {activeTab === 'mesh' && (
                            <>
                                {/* 生成的网格 */}
                                <div
                                    style={{
                                        flex: 1,
                                        padding: '15px',
                                        overflowY: 'auto',
                                    }}
                                >
                                    <h3
                                        style={{
                                            fontSize: '14px',
                                            fontWeight: '600',
                                            marginBottom: '12px',
                                            color: '#333',
                                            position: 'sticky',
                                            top: '0',
                                            background: 'white',
                                            padding: '8px 0',
                                            borderBottom: '1px solid #eee',
                                        }}
                                    >
                                        生成的网格 ({generatedMeshColors.length}个)：
                                    </h3>
                                    <div
                                        style={{
                                            display: 'grid',
                                            gridTemplateColumns: showComparison
                                                ? '1fr'
                                                : 'repeat(auto-fit, minmax(180px, 1fr))',
                                            gap: '10px',
                                        }}
                                    >
                                        {generatedMeshColors.map((meshItem, index) => (
                                            <div
                                                key={index}
                                                style={{
                                                    background: 'white',
                                                    borderRadius: '6px',
                                                    padding: '8px',
                                                    border: '1px solid #e9ecef',
                                                    cursor: 'pointer',
                                                    transition: 'transform 0.2s ease',
                                                }}
                                                onClick={() => handleGeneratedMeshClick(meshItem)}
                                                onMouseOver={e => {
                                                    e.currentTarget.style.transform = 'scale(1.02)'
                                                }}
                                                onMouseOut={e => {
                                                    e.currentTarget.style.transform = 'scale(1)'
                                                }}
                                            >
                                                <div
                                                    style={{
                                                        width: '100%',
                                                        height: showComparison ? '50px' : '60px',
                                                        borderRadius: '4px',
                                                        marginBottom: '6px',
                                                        border: '1px solid #ddd',
                                                        backgroundColor: meshItem.backgroundColor,
                                                        backgroundImage: meshItem.backgroundImage,
                                                    }}
                                                />
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center',
                                                        fontSize: '11px',
                                                    }}
                                                >
                                                    <span style={{ color: '#666' }}>
                                                        {meshItem.className} #{index + 1}
                                                    </span>
                                                    {meshItem.showPlusBadge && (
                                                        <span
                                                            style={{
                                                                background: '#007bff',
                                                                color: 'white',
                                                                padding: '2px 4px',
                                                                borderRadius: '3px',
                                                                fontSize: '9px',
                                                            }}
                                                        >
                                                            Plus
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* 原始网格（仅在对比模式下显示） */}
                                {showComparison && (
                                    <div
                                        style={{
                                            flex: 1,
                                            padding: '15px',
                                            background: '#f8f9fa',
                                            borderLeft: '1px solid #e9ecef',
                                            overflowY: 'auto',
                                        }}
                                    >
                                        <h3
                                            style={{
                                                fontSize: '14px',
                                                fontWeight: '600',
                                                marginBottom: '12px',
                                                color: '#333',
                                                position: 'sticky',
                                                top: '0',
                                                background: '#f8f9fa',
                                                padding: '8px 0',
                                                borderBottom: '1px solid #ddd',
                                            }}
                                        >
                                            原始网格 ({currentDataSet.meshColors.length}个)：
                                        </h3>
                                        <div
                                            style={{
                                                display: 'grid',
                                                gridTemplateColumns: '1fr',
                                                gap: '10px',
                                            }}
                                        >
                                            {currentDataSet.meshColors.map((meshItem, index) => (
                                                <div
                                                    key={index}
                                                    style={{
                                                        background: 'white',
                                                        borderRadius: '6px',
                                                        padding: '8px',
                                                        border: '1px solid #e9ecef',
                                                        cursor: 'pointer',
                                                        transition: 'transform 0.2s ease',
                                                    }}
                                                    onClick={() =>
                                                        handleOriginalMeshClick(meshItem)
                                                    }
                                                    onMouseOver={e => {
                                                        e.currentTarget.style.transform =
                                                            'scale(1.02)'
                                                    }}
                                                    onMouseOut={e => {
                                                        e.currentTarget.style.transform = 'scale(1)'
                                                    }}
                                                >
                                                    <div
                                                        style={{
                                                            width: '100%',
                                                            height: '50px',
                                                            borderRadius: '4px',
                                                            marginBottom: '6px',
                                                            border: '1px solid #ddd',
                                                            backgroundColor:
                                                                meshItem.backgroundColor,
                                                            backgroundImage:
                                                                meshItem.backgroundImage,
                                                        }}
                                                    />
                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            justifyContent: 'space-between',
                                                            alignItems: 'center',
                                                            fontSize: '11px',
                                                        }}
                                                    >
                                                        <span style={{ color: '#666' }}>
                                                            {meshItem.className} #{index + 1}
                                                        </span>
                                                        {meshItem.showPlusBadge && (
                                                            <span
                                                                style={{
                                                                    background: '#28a745',
                                                                    color: 'white',
                                                                    padding: '2px 4px',
                                                                    borderRadius: '3px',
                                                                    fontSize: '9px',
                                                                }}
                                                            >
                                                                Plus
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default TestColor
