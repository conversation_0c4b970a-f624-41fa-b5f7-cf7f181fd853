// ;<div className='panel-control undefined '>
//     <div className='controls'>
//         <div className='backpack-new'>
//             <div className='backpack-colors-list magic-list is-expanded'>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(193, 192, 197)' }} />
//                     </div>
//                 </button>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(1, 74, 80)' }} />
//                     </div>
//                 </button>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(83, 142, 156)' }} />
//                     </div>
//                 </button>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(144, 103, 75)' }} />
//                     </div>
//                 </button>
//                 <button className='background-item solid-item'>
//                     <div className='display false'>
//                         <div style={{ background: 'rgb(73, 37, 9)' }} />
//                     </div>
//                 </button>
//                 <div className='expand-button is-expanded'>
//                     <div className='items-preview'>
//                         <button className='background-item solid-item'>
//                             <div className='display false'>
//                                 <div style={{ background: 'rgb(144, 103, 75)' }} />
//                             </div>
//                         </button>
//                         <button className='background-item solid-item'>
//                             <div className='display false'>
//                                 <div style={{ background: 'rgb(73, 37, 9)' }} />
//                             </div>
//                         </button>
//                     </div>
//                     <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
//                         <path
//                             fill='currentColor'
//                             d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
//                         />
//                     </svg>
//                 </div>
//             </div>
//         </div>
//         <div className='backpack-new'>
//             <div className='backpack-colors-list magic-list is-expanded'>
//                 <button className='background-item gradient-item'>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(to top, rgb(144, 103, 75) 0%, rgb(249, 178, 130) 35%, rgb(255, 253, 185) 70%, rgb(255, 255, 239) 105%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(to top, rgb(73, 37, 9) 0%, rgb(126, 64, 16) 35%, rgb(180, 91, 22) 70%, rgb(233, 118, 29) 105%, rgb(255, 145, 35) 140%, rgb(255, 172, 42) 175%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(to top, rgb(1, 74, 80) 0%, rgb(2, 128, 138) 35%, rgb(2, 182, 197) 70%, rgb(3, 236, 255) 105%, rgb(4, 255, 255) 140%, rgb(5, 255, 255) 175%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(to top, rgb(83, 142, 156) 0%, rgb(144, 246, 255) 35%, rgb(204, 255, 255) 70%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 115%, rgb(144, 103, 75) 0%, rgb(249, 178, 130) 15%, rgb(255, 253, 185) 30%, rgb(255, 255, 239) 45%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 115%, rgb(73, 37, 9) 0%, rgb(126, 64, 16) 15%, rgb(180, 91, 22) 30%, rgb(233, 118, 29) 45%, rgb(255, 145, 35) 60%, rgb(255, 172, 42) 75%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 115%, rgb(1, 74, 80) 0%, rgb(2, 128, 138) 15%, rgb(2, 182, 197) 30%, rgb(3, 236, 255) 45%, rgb(4, 255, 255) 60%, rgb(5, 255, 255) 75%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 115%, rgb(83, 142, 156) 0%, rgb(144, 246, 255) 15%, rgb(204, 255, 255) 30%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(140deg, rgb(83, 142, 156) 25%, rgb(144, 103, 75) 90%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(140deg, rgb(193, 192, 197) 25%, rgb(73, 37, 9) 90%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(144deg, rgb(211, 211, 213) 20%, rgb(68, 127, 134) 95%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'linear-gradient(144deg, rgb(130, 176, 188) 20%, rgb(184, 144, 120) 95%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 100%, rgb(255, 255, 210) 5%, rgb(230, 165, 120) 20%, black 70%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 100%, rgb(204, 104, 25) 5%, rgb(117, 59, 14) 20%, black 70%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 100%, rgb(3, 207, 224) 5%, rgb(2, 118, 128) 20%, black 70%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <button className='background-item gradient-item'>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             style={{
//                                 background:
//                                     'radial-gradient(circle at 50% 100%, rgb(232, 255, 255) 5%, rgb(133, 227, 250) 20%, black 70%)',
//                             }}
//                         />
//                     </div>
//                 </button>
//                 <div className='expand-button is-expanded'>
//                     <div className='items-preview'>
//                         <button className='background-item gradient-item'>
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display false'>
//                                 <div
//                                     style={{
//                                         background:
//                                             'linear-gradient(to top, rgb(83, 142, 156) 0%, rgb(144, 246, 255) 35%, rgb(204, 255, 255) 70%)',
//                                     }}
//                                 />
//                             </div>
//                         </button>
//                         <button className='background-item gradient-item'>
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display false'>
//                                 <div
//                                     style={{
//                                         background:
//                                             'radial-gradient(circle at 50% 115%, rgb(144, 103, 75) 0%, rgb(249, 178, 130) 15%, rgb(255, 253, 185) 30%, rgb(255, 255, 239) 45%)',
//                                     }}
//                                 />
//                             </div>
//                         </button>
//                     </div>
//                     <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
//                         <path
//                             fill='currentColor'
//                             d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
//                         />
//                     </svg>
//                 </div>
//             </div>
//         </div>
//         <div className='backpack-new'>
//             <div className='backpack-colors-list magic-list is-expanded'>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(83, 142, 156)',
//                                     backgroundImage:
//                                         'radial-gradient(at 40% 20%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 80% 0%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 80% 100%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 0% 0%, rgb(73, 37, 9) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-2'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(83, 142, 156)',
//                                     backgroundImage:
//                                         'radial-gradient(at 72% 13%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 13% 87%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 58% 67%, rgb(73, 37, 9) 0px, transparent 50%), radial-gradient(at 54% 83%, rgb(193, 192, 197) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-3'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(1, 74, 80)',
//                                     backgroundImage:
//                                         'radial-gradient(at 30% 23%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 93% 23%, rgb(83, 142, 156) 0px, transparent 50%), radial-gradient(at 73% 26%, rgb(73, 37, 9) 0px, transparent 50%), radial-gradient(at 56% 51%, rgb(193, 192, 197) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-4'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(1, 74, 80)',
//                                     backgroundImage:
//                                         'radial-gradient(at 41% 59%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 24% 34%, rgb(73, 37, 9) 0px, transparent 50%), radial-gradient(at 66% 76%, rgb(83, 142, 156) 0px, transparent 50%), radial-gradient(at 48% 53%, rgb(193, 192, 197) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-5-5'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(144, 103, 75)',
//                                     backgroundImage:
//                                         'radial-gradient(at 69% 53%, rgb(73, 37, 9) 0px, transparent 50%), radial-gradient(at 5% 98%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 38% 49%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 30% 96%, rgb(83, 142, 156) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-4-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(193, 192, 197)',
//                                     backgroundImage:
//                                         'radial-gradient(at 17% 61%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 72% 97%, rgb(83, 142, 156) 0px, transparent 50%), radial-gradient(at 44% 75%, rgb(1, 74, 80) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-4-2'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(144, 103, 75)',
//                                     backgroundImage:
//                                         'radial-gradient(at 26% 73%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 59% 14%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 71% 72%, rgb(83, 142, 156) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-3-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(1, 74, 80)',
//                                     backgroundImage:
//                                         'radial-gradient(at 31% 77%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 91% 12%, rgb(83, 142, 156) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-3-2'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(1, 74, 80)',
//                                     backgroundImage:
//                                         'radial-gradient(at 33% 75%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 70% 39%, rgb(83, 142, 156) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-2-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(1, 74, 80)',
//                                     backgroundImage:
//                                         'radial-gradient(at 51% 86%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(221, 220, 223) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-2-2'>
//                             <div style={{ backgroundColor: 'rgb(193, 192, 197)' }} />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-2-1'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(83, 142, 156)',
//                                     backgroundImage:
//                                         'radial-gradient(at 51% 86%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(176, 127, 93) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display '>
//                         <div className='mesh-display mesh-2-2'>
//                             <div
//                                 style={{
//                                     backgroundColor: 'rgb(144, 103, 75)',
//                                     backgroundImage:
//                                         'radial-gradient(at 61% 8%, rgb(83, 142, 156) 0px, transparent 50%), radial-gradient(at 74% 89%, rgb(100, 170, 186) 0px, transparent 50%)',
//                                 }}
//                             />
//                         </div>
//                     </div>
//                 </div>
//                 <div className='expand-button is-expanded'>
//                     <div className='items-preview'>
//                         <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display '>
//                                 <div className='mesh-display mesh-5-4'>
//                                     <div
//                                         style={{
//                                             backgroundColor: 'rgb(83, 142, 156)',
//                                             backgroundImage:
//                                                 'radial-gradient(at 41% 59%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 24% 34%, rgb(73, 37, 9) 0px, transparent 50%), radial-gradient(at 66% 76%, rgb(144, 103, 75) 0px, transparent 50%), radial-gradient(at 48% 53%, rgb(193, 192, 197) 0px, transparent 50%)',
//                                         }}
//                                     />
//                                 </div>
//                             </div>
//                         </div>
//                         <div className='background-item mesh-item' style={{ fontSize: 1 }}>
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display '>
//                                 <div className='mesh-display mesh-5-5'>
//                                     <div
//                                         style={{
//                                             backgroundColor: 'rgb(144, 103, 75)',
//                                             backgroundImage:
//                                                 'radial-gradient(at 69% 53%, rgb(193, 192, 197) 0px, transparent 50%), radial-gradient(at 5% 98%, rgb(83, 142, 156) 0px, transparent 50%), radial-gradient(at 38% 49%, rgb(1, 74, 80) 0px, transparent 50%), radial-gradient(at 30% 96%, rgb(73, 37, 9) 0px, transparent 50%)',
//                                         }}
//                                     />
//                                 </div>
//                             </div>
//                         </div>
//                     </div>
//                     <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
//                         <path
//                             fill='currentColor'
//                             d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
//                         />
//                     </svg>
//                 </div>
//             </div>
//         </div>
//         <div className='backpack-new'>
//             <div className='backpack-colors-list magic-list is-expanded'>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(193, 192, 197)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/b5691e06-c498-49cc-b5ba-524cae233238'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(1, 74, 80)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/b5691e06-c498-49cc-b5ba-524cae233238'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(83, 142, 156)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/b5691e06-c498-49cc-b5ba-524cae233238'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(144, 103, 75)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/b5691e06-c498-49cc-b5ba-524cae233238'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <button className='background-item magic-image-item ' style={{ fontSize: 1 }}>
//                     <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                         <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                             <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                 <path
//                                     fill='currentColor'
//                                     d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                 />
//                             </svg>
//                         </div>
//                     </div>
//                     <div className='display false'>
//                         <div
//                             className='magic-image-display style-2'
//                             style={{ background: 'rgb(73, 37, 9)' }}
//                         >
//                             <img
//                                 crossOrigin='anonymous'
//                                 loading='lazy'
//                                 decoding='async'
//                                 alt='magicImage'
//                                 src='blob:https://shots.so/b5691e06-c498-49cc-b5ba-524cae233238'
//                             />
//                         </div>
//                     </div>
//                 </button>
//                 <div className='expand-button is-expanded'>
//                     <div className='items-preview'>
//                         <button
//                             className='background-item magic-image-item '
//                             style={{ fontSize: 1 }}
//                         >
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display false'>
//                                 <div
//                                     className='magic-image-display style-2'
//                                     style={{ background: 'rgb(144, 103, 75)' }}
//                                 >
//                                     <img
//                                         crossOrigin='anonymous'
//                                         loading='lazy'
//                                         decoding='async'
//                                         alt='magicImage'
//                                         src='blob:https://shots.so/b5691e06-c498-49cc-b5ba-524cae233238'
//                                     />
//                                 </div>
//                             </div>
//                         </button>
//                         <button
//                             className='background-item magic-image-item '
//                             style={{ fontSize: 1 }}
//                         >
//                             <div className='plus-badge-wrapper' style={{ padding: 3 }}>
//                                 <div className='plus-badge' style={{ width: 14, height: 14 }}>
//                                     <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
//                                         <path
//                                             fill='currentColor'
//                                             d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
//                                         />
//                                     </svg>
//                                 </div>
//                             </div>
//                             <div className='display false'>
//                                 <div
//                                     className='magic-image-display style-2'
//                                     style={{ background: 'rgb(73, 37, 9)' }}
//                                 >
//                                     <img
//                                         crossOrigin='anonymous'
//                                         loading='lazy'
//                                         decoding='async'
//                                         alt='magicImage'
//                                         src='blob:https://shots.so/b5691e06-c498-49cc-b5ba-524cae233238'
//                                     />
//                                 </div>
//                             </div>
//                         </button>
//                     </div>
//                     <svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'>
//                         <path
//                             fill='currentColor'
//                             d='M2 8.539c0-.797.688-1.448 1.543-1.448.421 0 .821.165 1.12.455l7.348 7.031 7.325-7.031a1.65 1.65 0 0 1 1.121-.455c.855 0 1.543.651 1.543 1.448 0 .403-.144.734-.433 1.003l-8.324 7.93c-.366.352-.766.528-1.243.528-.466 0-.866-.165-1.243-.527L2.444 9.542C2.155 9.262 2 8.932 2 8.539'
//                         />
//                     </svg>
//                 </div>
//             </div>
//         </div>
//     </div>
// </div>
