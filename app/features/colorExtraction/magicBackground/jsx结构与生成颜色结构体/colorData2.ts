/**
 * @file gata.ts
 * @description 这个文件包含了从设计稿中提取的魔术背景颜色数据。
 * @description 它定义了颜色项、网格项的接口，以及背景类型的枚举。
 * @description 它导出了四种类型的背景数据：纯色、渐变色、网格色和图片背景。
 * @description 这些数据用于在应用程序的魔术背景选择器中动态渲染颜色选项。
 * @module app/MobileFrame_Magic/gata
 */

import React from 'react'

/**
 * @description 默认壁纸路径
 */
export const DEFAULT_WALLPAPER_PATH = '/__壁纸测试/walller__2.jpg'

/**
 * @description 背景颜色类型的枚举
 * @enum {string}
 */
export enum BackgroundType {
    /** 纯色 */
    SOLID = 'solid',
    /** 渐变色 */
    GRADIENT = 'gradient',
    /** 网格 */
    MESH = 'mesh',
    /** 图片 */
    IMAGE = 'image',
}

/**
 * @description 颜色项接口定义
 * @interface IColorItem
 * @property {string} background - 背景样式字符串 (例如, 'rgb(255, 255, 255)' 或 'linear-gradient(...)')。
 * @property {boolean} [showPlusBadge] - 是否显示 "Plus" 徽章，通常用于表示高级或额外选项。
 */
export interface IColorItem {
    background: string
    showPlusBadge?: boolean
}

/**
 * @description 网格背景项接口定义，继承自 IColorItem
 * @interface IMeshItem
 * @extends {IColorItem}
 * @property {string} className - 用于应用特定网格样式的CSS类名。
 * @property {string} backgroundColor - 网格背景的基础颜色。
 * @property {string} backgroundImage - 网格的渐变背景图片。
 */
export interface IMeshItem extends IColorItem {
    className: string
    backgroundColor: string
    backgroundImage: string
}

/**
 * @description 纯色背景的数据
 * @type {IColorItem[]}
 */
export const solidColors: IColorItem[] = [
    { background: 'rgb(38, 52, 52)', showPlusBadge: false },
    { background: 'rgb(118, 101, 93)', showPlusBadge: true },
    { background: 'rgb(242, 206, 156)', showPlusBadge: true },
    { background: 'rgb(242, 72, 62)', showPlusBadge: true },
    { background: 'rgb(39, 128, 163)', showPlusBadge: true },
]

/**
 * @description 渐变色背景的数据 长度：15
 * @type {IColorItem[]}
 */
export const gradientColors: IColorItem[] = [
    {
        background:
            'linear-gradient(to top, rgb(242, 71, 61) 0%, rgb(255, 125, 107) 35%, rgb(255, 177, 153) 70%, rgb(255, 230, 198) 105%, rgb(255, 255, 243) 140%)',
        showPlusBadge: false,
    },
    {
        background:
            'linear-gradient(to top, rgb(118, 101, 93) 0%, rgb(204, 175, 161) 35%, rgb(255, 248, 229) 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'linear-gradient(to top, rgb(38, 52, 52) 0%, rgb(66, 90, 90) 35%, rgb(93, 128, 128) 70%, rgb(121, 166, 166) 105%, rgb(149, 204, 204) 140%, rgb(177, 242, 242) 175%)',
        showPlusBadge: true,
    },
    {
        background:
            'linear-gradient(to top, rgb(38, 128, 163) 0%, rgb(67, 221, 255) 35%, rgb(96, 255, 255) 70%, rgb(124, 255, 255) 105%, rgb(153, 255, 255) 140%, rgb(181, 255, 255) 175%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(242, 71, 61) 0%, rgb(255, 125, 107) 15%, rgb(255, 177, 153) 30%, rgb(255, 230, 198) 45%, rgb(255, 255, 243) 60%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(118, 101, 93) 0%, rgb(204, 175, 161) 15%, rgb(255, 248, 229) 30%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(38, 52, 52) 0%, rgb(66, 90, 90) 15%, rgb(93, 128, 128) 30%, rgb(121, 166, 166) 45%, rgb(149, 204, 204) 60%, rgb(177, 242, 242) 75%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 115%, rgb(38, 128, 163) 0%, rgb(67, 221, 255) 15%, rgb(96, 255, 255) 30%, rgb(124, 255, 255) 45%, rgb(153, 255, 255) 60%, rgb(181, 255, 255) 75%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(140deg, rgb(118, 101, 93) 25%, rgb(38, 52, 52) 90%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(140deg, rgb(242, 72, 62) 25%, rgb(39, 128, 163) 90%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(144deg, rgb(160, 144, 136) 20%, rgb(91, 108, 108) 95%)',
        showPlusBadge: true,
    },
    {
        background: 'linear-gradient(144deg, rgb(239, 222, 201) 20%, rgb(226, 149, 146) 95%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 100%, rgb(255, 202, 174) 5%, rgb(255, 115, 99) 20%, black 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 100%, rgb(106, 146, 146) 5%, rgb(61, 83, 83) 20%, black 70%)',
        showPlusBadge: true,
    },
    {
        background:
            'radial-gradient(circle at 50% 100%, rgb(109, 255, 255) 5%, rgb(62, 205, 255) 20%, black 70%)',
        showPlusBadge: true,
    },
]

/**
 * @description 网格背景的数据 长度: 13
 * @type {IMeshItem[]}
 */
export const meshColors: IMeshItem[] = [
    {
        className: 'mesh-5-1',
        backgroundColor: 'rgb(242, 206, 156)',
        backgroundImage:
            'radial-gradient(at 40% 20%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 80% 0%, rgb(39, 128, 163) 0px, transparent 50%), radial-gradient(at 80% 100%, rgb(242, 72, 62) 0px, transparent 50%), radial-gradient(at 0% 0%, rgb(38, 52, 52) 0px, transparent 50%)',
        background: '',
        showPlusBadge: false,
    },
    {
        className: 'mesh-5-2',
        backgroundColor: 'rgb(242, 72, 62)',
        backgroundImage:
            'radial-gradient(at 72% 13%, rgb(39, 128, 163) 0px, transparent 50%), radial-gradient(at 13% 87%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 58% 67%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 54% 83%, rgb(242, 206, 156) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-5-3',
        backgroundColor: 'rgb(118, 101, 93)',
        backgroundImage:
            'radial-gradient(at 30% 23%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 93% 23%, rgb(39, 128, 163) 0px, transparent 50%), radial-gradient(at 73% 26%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 56% 51%, rgb(242, 72, 62) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-5-4',
        backgroundColor: 'rgb(39, 128, 163)',
        backgroundImage:
            'radial-gradient(at 41% 59%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 24% 34%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 66% 76%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 48% 53%, rgb(242, 72, 62) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-5-5',
        backgroundColor: 'rgb(242, 72, 62)',
        backgroundImage:
            'radial-gradient(at 69% 53%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 5% 98%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 38% 49%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 30% 96%, rgb(39, 128, 163) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-4-1',
        backgroundColor: 'rgb(242, 206, 156)',
        backgroundImage:
            'radial-gradient(at 17% 61%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 72% 97%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 44% 75%, rgb(242, 72, 62) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-4-2',
        backgroundColor: 'rgb(242, 206, 156)',
        backgroundImage:
            'radial-gradient(at 26% 73%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 59% 14%, rgb(38, 52, 52) 0px, transparent 50%), radial-gradient(at 71% 72%, rgb(242, 72, 62) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-3-1',
        backgroundColor: 'rgb(38, 52, 52)',
        backgroundImage:
            'radial-gradient(at 31% 77%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 91% 12%, rgb(242, 206, 156) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-3-2',
        backgroundColor: 'rgb(38, 52, 52)',
        backgroundImage:
            'radial-gradient(at 33% 75%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 70% 39%, rgb(118, 101, 93) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-1',
        backgroundColor: 'rgb(38, 52, 52)',
        backgroundImage:
            'radial-gradient(at 51% 86%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(146, 125, 116) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-2',
        backgroundColor: 'rgb(38, 52, 52)',
        backgroundImage:
            'radial-gradient(at 61% 8%, rgb(118, 101, 93) 0px, transparent 50%), radial-gradient(at 74% 89%, rgb(146, 125, 116) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-1',
        backgroundColor: 'rgb(242, 72, 62)',
        backgroundImage:
            'radial-gradient(at 51% 86%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 29% 9%, rgb(250, 238, 225) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
    {
        className: 'mesh-2-2',
        backgroundColor: 'rgb(242, 72, 62)',
        backgroundImage:
            'radial-gradient(at 61% 8%, rgb(242, 206, 156) 0px, transparent 50%), radial-gradient(at 74% 89%, rgb(250, 238, 225) 0px, transparent 50%)',
        background: '',
        showPlusBadge: true,
    },
]

/**
 * @description 图片背景的数据
 * @type {IColorItem[]}
 */
export const imageBackgrounds: IColorItem[] = [
    { background: 'rgb(38, 52, 52)', showPlusBadge: false },
    { background: 'rgb(118, 101, 93)', showPlusBadge: true },
    { background: 'rgb(242, 206, 156)', showPlusBadge: true },
    { background: 'rgb(242, 72, 62)', showPlusBadge: true },
    { background: 'rgb(39, 128, 163)', showPlusBadge: true },
]
