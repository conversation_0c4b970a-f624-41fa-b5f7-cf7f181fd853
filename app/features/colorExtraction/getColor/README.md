# 图片颜色提取模块

该模块提供了基于 ColorThief 库的图片主色调提取功能，支持从多种来源提取颜色调色板。

## 特性

- 🎨 **多种输入支持**：支持图片 URL、文件对象、HTML 图片元素
- 🎯 **灵活配置**：可自定义提取颜色数量
- 🔧 **错误处理**：完善的错误处理和错误信息
- 📦 **TypeScript 支持**：完整的类型定义
- 🚀 **Promise 支持**：基于 async/await 的异步操作

## 安装依赖

```bash
npm install colorthief
# 或
pnpm add colorthief
# 或
yarn add colorthief
```

## 基本用法

### 1. 从图片 URL 提取颜色

```typescript
import { extractColors } from './imageToColors'

const result = await extractColors('https://example.com/image.jpg', {
  colorCount: 5
})

if (result.success) {
  console.log('提取的颜色:', result.hexColors) // ['#FF0000', '#00FF00', ...]
  console.log('RGB值:', result.rgbColors)    // [[255,0,0], [0,255,0], ...]
} else {
  console.error('提取失败:', result.error)
}
```

### 2. 从文件对象提取颜色

```typescript
import { extractColors } from './imageToColors'

// 假设从文件输入获取
const file = document.getElementById('fileInput').files[0]
const result = await extractColors(file, { colorCount: 6 })

if (result.success) {
  console.log('提取的颜色:', result.hexColors)
}
```

### 3. 从 HTML 图片元素提取颜色

```typescript
import { extractColorsFromImageElement } from './imageToColors'

const img = document.getElementById('myImage') as HTMLImageElement
const result = await extractColorsFromImageElement(img, { colorCount: 4 })
```

## API 参考

### 主要函数

#### `extractColors(input, options)`

通用颜色提取函数，自动判断输入类型。

**参数：**
- `input: string | File | HTMLImageElement` - 图片输入
- `options?: ColorExtractionOptions` - 配置选项

**返回：**
- `Promise<ColorExtractionResult>` - 提取结果

### 专用函数

#### `extractColorsFromUrl(url, options)`

从图片 URL 提取颜色。

#### `extractColorsFromFile(file, options)`

从文件对象提取颜色。

#### `extractColorsFromImageElement(element, options)`

从已加载的 HTML 图片元素提取颜色。

### 类型定义

#### `ColorExtractionOptions`

```typescript
interface ColorExtractionOptions {
  /** 要提取的颜色数量，默认为 5 */
  colorCount?: number
  /** 是否启用跨域，默认为 true */
  crossOrigin?: boolean | string
}
```

#### `ColorExtractionResult`

```typescript
interface ColorExtractionResult {
  /** 十六进制颜色代码数组 */
  hexColors: string[]
  /** RGB 颜色值数组 */
  rgbColors: number[][]
  /** 提取状态 */
  success: boolean
  /** 错误信息（如果失败） */
  error?: string
}
```

## React Hook 示例

```typescript
import { useState } from 'react'
import { extractColors } from './imageToColors'

function useColorExtraction() {
  const [colors, setColors] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const extract = async (input: string | File | HTMLImageElement) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await extractColors(input, { colorCount: 5 })
      
      if (result.success) {
        setColors(result.hexColors)
      } else {
        setError(result.error || '提取失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误')
    } finally {
      setLoading(false)
    }
  }

  return { colors, loading, error, extract }
}
```

## 注意事项

1. **跨域问题**：从外部 URL 加载图片时，需要服务器支持 CORS
2. **图片加载**：确保图片完全加载后再进行颜色提取
3. **文件类型**：只支持图片文件（JPEG、PNG、GIF 等）
4. **性能**：大图片可能会影响性能，建议先压缩图片

## 错误处理

所有函数都返回包含 `success` 和 `error` 字段的对象，便于错误处理：

```typescript
const result = await extractColors('invalid-url.jpg')

if (!result.success) {
  switch (result.error) {
    case '图片加载失败':
      // 处理加载失败
      break
    case '无法从图片中提取颜色调色板':
      // 处理提取失败
      break
    default:
      // 处理其他错误
      console.error(result.error)
  }
}
```