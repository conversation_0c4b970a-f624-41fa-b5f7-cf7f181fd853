/**
 * 颜色提取模块使用示例
 *
 * 展示如何使用 imageToColors 模块从不同来源提取图片颜色
 */
import { useState } from 'react'
import { imageToColors, extractColors } from './imageToColors'

// 示例1：从图片URL提取颜色
async function example1() {
    const imageUrl = 'https://example.com/image.jpg'
    const result = await imageToColors.extractColorsFromUrl(imageUrl, {
        colorCount: 5,
    })

    if (result.success) {
        console.log('提取的颜色:', result.hexColors)
        console.log('RGB值:', result.rgbColors)
    } else {
        console.error('提取失败:', result.error)
    }
}

// 示例2：从文件对象提取颜色
async function example2(file: File) {
    const result = await imageToColors.extractColorsFromFile(file, {
        colorCount: 6,
    })

    if (result.success) {
        console.log('提取的颜色:', result.hexColors)
    }
}

// 示例3：从HTML图片元素提取颜色
async function example3(imageElement: HTMLImageElement) {
    const result = await imageToColors.extractColorsFromImageElement(imageElement, {
        colorCount: 4,
    })

    if (result.success) {
        console.log('提取的颜色:', result.hexColors)
    }
}

// 示例4：使用通用函数自动判断输入类型
async function example4(input: string | File | HTMLImageElement) {
    const result = await extractColors(input, {
        colorCount: 5,
    })

    return result
}

// 示例5：React Hook 使用方式
export function useColorExtraction() {
    const [colors, setColors] = useState<string[]>([])
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const extract = async (input: string | File | HTMLImageElement) => {
        setLoading(true)
        setError(null)

        try {
            const result = await extractColors(input, { colorCount: 5 })

            if (result.success) {
                setColors(result.hexColors)
            } else {
                setError(result.error || '提取失败')
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : '未知错误')
        } finally {
            setLoading(false)
        }
    }

    return { colors, loading, error, extract }
}

// 导出示例函数
export const colorExamples = {
    example1,
    example2,
    example3,
    example4,
    useColorExtraction,
}
