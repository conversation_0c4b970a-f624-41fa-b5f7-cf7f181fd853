import { useIsMobile } from '@/app/shared/hooks/useAppState'
import { PublicSliderComponent } from '@/app/components/PublicSliderComponent'
import { useState } from 'react'
import { useSceneStore, SceneTabType, ShadowLayerType } from '@/app/shared/hooks/useSceneStore'

/**
 * 形状类型枚举
 * @description 定义可选择的3D形状类型
 */
enum ShapeType {
    /** 玻璃蓝色 */
    GLASS_BLUE = 'glass_blue',
    /** 玻璃粉色 */
    GLASS_PINK = 'glass_pink',
    /** 毛绒 */
    PLUSHY = 'plushy',
    /** 金属深色 */
    METAL_DARK = 'metal_dark',
    /** 金属浅色 */
    METAL_LIGHT = 'metal_light',
    /** 充气 */
    INFLATABLE = 'inflatable',
    /** 金属 */
    METAL = 'metal',
}

/**
 * 公共框架场景组件
 * @description 处理场景选择、阴影设置和形状配置的组件
 * @param setActiveTab - 设置激活标签页的回调函数
 */
export const Public_FrameScene = ({ setActiveTab }: { setActiveTab: (tab: string) => void }) => {
    // 从Zustand store获取场景状态和方法
    const {
        sceneType,
        shadowType,
        shadowOpacity,
        shadowLayer,
        setSceneType,
        setShadowType,
        setShadowOpacity,
        setShadowLayer,
    } = useSceneStore()

    // 判断是否为移动端设备
    const isMobile = useIsMobile()

    // 当前选中的形状类型状态 - 保持本地状态
    const [activeShapeType, setActiveShapeType] = useState<ShapeType>(ShapeType.GLASS_BLUE)

    // 形状布局按钮激活状态 - 保持本地状态
    const [activeShapeLayoutIndex, setActiveShapeLayoutIndex] = useState<number>(0)

    /**
     * 关闭模态框处理函数
     * @description 清空激活的标签页状态
     */
    const handleClose = () => {
        setActiveTab('')
    }

    /**
     * 处理场景标签页切换
     * @description 根据点击的标签页类型更新状态
     * @param tabType - 要切换到的标签页类型
     */
    const handleSceneTabChange = (tabType: SceneTabType) => {
        if (
            tabType === SceneTabType.NONE ||
            tabType === SceneTabType.SHADOW ||
            tabType === SceneTabType.SHAPES
        ) {
            setSceneType(tabType)
        } else {
            throw new Error(`无效的场景标签页类型: ${tabType}`)
        }
    }

    /**
     * 处理阴影层级切换
     * @description 在 Underlay 和 Overlay 之间切换
     * @param layerType - 要切换到的层级类型
     */
    const handleShadowLayerChange = (layerType: ShadowLayerType) => {
        if (layerType === ShadowLayerType.UNDERLAY || layerType === ShadowLayerType.OVERLAY) {
            setShadowLayer(layerType)
        } else {
            throw new Error(`无效的阴影层级类型: ${layerType}`)
        }
    }

    /**
     * 处理形状类型切换
     * @description 切换选中的3D形状类型
     * @param shapeType - 要切换到的形状类型
     */
    const handleShapeTypeChange = (shapeType: ShapeType) => {
        if (
            shapeType === ShapeType.GLASS_BLUE ||
            shapeType === ShapeType.GLASS_PINK ||
            shapeType === ShapeType.PLUSHY ||
            shapeType === ShapeType.METAL_DARK ||
            shapeType === ShapeType.METAL_LIGHT ||
            shapeType === ShapeType.INFLATABLE ||
            shapeType === ShapeType.METAL
        ) {
            setActiveShapeType(shapeType)
        } else {
            throw new Error(`无效的形状类型: ${shapeType}`)
        }
    }

    /**
     * 处理阴影类型按钮切换
     * @description 切换选中的阴影类型按钮
     * @param item - 阴影类型对象
     */
    const handleShadowTypeToggle = (item: { name: number; src: string }) => {
        if (item && item.name >= 0) {
            setShadowType({ index: item.name, src: item.src })
        } else {
            throw new Error(`无效的阴影类型按钮索引: ${item.name}`)
        }
    }

    /**
     * 处理形状布局按钮切换
     * @description 切换选中的形状布局
     * @param index - 布局索引
     */
    const handleShapeLayoutToggle = (index: number) => {
        if (index >= 0) {
            setActiveShapeLayoutIndex(index)
        } else {
            throw new Error(`无效的形状布局按钮索引: ${index}`)
        }
    }

    /**
     * 渲染场景选择标签页
     * @description 渲染 none、shadow、shapes 三个标签页按钮，支持点击切换
     * @returns JSX.Element 标签页组件
     */
    const publicRender = () => {
        return (
            <>
                {/* None 标签页按钮 */}
                <div
                    className={`panel-button shadow-scene-button ${
                        sceneType === SceneTabType.NONE ? 'true-active' : 'false-active'
                    } has-label`}
                    onClick={() => handleSceneTabChange(SceneTabType.NONE)}
                    style={{ cursor: 'pointer' }}
                >
                    <div className='preview' style={{ aspectRatio: '5 / 4' }}>
                        <div className='icon'>
                            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                <path
                                    fill='currentColor'
                                    fillRule='evenodd'
                                    d='M3.575 7.088A9.7 9.7 0 0 0 2.25 12c0 5.384 4.365 9.75 9.75 9.75 1.79 0 3.468-.483 4.911-1.326l-1.104-1.104A8.25 8.25 0 0 1 3.75 12a8.2 8.2 0 0 1 .929-3.808zm15.686 8.831A8.25 8.25 0 0 0 12 3.75a8.2 8.2 0 0 0-3.92.988L6.981 3.639A9.7 9.7 0 0 1 12 2.25c5.384 0 9.75 4.365 9.75 9.75a9.7 9.7 0 0 1-1.39 5.018z'
                                />
                                <rect
                                    width='1.89'
                                    height='26.833'
                                    x='1.788'
                                    y='3.211'
                                    fill='currentColor'
                                    rx='0.945'
                                    ry='0.945'
                                    transform='rotate(-45 1.789 3.211)'
                                />
                            </svg>
                        </div>
                    </div>
                    <div className='label-wrapper'>
                        <span className='footnote truncate'>none</span>
                    </div>
                </div>

                {/* Shadow 标签页按钮 */}
                <div
                    className={`panel-button shadow-scene-button ${
                        sceneType === SceneTabType.SHADOW ? 'true-active' : 'false-active'
                    } has-label`}
                    onClick={() => handleSceneTabChange(SceneTabType.SHADOW)}
                    style={{ cursor: 'pointer' }}
                >
                    <div className='preview' style={{ aspectRatio: '5 / 4' }}>
                        <div className='image-wrapper'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/image/shadow-scene.png'
                            />
                        </div>
                    </div>
                    <div className='label-wrapper'>
                        <span className='footnote truncate'>shadow</span>
                    </div>
                </div>

                {/* Shapes 标签页按钮 */}
                <div
                    className={`panel-button shadow-scene-button ${
                        sceneType === SceneTabType.SHAPES ? 'true-active' : 'false-active'
                    } has-label`}
                    onClick={() => handleSceneTabChange(SceneTabType.SHAPES)}
                    style={{ cursor: 'pointer' }}
                >
                    <div className='preview' style={{ aspectRatio: '5 / 4' }}>
                        <div className='image-wrapper'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/image/shapes-scene.png'
                            />
                        </div>
                    </div>
                    <div className='label-wrapper'>
                        <span className='footnote truncate'>shapes</span>
                    </div>
                </div>
            </>
        )
    }
    /**
     * 渲染阴影类型选择
     * @description 渲染各种阴影类型的选择按钮，支持点击切换激活状态
     * @returns JSX.Element 阴影类型选择组件
     */
    const publicShadowType = () => {
        const shadowTypeList = [
            {
                name: 1,
                thumbSrc: '/display-assets/shadow-overlays/thumbs/051.jpg',
                src: '/display-assets/shadow-overlays/051.png',
            },
            {
                name: 2,
                thumbSrc: '/display-assets/shadow-overlays/thumbs/011.jpg',
                src: '/display-assets/shadow-overlays/011.png',
            },
            {
                name: 3,
                thumbSrc: '/display-assets/shadow-overlays/thumbs/088.jpg',
                src: '/display-assets/shadow-overlays/088.png',
            },
            {
                name: 4,
                thumbSrc: '/display-assets/shadow-overlays/thumbs/020.jpg',
                src: '/display-assets/shadow-overlays/020.png',
            },
            {
                name: 5,
                thumbSrc: '/display-assets/shadow-overlays/thumbs/066.jpg',
                src: '/display-assets/shadow-overlays/066.png',
            },
            {
                name: 6,
                thumbSrc: '/display-assets/shadow-overlays/thumbs/086.jpg',
                src: '/display-assets/shadow-overlays/086.png',
            },
            {
                name: 7,
                thumbSrc: '/display-assets/shadow-overlays/thumbs/074.jpg',
                src: '/display-assets/shadow-overlays/074.png',
            },
            {
                name: 8,
                thumbSrc: '/display-assets/shadow-overlays/thumbs/032.jpg',
                src: '/display-assets/shadow-overlays/032.png',
            },
        ]
        return (
            <>
                {shadowTypeList.map((item, index) => {
                    return (
                        <div
                            key={index}
                            className={`panel-button undefined ${
                                shadowType.index === item.name ? 'true-active' : 'false-active'
                            }`}
                            onClick={() => handleShadowTypeToggle(item)}
                            style={{ cursor: 'pointer' }}
                        >
                            <div className='preview' style={{ aspectRatio: '16 / 9' }}>
                                <div className='image-wrapper'>
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        src={item.thumbSrc}
                                    />
                                </div>
                            </div>
                        </div>
                    )
                })}
            </>
        )
    }

    /**
     * 渲染阴影选项控制
     * @description 包含透明度滑块和 Underlay/Overlay 切换开关
     * @returns JSX.Element 阴影选项组件
     */
    const publicShadowOptions = () => {
        return (
            <>
                {/* 透明度滑块控制 */}
                <PublicSliderComponent
                    config='scene_shadow_opacity'
                    value={shadowOpacity}
                    setValue={value => setShadowOpacity(value)}
                />

                {/* Underlay/Overlay 切换开关 */}
                <div className='switch'>
                    <button
                        className={`switch-button label-only ${
                            shadowLayer === ShadowLayerType.UNDERLAY ? 'is-active' : ''
                        }`}
                        onClick={() => handleShadowLayerChange(ShadowLayerType.UNDERLAY)}
                        style={{ cursor: 'pointer' }}
                    >
                        <p className='text-capitalize'>Underlay</p>
                        {shadowLayer === ShadowLayerType.UNDERLAY && (
                            <div
                                className='active-indicator'
                                style={{
                                    transform: 'none',
                                    transformOrigin: '50% 50% 0px',
                                }}
                            />
                        )}
                    </button>
                    <button
                        className={`switch-button label-only ${
                            shadowLayer === ShadowLayerType.OVERLAY ? 'is-active' : ''
                        }`}
                        onClick={() => handleShadowLayerChange(ShadowLayerType.OVERLAY)}
                        style={{ cursor: 'pointer' }}
                    >
                        <p className='text-capitalize'>Overlay</p>
                        {shadowLayer === ShadowLayerType.OVERLAY && (
                            <div
                                className='active-indicator'
                                style={{
                                    transform: 'none',
                                    transformOrigin: '50% 50% 0px',
                                }}
                            />
                        )}
                    </button>
                </div>
            </>
        )
    }

    /**
     * 渲染形状类型选择
     * @description 渲染各种3D形状类型的选择按钮，支持点击切换
     * @returns JSX.Element 形状类型选择组件
     */
    const publicShapesType = () => {
        return (
            <>
                {/* Glass Blue 形状按钮 */}
                <div
                    className={`panel-button undefined ${
                        activeShapeType === ShapeType.GLASS_BLUE ? 'true-active' : 'false-active'
                    } has-label`}
                    onClick={() => handleShapeTypeChange(ShapeType.GLASS_BLUE)}
                    style={{ cursor: 'pointer' }}
                >
                    <div className='preview' style={{ aspectRatio: '16 / 10' }}>
                        <div className='image-wrapper'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                            />
                        </div>
                    </div>
                    <div className='label-wrapper'>
                        <span className='footnote truncate'>Glass Blue</span>
                    </div>
                </div>

                {/* Glass Pink 形状按钮 */}
                <div
                    className={`panel-button undefined ${
                        activeShapeType === ShapeType.GLASS_PINK ? 'true-active' : 'false-active'
                    } has-label`}
                    onClick={() => handleShapeTypeChange(ShapeType.GLASS_PINK)}
                    style={{ cursor: 'pointer' }}
                >
                    <div className='preview' style={{ aspectRatio: '16 / 10' }}>
                        <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                            <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                    />
                                </svg>
                            </div>
                        </div>
                        <div className='image-wrapper'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/display-assets/3d-shapes/glass-pink/w200/1.avif'
                            />
                        </div>
                    </div>
                    <div className='label-wrapper'>
                        <span className='footnote truncate'>Glass Pink</span>
                    </div>
                </div>
                {/* Plushy 形状按钮 */}
                <div
                    className={`panel-button undefined ${
                        activeShapeType === ShapeType.PLUSHY ? 'true-active' : 'false-active'
                    } has-label`}
                    onClick={() => handleShapeTypeChange(ShapeType.PLUSHY)}
                    style={{ cursor: 'pointer' }}
                >
                    <div className='preview' style={{ aspectRatio: '16 / 10' }}>
                        <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                            <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                    />
                                </svg>
                            </div>
                        </div>
                        <div className='image-wrapper'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/display-assets/3d-shapes/plushy/w200/1.avif'
                            />
                        </div>
                    </div>
                    <div className='label-wrapper'>
                        <span className='footnote truncate'>Plushy</span>
                    </div>
                </div>

                {/* Metal Dark 形状按钮 */}
                <div
                    className={`panel-button undefined ${
                        activeShapeType === ShapeType.METAL_DARK ? 'true-active' : 'false-active'
                    } has-label`}
                    onClick={() => handleShapeTypeChange(ShapeType.METAL_DARK)}
                    style={{ cursor: 'pointer' }}
                >
                    <div className='preview' style={{ aspectRatio: '16 / 10' }}>
                        <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                            <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                    />
                                </svg>
                            </div>
                        </div>
                        <div className='image-wrapper'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/display-assets/3d-shapes/metal-gradient-dark/w200/1.avif'
                            />
                        </div>
                    </div>
                    <div className='label-wrapper'>
                        <span className='footnote truncate'>Metal Dark</span>
                    </div>
                </div>

                {/* Metal Light 形状按钮 */}
                <div
                    className={`panel-button undefined ${
                        activeShapeType === ShapeType.METAL_LIGHT ? 'true-active' : 'false-active'
                    } has-label`}
                    onClick={() => handleShapeTypeChange(ShapeType.METAL_LIGHT)}
                    style={{ cursor: 'pointer' }}
                >
                    <div className='preview' style={{ aspectRatio: '16 / 10' }}>
                        <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                            <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                    />
                                </svg>
                            </div>
                        </div>
                        <div className='image-wrapper'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/display-assets/3d-shapes/metal-gradient-light/w200/1.avif'
                            />
                        </div>
                    </div>
                    <div className='label-wrapper'>
                        <span className='footnote truncate'>Metal Light</span>
                    </div>
                </div>

                {isMobile ? (
                    <>
                        {/* Inflatable 形状按钮 (移动端显示) */}
                        <div
                            className={`panel-button undefined ${
                                activeShapeType === ShapeType.INFLATABLE
                                    ? 'true-active'
                                    : 'false-active'
                            } has-label`}
                            onClick={() => handleShapeTypeChange(ShapeType.INFLATABLE)}
                            style={{ cursor: 'pointer' }}
                        >
                            <div className='preview' style={{ aspectRatio: '16 / 10' }}>
                                <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                                    <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className='image-wrapper'>
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        src='/display-assets/3d-shapes/inflatable/w200/1.avif'
                                    />
                                </div>
                            </div>
                            <div className='label-wrapper'>
                                <span className='footnote truncate'>Inflatable</span>
                            </div>
                        </div>

                        {/* Metal 形状按钮 (移动端显示) */}
                        <div
                            className={`panel-button undefined ${
                                activeShapeType === ShapeType.METAL ? 'true-active' : 'false-active'
                            } has-label`}
                            onClick={() => handleShapeTypeChange(ShapeType.METAL)}
                            style={{ cursor: 'pointer' }}
                        >
                            <div className='preview' style={{ aspectRatio: '16 / 10' }}>
                                <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                                    <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className='image-wrapper'>
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        src='/display-assets/3d-shapes/metal/w200/1.avif'
                                    />
                                </div>
                            </div>
                            <div className='label-wrapper'>
                                <span className='footnote truncate'>Metal</span>
                            </div>
                        </div>
                    </>
                ) : (
                    /* 桌面端更多选项按钮 (带 toggle 功能) */
                    <div
                        className='panel-button undefined undefined-active'
                        onClick={() => {
                            // 可以在这里添加更多形状的逻辑
                            console.log('更多形状选项被点击')
                        }}
                        style={{ cursor: 'pointer' }}
                    >
                        <div className='preview' style={{ aspectRatio: '16 / 10' }}>
                            <div className='icon'>
                                <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                >
                                    <path
                                        fill='currentColor'
                                        fillRule='evenodd'
                                        d='M1.29 12c0 1.188 1.017 2.16 2.262 2.16s2.244-.972 2.244-2.16-1-2.158-2.244-2.158-2.263.97-2.263 2.158zM12 14.16c-1.245 0-2.263-.972-2.263-2.16S10.755 9.842 12 9.842s2.244.97 2.244 2.158-1 2.16-2.244 2.16m8.448 0c-1.263 0-2.263-.972-2.263-2.16s1-2.158 2.263-2.158c1.245 0 2.244.97 2.244 2.158s-1 2.16-2.244 2.16'
                                        clipRule='evenodd'
                                    />
                                </svg>
                            </div>
                        </div>
                    </div>
                )}
            </>
        )
    }

    const publicShapesItems = () => {
        return (
            <>
                <div className='panel-button frame-shapes-scene-picker-button undefined-active '>
                    <div className='preview' style={{ aspectRatio: '5 / 1' }}>
                        <div className='rich-preview'>
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                            />
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                            />
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/display-assets/3d-shapes/glass-blue/w200/2.avif'
                            />
                            <img
                                crossOrigin='anonymous'
                                loading='lazy'
                                decoding='async'
                                src='/display-assets/3d-shapes/glass-blue/w200/6.avif'
                            />
                        </div>
                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                            <path
                                fill='currentColor'
                                d='M17.179 12.168a.95.95 0 0 0-.293-.692L9.644 4.281A.9.9 0 0 0 8.964 4c-.55 0-.972.41-.972.96 0 .258.117.504.281.692l6.551 6.516-6.551 6.515a1.05 1.05 0 0 0-.281.692c0 .55.422.96.972.96a.9.9 0 0 0 .68-.281l7.242-7.195a.95.95 0 0 0 .293-.691'
                            />
                        </svg>
                    </div>
                </div>
                <button
                    type='button'
                    className='button icon-button medium-button undefined-button undefined-blur undefined-round undefined-active bg-panel-dim'
                    style={{ flexDirection: 'row', minWidth: 'max-content' }}
                >
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <g fill='currentColor'>
                            <path d='M17.374 4.866v1.416h-1.352V4.941c0-1.05-.571-1.589-1.572-1.589H4.924c-1.016 0-1.572.539-1.572 1.589v9.492c0 1.05.556 1.589 1.572 1.589h1.701v1.352H4.9c-1.931 0-2.9-.961-2.9-2.866V4.866C2 2.961 2.969 2 4.9 2h9.574c1.922 0 2.9.968 2.9 2.866' />
                            <path d='M9.525 21.657h9.574c1.922 0 2.9-.969 2.9-2.867V9.148c0-1.897-.978-2.866-2.9-2.866H9.525c-1.931 0-2.9.961-2.9 2.866v9.642c0 1.905.969 2.867 2.9 2.867m.024-1.353c-1.016 0-1.571-.539-1.571-1.589V9.223c0-1.05.555-1.588 1.571-1.588h9.526c1.001 0 1.572.538 1.572 1.588v9.492c0 1.05-.571 1.589-1.572 1.589z' />
                            <path d='M10.524 11.516c.728.012 1.33-.59 1.33-1.343 0-.728-.602-1.318-1.33-1.318-.732 0-1.33.59-1.33 1.318 0 .753.598 1.331 1.33 1.343m7.58 0c.725.012 1.327-.59 1.327-1.343 0-.728-.602-1.318-1.327-1.318a1.33 1.33 0 0 0-1.334 1.318c0 .753.602 1.331 1.334 1.343m-3.792 3.799c.72 0 1.33-.602 1.33-1.346 0-.716-.61-1.318-1.33-1.318-.728 0-1.326.602-1.326 1.318 0 .744.598 1.346 1.326 1.346m-3.788 3.793c.728 0 1.326-.598 1.326-1.338a1.324 1.324 0 0 0-2.648 0c0 .74.59 1.338 1.322 1.338m7.58 0c.725 0 1.327-.598 1.327-1.338 0-.729-.602-1.318-1.327-1.318-.732 0-1.321.589-1.321 1.318 0 .74.589 1.338 1.321 1.338' />
                        </g>
                    </svg>
                </button>
            </>
        )
    }

    /**
     * 渲染形状布局选择
     * @description 渲染不同形状布局的选择按钮，支持点击切换
     * @returns JSX.Element 形状布局选择组件
     */
    const publicShapesLayout = () => {
        return (
            <>
                {/* 布局选项 1 */}
                <div
                    className={`panel-button undefined ${
                        activeShapeLayoutIndex === 0 ? 'true-active' : 'false-active'
                    }`}
                    style={{ width: 100, cursor: 'pointer' }}
                    onClick={() => handleShapeLayoutToggle(0)}
                >
                    <div className='preview' style={{ aspectRatio: '16 / 9' }}>
                        <div className='frame-shapes-scene-layout-preview'>
                            <div className='display'>
                                <div className='preview-wrapper'>
                                    <div className='item-card' />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/12.avif'
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            transform:
                                                'scale(1.2) translateX(-50%) translateY(-50%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/8.avif'
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            right: 0,
                                            transform:
                                                'scale(0.8) translateX(70%) translateY(-50%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                                        style={{
                                            position: 'absolute',
                                            right: 0,
                                            bottom: 0,
                                            transform:
                                                'scale(0.7) translateX(100%) translateY(20%) rotate(-15deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -2,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                                        style={{
                                            position: 'absolute',
                                            bottom: 0,
                                            left: 0,
                                            transform:
                                                'scale(1.6) translateX(-60%) translateY(20%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: 1,
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* 布局选项 2 */}
                <div
                    className={`panel-button undefined ${
                        activeShapeLayoutIndex === 1 ? 'true-active' : 'false-active'
                    }`}
                    style={{ width: 100, cursor: 'pointer' }}
                    onClick={() => handleShapeLayoutToggle(1)}
                >
                    <div className='preview' style={{ aspectRatio: '16 / 9' }}>
                        <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                            <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                    />
                                </svg>
                            </div>
                        </div>
                        <div className='frame-shapes-scene-layout-preview'>
                            <div className='display'>
                                <div className='preview-wrapper'>
                                    <div className='item-card' />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/12.avif'
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            transform:
                                                'scale(1) translateX(-60%) translateY(-50%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/8.avif'
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            transform:
                                                'scale(0.5) translateX(80%) translateY(-120%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            right: 0,
                                            transform:
                                                'scale(1.2) translateX(40%) translateY(-20%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                                        style={{
                                            position: 'absolute',
                                            right: 0,
                                            bottom: 0,
                                            transform:
                                                'scale(0.7) translateX(80%) translateY(80%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: 2,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/3.avif'
                                        style={{
                                            position: 'absolute',
                                            bottom: 0,
                                            left: 0,
                                            transform:
                                                'scale(0.8) translateX(-20%) translateY(80%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: 1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/7.avif'
                                        style={{
                                            position: 'absolute',
                                            bottom: 0,
                                            left: 0,
                                            transform:
                                                'scale(0.6) translateX(-100%) translateY(10%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -1,
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className='panel-button undefined false-active ' style={{ width: 100 }}>
                    <div className='preview' style={{ aspectRatio: '16 / 9' }}>
                        <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                            <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                    />
                                </svg>
                            </div>
                        </div>
                        <div className='frame-shapes-scene-layout-preview'>
                            <div className='display'>
                                <div className='preview-wrapper'>
                                    <div className='item-card' />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/12.avif'
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            right: 0,
                                            transform:
                                                'scale(0.5) translateX(10%) translateY(-110%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: 1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/8.avif'
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            right: 0,
                                            transform:
                                                'scale(0.35) translateX(190%) translateY(-70%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                                        style={{
                                            position: 'absolute',
                                            right: 0,
                                            bottom: 0,
                                            transform:
                                                'scale(0.7) translateX(80%) translateY(-40%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                                        style={{
                                            position: 'absolute',
                                            bottom: 0,
                                            left: 0,
                                            transform:
                                                'scale(0.5) translateX(90%) translateY(110%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: 1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/3.avif'
                                        style={{
                                            position: 'absolute',
                                            bottom: 0,
                                            left: 0,
                                            transform:
                                                'scale(1.05) translateX(-45%) translateY(45%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: -1,
                                        }}
                                    />
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        className='shape-scene-object'
                                        src='/display-assets/3d-shapes/glass-blue/w200/7.avif'
                                        style={{
                                            position: 'absolute',
                                            bottom: 0,
                                            left: 0,
                                            transform:
                                                'scale(0.35) translateX(-160%) translateY(-80%) rotate(0deg)',
                                            filter: 'blur(0em)',
                                            zIndex: 1,
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {isMobile ? (
                    <>
                        <div
                            className='panel-button undefined false-active '
                            style={{ width: 100 }}
                        >
                            <div className='preview' style={{ aspectRatio: '16 / 9' }}>
                                <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                                    <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className='frame-shapes-scene-layout-preview'>
                                    <div className='display'>
                                        <div className='preview-wrapper'>
                                            <div className='item-card' />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/12.avif'
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(0.8) translateX(-60%) translateY(-60%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: -1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/8.avif'
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(1) translateX(100%) translateY(-60%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: -1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    right: 0,
                                                    transform:
                                                        'scale(0.7) translateX(100%) translateY(15%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                                                style={{
                                                    position: 'absolute',
                                                    right: 0,
                                                    bottom: 0,
                                                    transform:
                                                        'scale(1.2) translateX(40%) translateY(40%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: -1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/3.avif'
                                                style={{
                                                    position: 'absolute',
                                                    bottom: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(1.1) translateX(-50%) translateY(50%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/7.avif'
                                                style={{
                                                    position: 'absolute',
                                                    bottom: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(0.5) translateX(-130%) translateY(-50%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            className='panel-button undefined false-active '
                            style={{ width: 100 }}
                        >
                            <div className='preview' style={{ aspectRatio: '16 / 9' }}>
                                <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                                    <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className='frame-shapes-scene-layout-preview'>
                                    <div className='display'>
                                        <div className='preview-wrapper'>
                                            <div className='item-card' />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/12.avif'
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(0.6) translateX(20%) translateY(-80%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: -1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/8.avif'
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    right: 0,
                                                    transform:
                                                        'scale(1.3) translateX(20%) translateY(-40%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: -1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                                                style={{
                                                    position: 'absolute',
                                                    right: 0,
                                                    bottom: 0,
                                                    transform:
                                                        'scale(0.35) translateX(145%) translateY(0%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                                                style={{
                                                    position: 'absolute',
                                                    right: 0,
                                                    bottom: 0,
                                                    transform:
                                                        'scale(0.7) translateX(0%) translateY(85%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/3.avif'
                                                style={{
                                                    position: 'absolute',
                                                    bottom: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(0.8) translateX(-50%) translateY(80%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: -1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/7.avif'
                                                style={{
                                                    position: 'absolute',
                                                    bottom: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(0.4) translateX(-140%) translateY(-50%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            className='panel-button undefined false-active '
                            style={{ width: 100 }}
                        >
                            <div className='preview' style={{ aspectRatio: '16 / 9' }}>
                                <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                                    <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className='frame-shapes-scene-layout-preview'>
                                    <div className='display'>
                                        <div className='preview-wrapper'>
                                            <div className='item-card' />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/12.avif'
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(0.65) translateX(-130%) translateY(-30%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: -1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/8.avif'
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    right: 0,
                                                    transform:
                                                        'scale(0.75) translateX(80%) translateY(-40%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                                                style={{
                                                    position: 'absolute',
                                                    right: 0,
                                                    bottom: 0,
                                                    transform:
                                                        'scale(0.6) translateX(140%) translateY(0%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: -1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                                                style={{
                                                    position: 'absolute',
                                                    bottom: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(0.9) translateX(-80%) translateY(0%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            className='panel-button undefined false-active '
                            style={{ width: 100 }}
                        >
                            <div className='preview' style={{ aspectRatio: '16 / 9' }}>
                                <div className='plus-badge-wrapper' style={{ padding: 3 }}>
                                    <div className='plus-badge' style={{ width: 16, height: 16 }}>
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M2 12.001a2.21 2.21 0 0 0 2.212 2.213h5.576v5.576a2.21 2.21 0 1 0 4.423 0v-5.576h5.579A2.21 2.21 0 0 0 22 12.001c0-1.223-.99-2.213-2.21-2.213h-5.579V4.212A2.21 2.21 0 0 0 12.001 2a2.213 2.213 0 0 0-2.213 2.212v5.576H4.212c-1.223 0-2.212.99-2.212 2.213'
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className='frame-shapes-scene-layout-preview'>
                                    <div className='display'>
                                        <div className='preview-wrapper'>
                                            <div className='item-card' />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/12.avif'
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(0.6) translateX(-140%) translateY(10%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/8.avif'
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    right: 0,
                                                    transform:
                                                        'scale(1.1) translateX(40%) translateY(-50%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: -1,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/5.avif'
                                                style={{
                                                    position: 'absolute',
                                                    right: 0,
                                                    bottom: 0,
                                                    transform:
                                                        'scale(1.6) translateX(40%) translateY(50%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 2,
                                                }}
                                            />
                                            <img
                                                crossOrigin='anonymous'
                                                loading='lazy'
                                                decoding='async'
                                                className='shape-scene-object'
                                                src='/display-assets/3d-shapes/glass-blue/w200/1.avif'
                                                style={{
                                                    position: 'absolute',
                                                    bottom: 0,
                                                    left: 0,
                                                    transform:
                                                        'scale(0.8) translateX(-60%) translateY(50%) rotate(0deg)',
                                                    filter: 'blur(0em)',
                                                    zIndex: 1,
                                                }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </>
                ) : (
                    <div className='panel-button undefined undefined-active '>
                        <div className='preview' style={{ aspectRatio: '16 / 10' }}>
                            <div className='icon'>
                                <svg
                                    xmlns='http://www.w3.org/2000/svg'
                                    fill='none'
                                    viewBox='0 0 24 24'
                                >
                                    <path
                                        fill='currentColor'
                                        fillRule='evenodd'
                                        d='M1.29 12c0 1.188 1.017 2.16 2.262 2.16s2.244-.972 2.244-2.16-1-2.158-2.244-2.158-2.263.97-2.263 2.158zM12 14.16c-1.245 0-2.263-.972-2.263-2.16S10.755 9.842 12 9.842s2.244.97 2.244 2.158-1 2.16-2.244 2.16m8.448 0c-1.263 0-2.263-.972-2.263-2.16s1-2.158 2.263-2.158c1.245 0 2.244.97 2.244 2.158s-1 2.16-2.244 2.16'
                                        clipRule='evenodd'
                                    />
                                </svg>
                            </div>
                        </div>
                    </div>
                )}
            </>
        )
    }
    /**
     * 渲染移动端场景内容
     * @description 移动端模态框形式显示场景选择和控制内容
     * @returns JSX.Element 移动端场景组件
     */
    const mobileInViewRender = () => {
        /**
         * 渲染移动端标签页内容区域
         * @description 根据当前激活的标签页类型显示对应的移动端内容
         * @returns JSX.Element | null 标签页内容或空
         */
        const renderMobileTabContent = () => {
            if (sceneType === SceneTabType.NONE) {
                // 无场景选择时不显示额外内容
                return null
            } else if (sceneType === SceneTabType.SHADOW) {
                // 阴影场景选择时显示阴影相关控制
                return (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 20 }} className=''>
                        <div className='panel-control-stack'>
                            <div className='stack-content'>{publicShadowType()}</div>
                        </div>
                        {publicShadowOptions()}
                    </div>
                )
            } else if (sceneType === SceneTabType.SHAPES) {
                // 形状场景选择时显示形状相关控制
                return (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 28 }}>
                        <div className='panel-control-stack'>
                            <div className='stack-content'>{publicShapesType()}</div>
                        </div>
                        <div className='panel-control-stack'>
                            <div className='stack-content'>{publicShapesLayout()}</div>
                        </div>
                    </div>
                )
            } else {
                // 未知标签页类型时抛出错误
                throw new Error(`未知的移动端场景标签页类型: ${sceneType}`)
            }
        }

        return (
            <div
                className='modal-container '
                style={{ background: 'transparent', justifyContent: 'center', opacity: 1 }}
            >
                <div
                    className='modal-sheet sheet-type shots-mobile-ui'
                    style={{
                        width: 'max-content',
                        maxWidth: 'max-content',
                        height: 'auto',
                        transform: 'none',
                    }}
                >
                    {/* 模态框标题栏 */}
                    <div className='modal-head modal-title-bar'>
                        <h4>Scene</h4>
                        <button
                            onClick={() => handleClose()}
                            type='button'
                            className='button icon-button small-button secondary-button undefined-blur true-round undefined-active undefined'
                            style={{ flexDirection: 'row' }}
                        >
                            <svg
                                xmlns='http://www.w3.org/2000/svg'
                                fill='currentColor'
                                viewBox='0 0 24 24'
                            >
                                <path d='M4.362 17.793c-.48.48-.49 1.332.01 1.831.51.5 1.361.49 1.832.02L12 13.846l5.788 5.788c.49.49 1.332.49 1.831-.01.5-.51.5-1.341.01-1.831l-5.788-5.788 5.788-5.798c.49-.49.5-1.332-.01-1.831-.499-.5-1.341-.5-1.83-.01L12 10.154 6.204 4.366c-.47-.48-1.332-.5-1.832.01-.5.5-.49 1.361-.01 1.831l5.788 5.798z' />
                            </svg>
                        </button>
                    </div>

                    {/* 模态框内容区域 */}
                    <div id='modalScrollView' className='modal-scroll-view'>
                        <div className='content-view undefined'>
                            {/* 场景选择标签页 */}
                            <div
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-evenly',
                                    marginBottom: 8,
                                }}
                            >
                                {publicRender()}
                            </div>

                            {/* 根据选中的标签页动态渲染移动端内容 */}
                            {renderMobileTabContent()}
                        </div>
                    </div>
                </div>
            </div>
        )
    }
    /**
     * 渲染桌面端场景内容
     * @description 根据选中的标签页显示相应的控制内容
     * @returns JSX.Element 桌面端场景组件
     */
    const pcInViewRender = () => {
        /**
         * 渲染标签页内容区域
         * @description 根据当前激活的标签页类型显示对应内容
         * @returns JSX.Element | null 标签页内容或空
         */
        const renderTabContent = () => {
            if (sceneType === SceneTabType.NONE) {
                // 无场景选择时不显示额外内容
                return null
            } else if (sceneType === SceneTabType.SHADOW) {
                // 阴影场景选择时显示阴影相关控制
                return (
                    <>
                        {/* 阴影类型选择 */}
                        <div className='panel-control undefined '>
                            <span className='label gray-text'>shadow type</span>
                            <div className='controls'>
                                <div className='panel-control-grid col-3'>{publicShadowType()}</div>
                            </div>
                        </div>

                        {/* 阴影选项设置 */}
                        <div className='panel-control undefined '>
                            <span className='label gray-text'>shadow options</span>
                            <div className='controls'>{publicShadowOptions()}</div>
                        </div>
                    </>
                )
            } else if (sceneType === SceneTabType.SHAPES) {
                // 形状场景选择时显示形状相关控制
                return (
                    <>
                        {/* 形状类型选择 */}
                        <div className='panel-control undefined '>
                            <span className='label gray-text'>shapes type</span>
                            <div className='controls'>
                                <div className='panel-control-grid col-3'>{publicShapesType()}</div>
                            </div>
                        </div>

                        {/* 形状项目选择 */}
                        <div className='panel-control undefined '>
                            <span className='label gray-text'>shapes items</span>
                            <div className='controls'>
                                <div style={{ display: 'flex', gap: 6 }}>{publicShapesItems()}</div>
                            </div>
                        </div>

                        {/* 形状布局选择 */}
                        <div className='panel-control undefined '>
                            <span className='label gray-text'>shapes layout</span>
                            <div className='panel-control-grid col-2'>{publicShapesLayout()}</div>
                        </div>
                    </>
                )
            } else {
                // 未知标签页类型时抛出错误
                throw new Error(`未知的场景标签页类型: ${sceneType}`)
            }
        }

        return (
            <>
                {/* 场景选择标签页 */}
                <div className='panel-control undefined '>
                    <span className='label gray-text'>scene</span>
                    <div className='controls'>
                        <div className='panel-control-grid col-3'>{publicRender()}</div>
                    </div>
                </div>

                {/* 根据选中的标签页动态渲染内容 */}
                {renderTabContent()}
            </>
        )
    }
    /**
     * 根据设备类型渲染相应的场景组件
     * @description 移动端显示模态框形式，桌面端显示面板形式
     */
    if (useIsMobile()) {
        return mobileInViewRender()
    } else {
        return pcInViewRender()
    }
}

export default Public_FrameScene
