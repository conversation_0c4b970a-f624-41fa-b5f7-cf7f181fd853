# 流程：

需求：拖拽上传图片需求逻辑，但是不需要实现代码 拖拽库：@https://react-dropzone.js.org/#section-basic-example @https://github.com/react-dropzone/react-dropzone 流程：1：@Public_MediaPickerModal.tsx class='media-item new-assetmedia-item new-asset'为点击用户选择上传图片、class=media-item is-landscape是上传后的图片展示，class=media-item is-portrait是上传中的图片、并且需要展示上传中进度条，class=button icon-button tiny-button secondary-button undefined-blur true-round undefined-active remove-button点击可移除上传过的图片 2：@PcMockup_Media.tsx className='device-item ' 第一个设备项目 - 图片预览区域 使用户上传过的图片，className='device-item ' 第二个设备项目 - 空状态展示区域，为点击用户选择上传图片, useSate=dropActive 当用户拖拽图片并且放置到 class=device-item元素上展示 放置后上传图片 3: @DisplayContainer.tsx 当用户拖拽图片并且放置到 className='empty-state dropping-state'展示 opacity更新1 并且、当图片放置后上传图片
