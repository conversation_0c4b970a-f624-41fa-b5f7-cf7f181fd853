/**
 * @fileoverview 图片管理系统统一配置文件
 * @description 集中管理所有图片相关的配置常量，避免重复定义和配置不一致
 *
 * 配置分类：
 * - 🎯 文件类型：仅支持 JPG 和 PNG 格式
 * - 📏 文件大小：上传文件的大小限制
 * - ⚡ 并发控制：批次处理和并发上传配置
 * - 🔧 HTML属性：文件输入控件的accept属性
 * - 📱 设备限制：设备相关的配置
 * - 🚨 错误提示：使用 sonner 显示用户友好的错误信息
 *
 * 设计原则：
 * - 单一数据源：所有配置集中管理
 * - 类型安全：使用TypeScript确保类型正确
 * - 严格验证：仅支持 JPG 和 PNG 格式
 * - 用户体验：使用 sonner 提供清晰的错误提示
 *
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2025.01
 */

import { toast } from 'sonner'

// ==================== 文件类型配置 ====================

/**
 * @enum ImageMimeType
 * @description 支持的图片MIME类型枚举
 *
 * 仅支持的图片格式：
 * - PNG: 无损压缩，支持透明度
 * - JPEG: 有损压缩，适合照片
 * - JPG: JPEG的别名格式
 *
 * 注意：系统已限制只支持 JPG 和 PNG 格式，其他格式将被拒绝并显示错误提示
 */
export enum ImageMimeType {
    PNG = 'image/png',
    JPEG = 'image/jpeg',
    JPG = 'image/jpg',
}

/**
 * @constant SUPPORTED_IMAGE_TYPES
 * @description 系统支持的标准图片类型数组
 *
 * 仅包含允许的图片格式：
 * - PNG: 无损压缩，支持透明度
 * - JPEG: 有损压缩，适合照片
 * - JPG: JPEG的别名
 *
 * 用途：
 * - 文件验证时的类型检查
 * - 拖拽上传的类型过滤
 * - 错误提示中的格式说明
 */
export const SUPPORTED_IMAGE_TYPES = [
    ImageMimeType.PNG,
    ImageMimeType.JPEG,
    ImageMimeType.JPG,
] as const

/**
 * @constant EXTENDED_IMAGE_TYPES
 * @description 扩展支持的图片类型（与标准类型相同，仅支持 JPG 和 PNG）
 *
 * 用途：
 * - 保持API兼容性
 * - 粘贴上传等特殊场景
 * - 统一的图片类型验证
 *
 * 注意：现在与 SUPPORTED_IMAGE_TYPES 完全相同，仅支持 JPG 和 PNG
 */
export const EXTENDED_IMAGE_TYPES = [...SUPPORTED_IMAGE_TYPES] as const

/**
 * @constant LEGACY_IMAGE_TYPES
 * @description 兼容旧版本的图片类型定义（仅支持 JPG 和 PNG）
 *
 * 用途：
 * - 兼容旧版本的文件输入控件
 * - 支持某些特殊的MIME类型变体
 * - 确保最大的浏览器兼容性
 *
 * 注意：仅包含 JPG 和 PNG 相关的类型
 */
export const LEGACY_IMAGE_TYPES = ['image/x-png', 'image/png', 'image/jpeg'] as const

// ==================== 文件大小配置 ====================

/**
 * @constant FILE_SIZE_LIMITS
 * @description 文件大小限制配置对象
 *
 * 配置说明：
 * - 使用字节为单位进行精确控制
 * - 提供不同场景的大小限制
 * - 包含可读性好的标签说明
 */
export const FILE_SIZE_LIMITS = {
    /** 默认最大文件大小：10MB */
    DEFAULT_MAX_SIZE: 10 * 1024 * 1024,

    /** 小文件限制：2MB（用于头像等） */
    SMALL_FILE_MAX_SIZE: 2 * 1024 * 1024,

    /** 大文件限制：50MB（用于高分辨率图片） */
    LARGE_FILE_MAX_SIZE: 50 * 1024 * 1024,

    /** 超大文件限制：100MB（用于专业用途） */
    HUGE_FILE_MAX_SIZE: 100 * 1024 * 1024,
} as const

/**
 * @constant FILE_SIZE_LABELS
 * @description 文件大小的可读性标签
 *
 * 用途：
 * - 用户界面的大小显示
 * - 错误提示中的大小说明
 * - 配置界面的选项标签
 */
export const FILE_SIZE_LABELS = {
    [FILE_SIZE_LIMITS.DEFAULT_MAX_SIZE]: '10MB',
    [FILE_SIZE_LIMITS.SMALL_FILE_MAX_SIZE]: '2MB',
    [FILE_SIZE_LIMITS.LARGE_FILE_MAX_SIZE]: '50MB',
    [FILE_SIZE_LIMITS.HUGE_FILE_MAX_SIZE]: '100MB',
} as const

// ==================== 并发控制配置 ====================

/**
 * @constant UPLOAD_CONCURRENCY_CONFIG
 * @description 上传并发控制配置
 *
 * 性能优化配置：
 * - 平衡上传速度和系统资源
 * - 避免过多并发请求导致的性能问题
 * - 提供不同场景的配置选项
 */
export const UPLOAD_CONCURRENCY_CONFIG = {
    /** 默认最大并发上传数：3个 */
    DEFAULT_MAX_CONCURRENT: 3,

    /** 每个批次的文件数量：5个 */
    DEFAULT_BATCH_SIZE: 5,

    /** 低性能设备的并发数：1个 */
    LOW_PERFORMANCE_CONCURRENT: 1,

    /** 高性能设备的并发数：6个 */
    HIGH_PERFORMANCE_CONCURRENT: 6,

    /** 小批次大小：3个 */
    SMALL_BATCH_SIZE: 3,

    /** 大批次大小：10个 */
    LARGE_BATCH_SIZE: 10,
} as const

// ==================== HTML属性配置 ====================

/**
 * @constant HTML_ACCEPT_ATTRIBUTES
 * @description HTML文件输入控件的accept属性值
 *
 * 用途：
 * - input[type="file"]的accept属性
 * - 文件选择对话框的过滤器
 * - 拖拽区域的文件类型限制
 */
export const HTML_ACCEPT_ATTRIBUTES = {
    /** 标准图片类型的accept属性 */
    STANDARD_IMAGES: SUPPORTED_IMAGE_TYPES.join(','),

    /** 扩展图片类型的accept属性 */
    EXTENDED_IMAGES: EXTENDED_IMAGE_TYPES.join(','),

    /** 兼容旧版本的accept属性 */
    LEGACY_IMAGES: LEGACY_IMAGE_TYPES.join(','),

    /** 所有图片类型的通用accept属性 */
    ALL_IMAGES: 'image/*',
} as const

// ==================== 设备配置 ====================

/**
 * @constant DEVICE_CONFIG
 * @description 设备相关的配置常量
 *
 * 设备管理配置：
 * - 支持的最大设备数量
 * - 设备索引的有效范围
 * - 设备相关的默认值
 */
export const DEVICE_CONFIG = {
    /** 支持的最大设备数量 */
    MAX_DEVICES: 3,

    /** 最小设备数量 */
    MIN_DEVICES: 1,

    /** 默认设备索引 */
    DEFAULT_DEVICE_INDEX: 0,

    /** 设备索引的有效范围 */
    VALID_DEVICE_INDEXES: [0, 1, 2] as const,
} as const

// ==================== 错误消息配置 ====================

/**
 * @constant ERROR_MESSAGES
 * @description 标准化的错误消息配置
 *
 * 用途：
 * - 用户友好的错误提示
 * - 多语言支持的基础
 * - 统一的错误处理体验
 */
export const ERROR_MESSAGES = {
    /** 不支持的文件类型 */
    UNSUPPORTED_FILE_TYPE: '不支持的文件类型，仅支持 JPG 和 PNG 格式',

    /** 文件大小超过限制 */
    FILE_SIZE_EXCEEDED: '文件大小超过限制: {size}，最大允许: {maxSize}',

    /** 设备索引无效 */
    INVALID_DEVICE_INDEX: '无效的设备索引: {index}，有效范围: {range}',

    /** 图片处理失败 */
    IMAGE_PROCESSING_FAILED: '图片处理失败: {fileName}',

    /** 上传失败 */
    UPLOAD_FAILED: '上传失败，请重试',

    /** 网络错误 */
    NETWORK_ERROR: '网络连接失败，请检查网络设置',
} as const

// ==================== 工具函数 ====================

/**
 * @function isValidImageType
 * @description 检查文件类型是否为有效的图片类型
 *
 * @param {string} mimeType 要检查的MIME类型
 * @param {readonly string[]} allowedTypes 允许的类型列表，默认为标准图片类型
 * @returns {boolean} 是否为有效的图片类型
 */
export const isValidImageType = (
    mimeType: string,
    allowedTypes: readonly string[] = SUPPORTED_IMAGE_TYPES,
): boolean => {
    return allowedTypes.includes(mimeType as any)
}

/**
 * @function isValidFileSize
 * @description 检查文件大小是否在允许范围内
 *
 * @param {number} fileSize 文件大小（字节）
 * @param {number} maxSize 最大允许大小，默认为10MB
 * @returns {boolean} 文件大小是否有效
 */
export const isValidFileSize = (
    fileSize: number,
    maxSize: number = FILE_SIZE_LIMITS.DEFAULT_MAX_SIZE,
): boolean => {
    return fileSize > 0 && fileSize <= maxSize
}

/**
 * @function isValidDeviceIndex
 * @description 检查设备索引是否有效
 *
 * @param {number} deviceIndex 设备索引
 * @returns {boolean} 设备索引是否有效
 */
export const isValidDeviceIndex = (deviceIndex: number): boolean => {
    return DEVICE_CONFIG.VALID_DEVICE_INDEXES.includes(deviceIndex as any)
}

/**
 * @function formatFileSize
 * @description 格式化文件大小为可读的字符串
 *
 * @param {number} bytes 文件大小（字节）
 * @returns {string} 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * @function getErrorMessage
 * @description 获取格式化的错误消息
 *
 * @param {keyof typeof ERROR_MESSAGES} messageKey 错误消息键
 * @param {Record<string, string>} params 消息参数
 * @returns {string} 格式化后的错误消息
 */
export const getErrorMessage = (
    messageKey: keyof typeof ERROR_MESSAGES,
    params: Record<string, string> = {},
): string => {
    let message: string = ERROR_MESSAGES[messageKey]

    // 替换消息中的参数占位符
    Object.entries(params).forEach(([key, value]) => {
        message = message.replace(new RegExp(`\\{${key}\\}`, 'g'), value)
    })

    return message
}

// ==================== 默认配置导出 ====================

/**
 * @constant DEFAULT_IMAGE_CONFIG
 * @description 默认的图片配置对象
 *
 * 用途：
 * - 组件的默认配置
 * - 配置的快速应用
 * - 向后兼容的配置接口
 */
export const DEFAULT_IMAGE_CONFIG = {
    /** 支持的图片类型 */
    allowedTypes: SUPPORTED_IMAGE_TYPES,

    /** 最大文件大小 */
    maxFileSize: FILE_SIZE_LIMITS.DEFAULT_MAX_SIZE,

    /** 最大并发上传数 */
    maxConcurrentUploads: UPLOAD_CONCURRENCY_CONFIG.DEFAULT_MAX_CONCURRENT,

    /** 批次大小 */
    batchSize: UPLOAD_CONCURRENCY_CONFIG.DEFAULT_BATCH_SIZE,

    /** HTML accept属性 */
    htmlAccept: HTML_ACCEPT_ATTRIBUTES.STANDARD_IMAGES,
} as const

/**
 * @constant EXTENDED_IMAGE_CONFIG
 * @description 扩展的图片配置对象（与标准配置相同，仅支持 JPG 和 PNG）
 *
 * 用途：
 * - 保持API兼容性
 * - 粘贴上传等特殊功能
 * - 统一的配置接口
 *
 * 注意：现在与 DEFAULT_IMAGE_CONFIG 完全相同，仅支持 JPG 和 PNG
 */
export const EXTENDED_IMAGE_CONFIG = {
    ...DEFAULT_IMAGE_CONFIG,
    allowedTypes: EXTENDED_IMAGE_TYPES,
    htmlAccept: HTML_ACCEPT_ATTRIBUTES.EXTENDED_IMAGES,
} as const

// ==================== 类型定义导出 ====================

/**
 * @type SupportedImageType
 * @description 支持的图片类型的联合类型
 */
export type SupportedImageType = (typeof SUPPORTED_IMAGE_TYPES)[number]

/**
 * @type ExtendedImageType
 * @description 扩展支持的图片类型的联合类型
 */
export type ExtendedImageType = (typeof EXTENDED_IMAGE_TYPES)[number]

/**
 * @type ValidDeviceIndex
 * @description 有效的设备索引类型
 */
export type ValidDeviceIndex = (typeof DEVICE_CONFIG.VALID_DEVICE_INDEXES)[number]

/**
 * @interface ImageValidationResult
 * @description 图片验证结果接口
 */
export interface ImageValidationResult {
    /** 是否验证通过 */
    isValid: boolean
    /** 错误消息（如果验证失败） */
    errorMessage?: string
    /** 错误类型 */
    errorType?: 'type' | 'size' | 'device' | 'unknown'
}

/**
 * @function validateImageFile
 * @description 综合验证图片文件，包含 sonner 错误提示和日志记录
 *
 * 验证流程：
 * 1. 检查文件类型是否为 JPG 或 PNG
 * 2. 检查文件大小是否在允许范围内
 * 3. 验证失败时显示 sonner 错误提示
 * 4. 记录详细的错误日志
 *
 * @param {File} file 要验证的文件
 * @param {object} options 验证选项
 * @param {boolean} options.showToast 是否显示错误提示，默认为 true
 * @returns {ImageValidationResult} 验证结果
 */
export const validateImageFile = (
    file: File,
    options: {
        allowedTypes?: readonly string[]
        maxFileSize?: number
        showToast?: boolean
    } = {},
): ImageValidationResult => {
    const {
        allowedTypes = SUPPORTED_IMAGE_TYPES,
        maxFileSize = FILE_SIZE_LIMITS.DEFAULT_MAX_SIZE,
        showToast = true,
    } = options

    // 检查文件类型
    if (!isValidImageType(file.type, allowedTypes)) {
        const errorMessage = ERROR_MESSAGES.UNSUPPORTED_FILE_TYPE

        // 🚨 显示 sonner 错误提示
        if (showToast) {
            toast.error(errorMessage, {
                description: `文件 "${file.name}" 的格式为 ${file.type || '未知'}，仅支持 JPG 和 PNG 格式`,
                duration: 5000,
            })
        }

        // 📝 记录详细错误日志
        console.error('❌ 图片类型验证失败:', {
            fileName: file.name,
            fileType: file.type,
            fileSize: formatFileSize(file.size),
            allowedTypes: allowedTypes,
            errorMessage: errorMessage,
            timestamp: new Date().toISOString(),
        })

        return {
            isValid: false,
            errorType: 'type',
            errorMessage: errorMessage,
        }
    }

    // 检查文件大小
    if (!isValidFileSize(file.size, maxFileSize)) {
        const errorMessage = getErrorMessage('FILE_SIZE_EXCEEDED', {
            size: formatFileSize(file.size),
            maxSize: formatFileSize(maxFileSize),
        })

        // 🚨 显示 sonner 错误提示
        if (showToast) {
            toast.error('文件大小超出限制', {
                description: `文件 "${file.name}" 大小为 ${formatFileSize(file.size)}，超过了 ${formatFileSize(maxFileSize)} 的限制`,
                duration: 5000,
            })
        }

        // 📝 记录详细错误日志
        console.error('❌ 图片大小验证失败:', {
            fileName: file.name,
            fileType: file.type,
            fileSize: formatFileSize(file.size),
            maxFileSize: formatFileSize(maxFileSize),
            errorMessage: errorMessage,
            timestamp: new Date().toISOString(),
        })

        return {
            isValid: false,
            errorType: 'size',
            errorMessage: errorMessage,
        }
    }

    // ✅ 验证通过的日志记录
    console.log('✅ 图片验证通过:', {
        fileName: file.name,
        fileType: file.type,
        fileSize: formatFileSize(file.size),
        timestamp: new Date().toISOString(),
    })

    return {
        isValid: true,
    }
}
