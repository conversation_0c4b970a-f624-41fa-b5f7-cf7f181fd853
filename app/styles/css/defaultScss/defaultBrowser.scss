/**
 * 禁用元素拖拽功能
 */
* {
    -webkit-user-drag: none;
}

/**
   * 设置HTML和BODY元素基础样式
   * - 占满整个视口
   * - 相对定位
   * - 禁用溢出和滚动行为
   */
html,
body {
    width: 100vw;
    height: 100vh;
    position: relative;
    overflow: hidden;
    touch-action: none !important; // 禁用触摸操作
    overscroll-behavior: none !important; // 禁用过度滚动行为
}

/**
   * 重置所有HTML元素及其伪元素的盒模型
   */
html *,
html :before,
html :after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/**
   * 设置BODY元素的基础样式
   * - 禁用文本选择
   * - 应用主题颜色
   * - 固定定位
   */
body {
    -webkit-user-select: none;
    user-select: none;
    background: rgba(var(--body), 1); // 使用主题变量设置背景色
    color: rgba(var(--primary), 1); // 使用主题变量设置文本颜色
    line-height: 0;
    top: 0;
    left: 0;
    position: fixed !important;
}

/**
   * 重置常用HTML元素的默认样式
   * - 移除点击高亮效果
   * - 移除轮廓线
   * - 重置边距和内边距
   * - 移除文本装饰
   */
body,
nav,
footer,
section,
div,
h1,
h2,
h3,
h4,
h5,
p,
a,
label,
ul,
li {
    -webkit-tap-highlight-color: #fff0; // 移除移动设备点击高亮效果
    outline: none;
    margin: 0;
    padding: 0;
    text-decoration: none;
}

/**
   * 移除列表项的默认样式
   */
li {
    list-style-type: none;
}

/**
   * 自定义滚动条样式
   */
::-webkit-scrollbar {
    width: 8px !important;
    height: 0 !important;
}

/**
   * 隐藏div元素的滚动条
   */
div::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
}

/**
   * 优化文本渲染
   * - 平滑字体渲染
   */
body {
    -moz-osx-font-smoothing: grayscale; // Firefox字体平滑渲染
    -webkit-font-smoothing: antialiased; // WebKit字体平滑渲染
}
