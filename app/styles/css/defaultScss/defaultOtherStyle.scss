.callout,
.toaster-default,
.toaster-error,
.toaster-success,
button,
.button {
    letter-spacing: -0.4px;
    font:
        500 15px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .callout,
    .toaster-default,
    .toaster-error,
    .toaster-success,
    button,
    .button {
        letter-spacing: -0.1px;
        font:
            500 15.5px/20px Inter,
            sans-serif;
    }
}
label,
.label,
.custom-frame form .custom-frame-input h6 {
    letter-spacing: 0.4px;
    text-transform: uppercase;
    font:
        600 11px/16px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    label,
    .label,
    .custom-frame form .custom-frame-input h6 {
        font:
            500 11px/16px Inter,
            sans-serif;
    }
}
p,
.body,
.multiline-input {
    letter-spacing: -0.3px;
    font:
        16px/22px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    p,
    .body,
    .multiline-input {
        letter-spacing: -0.2px;
        font:
            17px/22px Inter,
            sans-serif;
    }
}
.caption,
.about .members-list .member button,
.app-about .members-list .member button {
    letter-spacing: -0.2px;
    font:
        14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .caption,
    .about .members-list .member button,
    .app-about .members-list .member button {
        letter-spacing: -0.07px;
        font:
            13px/18px Inter,
            sans-serif;
    }
}
.caption2,
.tag,
button.default-button.tiny-button,
.button.default-button.tiny-button {
    letter-spacing: -0.2px;
    font:
        12.5px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .caption2,
    .tag,
    button.default-button.tiny-button,
    .button.default-button.tiny-button {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.footnote,
.frame-item,
.mock-style-3d-tag,
.mockup-details .row span,
.mockup-details .row p,
.tag.tag-small,
.slider-component .labels span {
    letter-spacing: -0.2px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .footnote,
    .frame-item,
    .mock-style-3d-tag,
    .mockup-details .row span,
    .mockup-details .row p,
    .tag.tag-small,
    .slider-component .labels span {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.font-weight-regular {
    font-weight: 400;
}
.font-weight-medium,
.mock-style-3d-tag,
.export-button .export-state > span,
.tag,
.subscription-view .sub-card-drawer .change-subscription .copy span,
button.default-button.small-button,
.button.default-button.small-button,
button.default-button.tiny-button,
.button.default-button.tiny-button {
    font-weight: 500;
}
.font-weight-semibold {
    font-weight: 600;
}
.bg-blur,
.global-resize-handle .move-control,
.unsplash-control .search-bar .input-box input,
.drag-pad-wrapper.zoom-pad .drag-pad .drag-handle:after,
.dropzone-edit-popover .content .media-list .new-asset,
.plus-badge-wrapper .plus-badge,
.plus-badge-wrapper .pro-badge,
.slideshow-component .nav-buttons.overlay-mode > *,
.toaster-default,
.toaster-error,
.toaster-success,
button.true-blur,
.button.true-blur {
    -webkit-backdrop-filter: blur(4px) saturate(150%);
    backdrop-filter: blur(4px) saturate(150%);
}
.bg-blur-hard,
.shots-admin .shots-admin-tabs,
.shots-admin .admin-system-tab .device-card,
.admin-feedback-item .item .message,
.subscriptions-tab .sub-user {
    -webkit-backdrop-filter: blur(40px) saturate(150%);
    backdrop-filter: blur(40px) saturate(150%);
}
.trans-display {
    transition: none !important;
}
.trans-100 {
    transition: all 0.1s;
}
.trans-200,
.timeline-component .timeline-track-wrapper .slide-duration-handle .handle-knob,
.timeline-component .timeline-track-wrapper .slide-duration-handle .hint,
.base-clip-item .clip,
.animation-clip-item .clip,
.video-clip-item .clip,
.base-clip-item .clip .clip-content,
.animation-clip-item .clip .clip-content,
.video-clip-item .clip .clip-content,
.base-clip-item .options-ornament-wrapper,
.animation-clip-item .options-ornament-wrapper,
.video-clip-item .options-ornament-wrapper,
.base-clip-item .timeline-clip-is-playing,
.animation-clip-item .timeline-clip-is-playing,
.video-clip-item .timeline-clip-is-playing,
.video-clip-item .image-media-preview,
.pause-clip-item .clip span,
.timeline-ghost-ticker .needle,
.timeline-track-ticker .needle,
.timeline-track-ticker .orb,
.magic-media-picker .magic-media-item,
.mock-assets-control .device-item .display-preview .safe-area .device-screen .overlay-plus,
.drag-pad-wrapper .drag-hint,
.drag-pad .drag-handle,
.layout-item,
.dropzone-edit-popover .content .media-list .media-item .media-safearea .media-display .media-icon,
.switch .switch-button p,
.switch .switch-button .visual .icon-wrapper > svg,
.switch .switch-button .visual .image-wrapper,
.panel-button .preview:after,
.shots-mobile-ui .panel-button,
.slider-component .labels span,
.slider-component .reset-icon,
.mockup-picker-mobile-modal .mock-item {
    transition: all 0.2s;
}
.trans-300,
.timeline-component .timeline-track-wrapper .timeline-track .track-label,
.timeline-component .timeline-track-wrapper .timeline-track .base-track,
.timeline-component .timeline-track-wrapper .timeline-track .animation-track,
.timeline-component .timeline-track-wrapper .timeline-track .video-track,
.mockup-track,
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .timeline-clip-preview,
.animation-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .timeline-clip-preview,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .timeline-clip-preview,
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame,
.animation-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame,
.magic-backs-empty-state .empty-state .previews .preview-image,
.drag-pad-wrapper.zoom-pad .drag-pad .drag-handle:after,
.drag-pad-wrapper.zoom-pad .viewfinder-div,
.drag-pad-wrapper.zoom-pad .ghost-viewfinder,
.drag-pad .drag-handle:after,
.drag-grid-tile,
.drag-grid-tile .preset-button .tile,
.layouts-panel-controls:before,
.layouts-panel-controls:after,
.layouts-panel-controls-top:before,
.layouts-panel-controls-top:after,
.layouts-panel-controls-top .transform-controls-wrapper .position-controls .zoom-panel-control,
.dropzone-edit-popover .content .media-list .media-item,
.app-main:after,
.banner-popover .content,
.create-animation-button .gradient-effect-wrapper .gradient-effect,
.create-animation-button .light-backdrop .gradient-effect,
.create-animation-button .gradient-effect-wrapper .light-effect,
.create-animation-button .light-backdrop .light-effect,
.animation-onboarding-container .animation-onboarding .tip-card {
    transition: all 0.3s;
}
.shadow-card {
    box-shadow: 0 12px 24px #00000052;
}
.border-right {
    box-shadow: inset -1px 0px 0px rgba(var(--primary), 0.06);
}
.border-light,
.banner-popover .close-icon,
.style-item .thumbnail .image-wrapper {
    box-shadow: inset 0px 0px 0px 1px rgba(var(--primary), 0.06) !important;
}
.border-light2 {
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
}
.border-light-after,
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .timeline-clip-preview,
.animation-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .timeline-clip-preview,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .timeline-clip-preview,
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame,
.animation-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame,
.layout-item,
.user-avatar,
.popover,
.slide-over {
    position: relative;
}
.border-light-after:after,
.base-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .timeline-clip-preview:after,
.animation-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .timeline-clip-preview:after,
.video-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .timeline-clip-preview:after,
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame:after,
.animation-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame:after,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame:after,
.layout-item:after,
.user-avatar:after,
.popover:after,
.slide-over:after {
    content: '';
    border-radius: inherit;
    pointer-events: none;
    border: solid 1px rgba(var(--primary), 0.06);
    z-index: 10;
    position: absolute;
    inset: 0;
}
.shots-effect {
    background: linear-gradient(120deg, #ff6432 25%, #ff0065 45%, #7b2eff 75%) 50% 100%/200%
        no-repeat;
    animation: 2s linear infinite alternate gradient;
}
.shots-text-effect {
    -moz-text-fill-color: #0000;
    background: linear-gradient(120deg, #ff6432 25%, #ff0065 45%, #7b2eff 75%) 50% 100%/200%
        no-repeat;
    -webkit-background-clip: text;
    background-clip: text;
    animation: 2s linear infinite alternate gradient;
}
.hoverlight {
    transition: background-color 0.2s;
}
.hoverlight:active {
    background: rgba(var(--primary), 0.12);
}
button,
.button {
    color: rgba(var(--primary), 1);
    text-align: center;
    cursor: pointer;
    -webkit-user-select: none;
    user-select: none;
    width: max-content;
    box-shadow: none;
    appearance: none;
    background: 0 0;
    border: none;
    outline: none;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 0;
    text-decoration: none;
    transition: all 0.2s;
    display: flex;
    position: relative;
    overflow: hidden;
}
button span,
.button span {
    color: inherit;
}
button > svg,
.button > svg {
    aspect-ratio: 1;
    width: 20px;
    color: inherit;
}
button.default-button.huge-button,
.button.default-button.huge-button {
    padding: 14px 16px;
}
button.default-button.large-button,
.button.default-button.large-button {
    padding: 10px 12px;
}
button.default-button.medium-button,
.button.default-button.medium-button {
    padding: 8px 10px;
}
button.default-button.small-button,
.button.default-button.small-button {
    padding: 7px 10px;
}
button.default-button.small-button svg,
.button.default-button.small-button svg {
    width: 18px;
}
button.default-button.tiny-button,
.button.default-button.tiny-button {
    padding: 3px 9px;
}
button.default-button.tiny-button svg,
.button.default-button.tiny-button svg {
    width: 16px;
}
button.icon-button.huge-button,
.button.icon-button.huge-button {
    padding: 14px;
}
button.icon-button.large-button,
.button.icon-button.large-button {
    padding: 10px;
}
button.icon-button.medium-button,
.button.icon-button.medium-button {
    padding: 8px;
}
button.icon-button.small-button,
.button.icon-button.small-button {
    padding: 7px;
}
button.icon-button.small-button svg,
.button.icon-button.small-button svg {
    width: 18px;
}
button.icon-button.tiny-button,
.button.icon-button.tiny-button {
    padding: 5px;
}
button.icon-button.tiny-button svg,
.button.icon-button.tiny-button svg {
    width: 16px;
}
button.huge-button,
.button.huge-button {
    border-radius: 14px;
}
button.large-button,
.button.large-button {
    border-radius: 12px;
}
button.medium-button,
.button.medium-button {
    border-radius: 10px;
}
button.small-button,
.button.small-button,
button.tiny-button,
.button.tiny-button {
    border-radius: 9px;
}
button.true-round,
.button.true-round {
    border-radius: 100px;
}
button .round,
.button .round {
    border-radius: 100px !important;
}
@media only screen and (width>=1200px) {
    button:hover,
    .button:hover {
        background: rgba(var(--primary), 0.06);
    }
}
@media only screen and (width>=0) and (width<=800px) {
    button:active,
    .button:active {
        opacity: 0.75;
        transform: scale(0.95);
    }
}
button.primary-button,
.button.primary-button {
    background: rgba(var(--primary), 1);
    color: rgba(var(--secondary), 1);
}
button.secondary-button,
.button.secondary-button {
    background: rgba(var(--primary), 0.06);
}
@media only screen and (width>=1200px) {
    button.secondary-button:hover,
    .button.secondary-button:hover {
        background: rgba(var(--primary), 0.12);
    }
}
button.danger-button,
.button.danger-button {
    background: rgba(var(--primary), 0.06);
    color: rgba(var(--danger), 1);
}
@media only screen and (width>=1200px) {
    button.danger-button:hover,
    .button.danger-button:hover {
        background: rgba(var(--primary), 0.12);
    }
}
button:disabled,
.button:disabled {
    opacity: 0.3;
    pointer-events: none;
}
button.true-active,
.button.true-active {
    background: rgba(var(--panel-active), 1);
}
button.true-active:before,
.button.true-active:before {
    display: none;
}
button.true-active:hover,
.button.true-active:hover {
    background: rgba(var(--panel-active), 1);
}
label {
    letter-spacing: 0.6px;
    text-transform: uppercase;
    color: #0000005c;
    padding: 8px 16px;
    font: 600 13px/20px sf-pro-rounded;
}
input [type='text'],
.input-text {
    --font: normal 16px/20px 'Inter', sans-serif;
    --padding: 7px 10px;
    --border-radius: 10px;
    font: var(--font);
    letter-spacing: -0.4px;
    padding: var(--padding);
    border-radius: var(--border-radius);
    background: rgba(var(--primary), 0.06);
    color: rgba(var(--primary), 1);
    border: 1px solid #0000;
    transition: all 0.2s;
}
input [type='text']::placeholder,
.input-text::placeholder {
    color: rgba(var(--primary), 0.36);
}
input [type='text']:focus,
input [type='text']:active,
.input-text:focus,
.input-text:active {
    outline: none;
}
@media only screen and (width>=0) and (width<=800px) {
    input [type='text'],
    .input-text {
        --padding: 10px 12px;
        background: rgba(var(--panel-dim), 1);
        border: none;
    }
    input [type='text']:focus,
    input [type='text']:active,
    .input-text:focus,
    .input-text:active {
        border: none;
        outline: none;
    }
}
.input-transparent {
    background: 0 0;
    border: 0;
    border-radius: 0;
    margin: 0;
    padding: 0;
}
.input-transparent:focus {
    box-shadow: none;
    background: 0 0;
    border: none;
    outline: none;
}
.input-transparent:active {
    background: 0 0;
    border: none;
}
.multiline-input {
    color: rgba(var(--primary), 1);
    background: 0 0;
    background: rgba(var(--primary), 0.06);
    border: 1px solid #0000;
    border-radius: 12px;
    width: 100%;
    padding: 10px 14px;
    transition: all 0.2s;
}
.multiline-input::placeholder {
    color: rgba(var(--primary), 0.36);
}
.multiline-input:focus,
.multiline-input:active {
    outline: none;
}
.input-box {
    position: relative;
}
.input-box input {
    background: rgba(var(--primary), 0.06);
    width: 100%;
}
.input-box.icon-true input {
    padding-left: 36px;
}
.input-box.disabled {
    pointer-events: none;
    opacity: 0.5;
}
.input-box .label-wrap {
    transition: all 0.3s;
    position: absolute;
    top: 8px;
    left: 16px;
}
.input-box .icon-wrap {
    aspect-ratio: 1;
    width: 20px;
    position: absolute;
    top: 8px;
    left: 10px;
}
.input-box .icon-wrap svg {
    opacity: 0.5;
    width: 100%;
    height: 100%;
}
.input-box.small-size input {
    --font: normal 14.5px/20px 'Inter', sans-serif;
    --padding: 8px 12px;
    --border-radius: 12px;
    background: rgba(var(--panel-dim), 1) !important;
}
.input-box.small-size .icon-wrap {
    width: 18px;
    top: 10px;
    left: 10px;
}
.select {
    color: rgba(var(--primary), 1);
    border: 1px solid rgba(var(--primary), 0.12);
    appearance: none;
    background: rgba(var(--primary), 0.06)
        url("data:image/svg+xml;utf8,<svg width='8' height='16' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M7.666 4.803c0 .292-.252.53-.565.53a.59.59 0 0 1-.411-.166L3.996 2.588 1.31 5.167a.606.606 0 0 1-.411.166c-.314 0-.566-.238-.566-.53a.48.48 0 0 1 .159-.368l3.052-2.908a.632.632 0 0 1 .911 0l3.049 2.908a.496.496 0 0 1 .162.368ZM.334 11.197c0-.291.252-.53.565-.53a.59.59 0 0 1 .411.167l2.694 2.578 2.686-2.579a.606.606 0 0 1 .411-.166c.314 0 .566.239.566.53a.48.48 0 0 1-.159.368l-3.052 2.908a.632.632 0 0 1-.911 0L.496 11.565a.496.496 0 0 1-.162-.368Z' fill='gray'/></svg>")
        no-repeat;
    background-position: right 12px top 50%;
    background-repeat: no-repeat;
    border-radius: 10px;
    padding: 10px 12px;
    transition: all 0.2s;
    position: relative;
}
.select::placeholder {
    color: rgba(var(--primary), 0.36);
}
.select:focus,
.select:active {
    outline: none;
}
.dropdown {
    position: relative;
    z-index: 1000 !important;
}
.dropdown .button-wrapper {
    z-index: 50;
    cursor: pointer;
    position: relative;
}
.dropdown .button-wrapper > * {
    pointer-events: none;
}
.dropdown .drop-menu {
    z-index: 100;
    background: rgba(var(--modal), 1);
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: 16px;
    flex-direction: column;
    padding: 6px;
    display: flex;
    position: absolute;
    box-shadow: 0 12px 24px #00000052;
}
@media only screen and (width>=0) and (width<=800px) {
    .dropdown .drop-menu {
        border-radius: 32px;
        gap: 4px;
        padding: 8px;
    }
    .dropdown .drop-menu .danger {
        background: rgba(var(--danger), 0.06);
    }
}
.tip {
    opacity: 1;
    position: relative;
    overflow: visible;
}
.tip .tooltip {
    background: rgba(var(--panel), 1);
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    width: max-content;
    color: rgba(var(--primary), 1);
    text-align: center;
    z-index: 900;
    visibility: hidden;
    opacity: 0;
    letter-spacing: -0.4px;
    border-radius: 20px;
    align-items: center;
    gap: 8px;
    padding: 5px 11px;
    font:
        500 14px/20px Inter,
        sans-serif;
    transition: opacity 0.2s;
    display: flex;
    position: absolute;
    box-shadow: 0 4px 8px #0000001a;
}
@media only screen and (width>=0) and (width<=800px) {
    .tip .tooltip {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
        display: none;
    }
}
.tip .tooltip .keys {
    align-items: center;
    gap: 4px;
    display: flex;
}
.tip .tooltip .keys span {
    text-transform: capitalize;
    color: #000;
    aspect-ratio: 1;
    background: #ffffffb3;
    border-radius: 4px;
    place-items: center;
    width: 16px;
    font:
        600 13px/13px Inter,
        sans-serif;
    display: grid;
    position: relative;
}
.tip .tooltip .keys span:before {
    content: '';
    background: rgba(var(--primary), 0.36);
    z-index: -1;
    border-radius: 5px;
    position: absolute;
    inset: -1px;
    transform: translateY(1px);
}
.tip:hover .tooltip {
    visibility: visible;
    opacity: 1;
    transition-delay: 0.5s;
}
.toaster-container {
    z-index: 99999 !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .toaster-container {
        margin-top: calc(env(safe-area-inset-top, 0) - 12px);
    }
}
.toaster-default,
.toaster-error,
.toaster-success {
    background: rgba(var(--modal), 0.88) !important;
    border-radius: 40px !important;
    padding: 10px 8px 10px 14px !important;
    box-shadow: 0 8px 20px #0000001a !important;
}
.toaster-default .icon,
.toaster-error .icon,
.toaster-success .icon {
    width: 24px;
    height: 24px;
}
.toaster-default > * {
    color: rgba(var(--primary), 1) !important;
}
.toaster-success > * {
    color: #34d159 !important;
}
.toaster-error > * {
    color: #fd483c !important;
}
.toaster-alert > * {
    color: #ffb730 !important;
}
.divide-line {
    background: rgba(var(--primary), 0.06);
    width: 100%;
    height: 1px;
}
.modal-container {
    z-index: 9999;
    background: #00000052;
    justify-content: center;
    align-items: center;
    display: flex;
    position: fixed;
    inset: 0;
}
@media only screen and (width>=0) and (width<=800px) {
    .modal-container {
        align-items: flex-end;
    }
}
.modal-sheet {
    background: rgba(var(--panel), 1);
    box-shadow:
        inset 0px 0px 0px 1px rgba(var(--primary), 0.03),
        0px 16px 32px #0000003d;
    border-radius: 24px;
    position: relative;
    overflow: hidden;
}
@media only screen and (width>=0) and (width<=800px) {
    .modal-sheet {
        max-width: 100% !important;
    }
    .modal-sheet.modal-type {
        margin-bottom: calc(8px + (env(safe-area-inset-bottom, 16px)));
        width: calc(100vw - 16px) !important;
    }
    .modal-sheet.sheet-type,
    .modal-sheet.full-sheet-type {
        box-shadow: none;
        border-radius: 28px 28px 0 0;
        flex-direction: column;
        display: flex;
        width: 100vw !important;
        max-height: calc(98vh - env(safe-area-inset-top, 0)) !important;
    }
    .modal-sheet.sheet-type .modal-scroll-view .content-view,
    .modal-sheet.full-sheet-type .modal-scroll-view .content-view {
        padding-bottom: calc(16px + (env(safe-area-inset-bottom, 16px)));
    }
    .modal-sheet.full-sheet-type {
        height: 100% !important;
    }
    .modal-sheet.fullscreen-type {
        box-shadow: none;
        background: rgba(var(--modal), 1);
        border-radius: 0;
        width: 100vw !important;
        height: 100% !important;
        max-height: 100vh !important;
    }
    .modal-sheet.fullscreen-type .modal-title-bar {
        padding-top: calc(6px + env(safe-area-inset-top, 16px));
        background: rgba(var(--modal), 0.8);
    }
    .modal-sheet.fullscreen-type .floating-close {
        top: env(safe-area-inset-top, 16px) !important;
    }
    .modal-sheet.fullscreen-type .modal-scroll-view .content-view {
        padding-top: calc(56px + (env(safe-area-inset-top, 16px))) !important;
        padding-bottom: calc(16px + (env(safe-area-inset-bottom, 16px))) !important;
    }
}
.modal-head {
    z-index: 10;
    transition: all 0.2s;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}
.modal-head.modal-title-bar {
    justify-content: space-between;
    align-items: center;
    padding: 12px 12px 12px 16px;
    display: flex;
}
.modal-head.floating-close {
    top: 14px;
    right: 14px;
    left: unset;
    z-index: 10;
    border-radius: 50%;
    overflow: hidden;
}
.modal-head.floating-close button {
    background: rgba(var(--primary), 0.12);
}
.modal-head.scrolled {
    -webkit-backdrop-filter: blur(15px);
    backdrop-filter: blur(15px);
}
.modal-head.scrolled.modal-title-bar {
    background: rgba(var(--modal), 0.8);
}
.modal-head.scrolled.modal-title-bar:before {
    content: '';
    inset: 0;
    top: unset;
    background: rgba(var(--primary), 0.06);
    height: 1px;
    transition: inherit;
    position: absolute;
}
.modal-scroll-view {
    width: 100%;
    height: max-content;
    max-height: 100%;
    position: relative;
    overflow: hidden auto;
}
.modal-scroll-view .content-view {
    flex-direction: column;
    gap: 16px;
    height: max-content;
    padding: 64px 16px 16px;
    display: flex;
}
.popover {
    z-index: 999;
    background: rgba(var(--panel), 1);
    border-radius: 16px;
    flex-direction: column;
    display: flex;
    position: fixed;
    overflow: hidden;
    box-shadow: 0 20px 40px -10px #0006;
}
.slide-over {
    z-index: 999;
    background: rgba(var(--panel), 1);
    border-radius: 12px;
    flex-direction: column;
    display: flex;
    position: fixed;
    overflow: hidden;
    box-shadow: 0 0 8px #000000fc;
}
.view-loader {
    place-items: center;
    width: 100%;
    height: 100%;
    display: grid;
    position: absolute;
    top: 0;
    left: 0;
}
.slider-component {
    --height: 30px;
    --radius: 8px;
    --rail-radius: 6px;
    --thumb-ver-margin: 6px;
    --thumb-right-margin: 10px;
    --thumb-width: 2px;
    --labels-padding: 12px;
    height: var(--height);
    border-radius: var(--radius);
    touch-action: none;
    display: flex;
    position: relative;
}
.slider-component .track {
    background: rgba(var(--panel-dim), 1);
    color: rgba(var(--panel-active), 1);
    border-radius: inherit;
    cursor: pointer;
    flex-grow: 1;
    height: 100%;
}
.slider-component .track .rail {
    background: rgba(var(--panel-active), 0.7);
    border-radius: inherit;
    touch-action: pan-x;
    will-change: transform;
    height: 100%;
    transition: filter 0.2s;
    position: absolute;
    box-shadow: 0 1.5px 6px -3px #0006;
}
.slider-component .thumb {
    height: calc(var(--height) - var(--thumb-ver-margin) * 2);
    width: var(--thumb-width);
    margin: var(--thumb-ver-margin) 0;
    margin-right: var(--thumb-right-margin);
    border-radius: var(--thumb-width);
    background: rgba(var(--primary), 0.36);
    cursor: pointer;
    outline: none;
    transition: background 0.2s;
    display: block;
}
.slider-component .labels {
    z-index: 1;
    padding: 0 var(--labels-padding);
    pointer-events: none;
    justify-content: space-between;
    align-items: center;
    display: flex;
    position: absolute;
    inset: 0;
}
.slider-component .labels span {
    color: rgba(var(--primary), 0.36);
}
.slider-component .labels svg {
    width: 16px;
    height: 16px;
    margin-left: calc(0px - var(--labels-padding) * 0.4);
    color: rgba(var(--primary), 0.36);
}
@media only screen and (width>=1200px) {
    .slider-component:hover .reset-icon {
        visibility: visible;
        opacity: 1;
    }
    .slider-component:hover .thumb {
        background: rgba(var(--primary), 0.6);
    }
    .slider-component:hover .labels span {
        color: rgba(var(--primary), 0.6);
    }
}
.slider-component.is-at-start .thumb {
    transition: transform 0.3s;
    transform: translate(10px);
}
.slider-component.hide-rail {
    --thumb-ver-margin: 2px;
    --thumb-right-margin: 0;
    --thumb-width: 7px;
}
.slider-component.hide-rail .track .rail {
    display: none;
}
.slider-component.hide-rail .thumb {
    background: rgba(var(--panel-active), 1);
    border-radius: 2.5px;
    box-shadow: 0 1px 3px #00000026;
    transform: none !important;
}
.slider-component.true-disabled {
    pointer-events: none;
    opacity: 0.5;
}
.slider-component .reset-icon {
    width: 10px;
    height: inherit;
    border-radius: inherit;
    color: rgba(var(--primary), 0.36);
    cursor: pointer;
    visibility: hidden;
    opacity: 0;
    background: red;
    justify-content: center;
    align-items: center;
    display: flex;
    position: absolute;
    right: -11px;
}
.slider-component .reset-icon svg {
    width: 10px;
    height: 10px;
}
.slider-component .reset-icon:hover {
    background: rgba(var(--primary), 0.06);
    color: rgba(var(--primary), 1);
}
@media only screen and (width>=0) and (width<=800px) {
    .slider-component .reset-icon {
        display: none;
    }
    .slider-component {
        --height: 40px;
        --radius: 12px;
        --rail-radius: 6px;
        --thumb-ver-margin: 8px;
        --thumb-right-margin: 12px;
        --thumb-width: 3px;
        --labels-padding: 12px;
    }
    .slider-component .labels span {
        color: rgba(var(--primary), 0.6);
    }
    .slider-component .track .rail {
        background: rgba(var(--panel-active), 1);
    }
    .slider-component.hide-rail {
        --thumb-ver-margin: 3px;
        --thumb-width: 10px;
    }
    .slider-component.hide-rail .thumb {
        border-radius: 4px;
    }
}
.progress-bar-wrapper {
    aspect-ratio: 1;
    width: max-content;
    position: relative;
}
.progress-bar-wrapper .complete-indicator {
    background: rgba(var(--primary), 1);
    border-radius: 50%;
    padding: 18%;
    transition: all 0.3s;
    position: absolute;
    inset: 0;
}
.progress-bar-wrapper .complete-indicator svg {
    aspect-ratio: 1;
    width: 100%;
    color: rgba(var(--secondary), 1);
}
.progress-bar-wrapper .number-text {
    z-index: 0;
    place-items: center;
    display: grid;
    position: absolute;
    inset: 0;
}
.progress-bar-wrapper .number-text span {
    color: rgba(var(--primary), 1);
    scale: 80%;
}
.progress-bar-wrapper .progress-bar {
    border-radius: 50%;
}
.progress-bar-wrapper .progress-bar.is-loader {
    animation: 1s linear infinite spin;
}
.progress-bar-wrapper .progress-bar .trail {
    stroke: rgba(var(--secondary), 0.3);
}
.progress-bar-wrapper .progress-bar .path {
    stroke: rgba(var(--secondary), 1);
    transition: all 0.4s;
}
.progress-bar-wrapper.default-accent .complete-indicator {
    background: rgba(var(--primary), 1);
}
.progress-bar-wrapper.default-accent .progress-bar .trail {
    stroke: rgba(var(--primary), 0.2);
}
.progress-bar-wrapper.default-accent .progress-bar .path {
    stroke: rgba(var(--primary), 1);
}
.progress-bar-wrapper.info-accent .complete-indicator {
    background: rgba(var(--info), 1);
}
.progress-bar-wrapper.info-accent .progress-bar .trail {
    stroke: rgba(var(--info), 0.2);
}
.progress-bar-wrapper.info-accent .progress-bar .path {
    stroke: rgba(var(--info), 1);
}
@keyframes is-loader-spin {
    0% {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}
.slideshow-component {
    width: 100%;
    position: relative;
}
.slideshow-component .items-scroll {
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    width: 100%;
    height: max-content;
    position: relative;
    overflow: auto hidden;
    container-type: inline-size;
}
.slideshow-component .items-scroll .all-items {
    align-items: center;
    gap: 10px;
    width: max-content;
    height: 100%;
    display: flex;
}
.slideshow-component .items-scroll .all-items .slideshow-item {
    flex-shrink: 0;
    height: max-content;
}
.slideshow-component .items-scroll .all-items .slideshow-item.is-list {
    scroll-snap-align: start;
    width: max-content;
}
.slideshow-component .items-scroll .all-items .slideshow-item.is-full-width {
    scroll-snap-align: center;
    width: 100cqw;
    min-width: 100cqw;
}
.slideshow-component .slideshow-indicators {
    z-index: 3;
    pointer-events: none;
    justify-content: center;
    gap: 5px;
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}
.slideshow-component .slideshow-indicators span {
    aspect-ratio: 1;
    background: rgba(var(--primary), 0.12);
    border-radius: 50%;
    width: 5px;
}
.slideshow-component .slideshow-indicators span.is-active {
    background: rgba(var(--primary), 0.6);
}
.slideshow-component .nav-buttons.inline-mode {
    justify-content: center;
    gap: 10px;
    display: flex;
}
.slideshow-component .nav-buttons.inline-mode > * {
    background: rgba(var(--primary), 0.12) !important;
}
.slideshow-component .nav-buttons.overlay-mode {
    z-index: 3;
    pointer-events: none;
    justify-content: space-between;
    align-items: center;
    display: flex;
    position: absolute;
    inset: 0;
}
.slideshow-component .nav-buttons.overlay-mode > * {
    pointer-events: all;
    background: rgba(var(--secondary), 0.6) !important;
}
.hide-scrollbar::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    display: none !important;
}
.mockup-picker-mobile-modal .picker-filters::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    display: none !important;
}
.panel-control-stack::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    display: none !important;
}
.shots-mobile-ui .panel .panel-controls-stack .panel-control-switcher::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    display: none !important;
}
.h-stack::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    display: none !important;
}
.v-stack .scroll::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    display: none !important;
}
.truncate,
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .details
    p,
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .details
    > span,
.shots-mobile-ui .mobile-control-switcher-button span,
.panel .panel-selector-btn-desktop .details p,
.subscription-view .sub-card-drawer .change-subscription .copy span {
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    max-width: 100%;
    display: -webkit-box;
    overflow: hidden;
}
.d-none,
.des {
    display: none;
}
@media only screen and (width>=1200px) {
    .des {
        display: inherit;
    }
}
.tab {
    display: none;
}
@media only screen and (width>=800px) and (width<=1200px) {
    .tab {
        display: inherit;
    }
}
.mob {
    display: none;
}
@media only screen and (width>=0) and (width<=800px) {
    .mob {
        display: inherit;
    }
}
.flex {
    display: flex;
}
.flex-col {
    flex-direction: column;
    display: flex;
}
.align-c {
    align-items: center;
}
.justify-c {
    justify-content: center;
}
.justify-e {
    justify-content: flex-end;
}
.text-center {
    text-align: center;
}
.text-uppercase {
    text-transform: uppercase;
}
.text-capitalize {
    text-transform: capitalize;
}
.shots-mobile-ui.app-main {
    padding-top: env(safe-area-inset-top, 0) !important;
}
body {
    background: rgba(var(--body), 1);
    color: rgba(var(--primary), 1);
}
.app-main {
    background: rgba(var(--background), 1);
    flex-direction: column;
    gap: 3px;
    width: 100vw;
    height: 100vh;
    padding: 6px;
    display: flex;
    position: relative;
}
.app-main:after {
    content: '';
    background:
        linear-gradient(90deg, rgba(var(--body), 1) 0%, transparent 25%),
        linear-gradient(90deg, rgba(var(--body), 0.2) 0%, rgba(var(--body), 0.2) 100%);
    z-index: 1000;
    border-radius: inherit;
    opacity: 0;
    visibility: hidden;
    position: absolute;
    inset: 0;
}
.app-main.side-menu-open {
    border-radius: 20px;
}
.app-main.side-menu-open:after {
    opacity: 1;
    visibility: visible;
}
@media only screen and (height>=0) and (height<=800px) {
    .app-main .timeline-component {
        width: calc(100vw - 474px);
        margin-left: 231px;
    }
    .app-main .timeline-component .timeline-controls {
        left: 0;
        right: 0;
    }
    .app-main .sidebar {
        height: calc(100vh - 59px) !important;
    }
}
@media only screen and (height>=0) and (height<=800px) and (width>=800px) and (width<=1200px) {
    .app-main .timeline-component {
        width: calc(100vw - 243px);
    }
}
.app-top-bar {
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
    display: flex;
}
.app-top-bar .navbar,
.app-top-bar .export-button-placeholder {
    min-width: 228px;
    max-width: 228px;
}
.app-main-panels {
    flex: 1;
    align-items: stretch;
    gap: 3px;
    height: 60%;
    display: flex;
}
.sidebar {
    z-index: 10;
    min-width: 228px;
    max-width: 228px;
    position: relative;
}
.control-panel {
    background: rgba(var(--panel), 1);
    z-index: 1;
    border-radius: 16px;
    width: 100%;
    height: 100%;
    position: relative;
}
.tablet-layout-panel {
    z-index: 11;
    position: absolute;
    inset: 0;
}
.panel-hover-view {
    z-index: 10;
    padding: 8px;
    position: fixed;
}
.panel-hover-view.left-position {
    width: max-content;
    padding-right: 32px;
    top: 48px;
    bottom: 0;
    left: 0;
}
.panel-hover-view.right-position {
    width: max-content;
    padding-left: 32px;
    top: 48px;
    bottom: 0;
    right: 0;
}
.panel-hover-view.bottom-position {
    padding-top: 32px;
    bottom: 0;
    left: 0;
    right: 0;
}
.panel-hover-view .panel-hover-inner {
    flex-direction: column;
    gap: 4px;
    width: 228px;
    min-width: 228px;
    max-width: 228px;
    height: 100%;
    min-height: 100%;
    max-height: 100%;
    display: flex;
}
.panel-hover-view .panel-hover-inner > .panel {
    height: 100% !important;
}
.panel-hover-view-indicator {
    z-index: 0;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 16px;
    display: flex;
    position: fixed;
    inset: 0;
}
.panel-hover-view-indicator.left-position {
    right: unset;
    padding-right: 0;
}
.panel-hover-view-indicator.right-position {
    left: unset;
    padding-left: 0;
}
.panel-hover-view-indicator.bottom-position {
    top: unset;
    padding-top: 0;
}
.panel-hover-view-indicator svg {
    width: 24px;
    height: 24px;
    color: rgba(var(--primary), 0.36);
}
.export-layer {
    z-index: -1000;
    position: fixed;
}
.export-layer .empty-drop {
    display: none;
}
.layouts-button-wrapper {
    z-index: 20;
    border-radius: 0 0 14px 14px;
    align-items: flex-end;
    gap: 6px;
    padding: 10px;
    display: flex;
    position: absolute;
    inset: 0;
}
.layouts-button-wrapper.fix-top {
    bottom: unset;
    background: linear-gradient(0deg, rgba(var(--panel), 0) 0%, rgba(var(--panel), 1) 70%);
    border-radius: 14px 14px 0 0;
    padding-bottom: 20px;
}
.layouts-button-wrapper.fix-bottom {
    top: unset;
    background: linear-gradient(180deg, rgba(var(--panel), 0) 0%, rgba(var(--panel), 1) 70%);
    border-radius: 0 0 14px 14px;
    padding-top: 20px;
}
.active,
.mockup-track.is-active:before,
.base-clip-item.is-editing .clip,
.animation-clip-item.is-editing .clip,
.video-clip-item.is-editing .clip,
.panel-button.true-active .preview,
.dropzone-edit-popover .content .media-list .media-item.is-active .media-display img,
.dropzone-edit-popover .content .media-list .media-item.is-active .media-display video {
    outline: solid 1.5px rgba(var(--primary), 0.5) !important;
    outline-offset: 2.5px !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .active,
    .mockup-track.is-active:before,
    .base-clip-item.is-editing .clip,
    .animation-clip-item.is-editing .clip,
    .video-clip-item.is-editing .clip,
    .panel-button.true-active .preview,
    .dropzone-edit-popover .content .media-list .media-item.is-active .media-display img,
    .dropzone-edit-popover .content .media-list .media-item.is-active .media-display video {
        outline: solid 2px rgba(var(--primary), 0.5) !important;
        outline-offset: 3px !important;
    }
}
.banner-popover {
    border-radius: 28px;
    flex-direction: column;
    gap: 14px;
    display: flex;
    overflow: visible;
}
.banner-popover .close-icon {
    margin: 0 auto;
    position: absolute;
    bottom: -48px;
    left: 0;
    right: 0;
}
.banner-popover .close-icon:not(:hover) {
    color: rgba(var(--primary), 0.6);
}
.banner-popover .content {
    border-radius: inherit;
    cursor: pointer;
    align-items: center;
    gap: 16px;
    padding: 12px 14px 14px;
    display: flex;
}
.banner-popover .content:hover {
    background: rgba(var(--primary), 0.06);
}
.banner-popover .content .icon-wrapper svg {
    width: 32px;
    height: 32px;
}
.banner-popover .content .copy {
    flex-direction: column;
    gap: 4px;
    padding: 4px 6px;
    display: flex;
}
.banner-popover .content .image-wrapper {
    aspect-ratio: 1;
    border-radius: 16px;
    width: 100px;
    min-width: 100px;
    overflow: hidden;
}
.banner-popover .content .image-wrapper img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.plus-badge-wrapper {
    cursor: pointer;
    z-index: 10;
    justify-content: flex-end;
    align-items: flex-start;
    display: flex;
    position: absolute;
    inset: 0;
}
.plus-badge-wrapper .plus-badge {
    z-index: 10;
    filter: drop-shadow(0 0 4px #fff9);
    background: #ffffffb3;
    border-radius: 5px;
    outline: 0.5px solid #0000004d;
    width: 16px;
    max-width: max-content;
    height: 16px;
    padding: 1px;
    line-height: 0;
    overflow: hidden;
}
.plus-badge-wrapper .plus-badge svg {
    color: #0000;
    background: linear-gradient(130deg, #ff6432 30%, #ff0065 45%, #7b2eff 75%);
    width: 100%;
    height: 100%;
    // -webkit-mask-image: url(https://beta.shots.so/icon/plus-bold.svg);
    // mask-image: url(https://beta.shots.so/icon/plus-bold.svg);
    -webkit-mask-position: 50%;
    mask-position: 50%;
    -webkit-mask-size: 100%;
    mask-size: 100%;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
}
.plus-badge-wrapper .pro-badge {
    z-index: 10;
    filter: drop-shadow(0 0 4px #fff9);
    background: #ffffffb3;
    border-radius: 5px;
    outline: 0.5px solid #0000004d;
    padding: 1px;
    line-height: 0;
    overflow: hidden;
}
.plus-badge-wrapper .pro-badge span {
    color: #0000;
    -moz-text-fill-color: #0000;
    background: linear-gradient(130deg, #ff6432 30%, #ff0065 45%, #7b2eff 75%) 100% 100%/200%
        no-repeat;
    -webkit-background-clip: text;
    background-clip: text;
    margin-top: -2px;
    margin-left: -1px;
    font:
        italic 700 11.5px/15px Inter,
        sans-serif;
}
.plus-upgrade-modal {
    border-radius: 32px;
}
.plus-upgrade-modal .dim,
.plus-upgrade-modal .content .sub-button svg,
.plus-upgrade-modal .content .login-back svg {
    opacity: 0.4;
}
.plus-upgrade-modal:after {
    content: '';
    pointer-events: none;
    border-radius: inherit;
    border: solid 1px rgba(var(--primary), 0.06);
    z-index: 2;
    position: absolute;
    inset: 0;
}
.plus-upgrade-modal .modal-head button {
    color: #fff;
    background: #00000026;
}
.plus-upgrade-modal .cover {
    z-index: -1;
    position: absolute;
    inset: 0;
}
.plus-upgrade-modal .cover video,
.plus-upgrade-modal .cover img {
    object-fit: cover;
    width: 100%;
    height: 60%;
}
.plus-upgrade-modal .cover .overlay {
    z-index: 1;
    position: absolute;
    inset: 0;
    -webkit-mask-image: linear-gradient(#0000 40%, #000 60%);
    mask-image: linear-gradient(#0000 40%, #000 60%);
}
.plus-upgrade-modal .cover .overlay:after {
    content: '';
    z-index: 1;
    background: linear-gradient(
        80.18deg,
        #d1ccdd 0%,
        #c893e1 25%,
        #eb47a7 50%,
        #f94a73 75%,
        #fb7a53 100%
    );
    position: absolute;
    inset: 0;
}
.plus-upgrade-modal .cover .overlay:before {
    content: '';
    z-index: 2;
    background: linear-gradient(rgba(var(--panel), 1) 30%, transparent 100%);
    position: absolute;
    inset: 0;
}
.plus-upgrade-modal .content {
    text-align: center;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    height: 100%;
    padding: 16px;
    display: flex;
}
.plus-upgrade-modal .content .incentives {
    pointer-events: none;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
    width: 95%;
    margin-bottom: 12px;
    display: flex;
}
.plus-upgrade-modal .content .incentives button {
    color: rgba(var(--primary), 0.6);
    gap: 5px;
    padding: 4px 8px;
}
.plus-upgrade-modal .content .incentives button svg {
    width: 20px;
    height: 20px;
}
.plus-upgrade-modal .content .login-back svg {
    width: 18px;
    height: 18px;
    margin-left: -4px;
}
.plus-upgrade-modal .content .sub-button {
    gap: 2px;
}
.plus-upgrade-modal .content .sub-button svg {
    width: 18px;
    height: 18px;
}
.plus-upgrade-modal .content .main-button {
    color: #000;
    z-index: 10;
    outline-offset: -1px;
    cursor: pointer;
    background: #ffffffb3;
    outline: 1px solid #ffffff4d;
    overflow: hidden;
}
.plus-upgrade-modal .content .main-button:after {
    content: '';
    z-index: -1;
    background: linear-gradient(#fff9 0%, #0000 80%);
    position: absolute;
    inset: -1px;
}
.plus-upgrade-modal .content .main-button span {
    font-size: 16px;
}
.plus-upgrade-modal .content .main-button svg {
    animation: 1s infinite alternate see-plans-arrow;
    transform: translate(2px);
}
@keyframes see-plans-arrow {
    to {
        transform: translate(-2px);
    }
}
.upgrade-to-pro-modal {
    border-radius: 28px;
}
.upgrade-to-pro-modal:after {
    content: '';
    pointer-events: none;
    border-radius: inherit;
    border: solid 1px rgba(var(--primary), 0.36);
    z-index: 2;
    position: absolute;
    inset: 0;
}
.upgrade-to-pro-modal .dim,
.upgrade-to-pro-modal .plus-upgrade-modal .content .login-back svg,
.plus-upgrade-modal .content .login-back .upgrade-to-pro-modal svg,
.upgrade-to-pro-modal .plus-upgrade-modal .content .sub-button svg,
.plus-upgrade-modal .content .sub-button .upgrade-to-pro-modal svg {
    opacity: 0.6;
}
.upgrade-to-pro-modal .backdrop {
    z-index: -1;
}
.upgrade-to-pro-modal .backdrop video,
.upgrade-to-pro-modal .backdrop img {
    object-fit: cover;
    z-index: 2;
    width: 100%;
    height: 70%;
    position: relative;
    -webkit-mask-image: linear-gradient(#000 50%, #0000 70%);
    mask-image: linear-gradient(#000 50%, #0000 70%);
}
.upgrade-to-pro-modal .content {
    color: #fff;
    text-align: center;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    height: 100%;
    padding: 20px;
    display: flex;
}
.upgrade-to-pro-modal .content .incentives {
    pointer-events: none;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
    display: flex;
}
.upgrade-to-pro-modal .content .incentives button {
    color: #fff;
    gap: 6px;
    width: 164px;
    padding: 4px 8px;
}
.upgrade-to-pro-modal .content .incentives button svg {
    width: 20px;
    height: 20px;
}
.upgrade-to-pro-modal .content .main-button {
    color: #000;
    z-index: 1;
    outline-offset: -1px;
    cursor: pointer;
    background: #ffffffb3;
    outline: 1px solid #ffffff4d;
    overflow: hidden;
}
.upgrade-to-pro-modal .content .main-button:after {
    content: '';
    z-index: -1;
    background: linear-gradient(#fff9 0%, #0000 80%);
    position: absolute;
    inset: -1px;
}
.upgrade-to-pro-modal .content .footer {
    flex-direction: column;
    gap: 2px;
    display: flex;
}
.templates-btn img {
    pointer-events: none;
    width: auto;
    height: 34px;
    margin-right: 4px;
}
.templates-btn > svg {
    width: 16px;
    height: 16px;
    color: rgba(var(--primary), 0.6);
}
.templates-btn span {
    background: linear-gradient(80.18deg, #fff 0%, #f85bb6 80%, #fe678a 90% 100%);
    background: linear-gradient(90deg, rgba(var(--primary), 1), rgba(var(--primary), 0.36));
    -moz-text-fill-color: #0000;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-background-clip: text;
    background-clip: text;
}
.h-stack {
    width: 100%;
    container-type: inline-size;
}
.h-stack .content {
    padding: 12px 16px;
}
.h-stack.show-list {
    overflow-x: auto;
}
.h-stack.show-list .content {
    width: max-content;
    display: flex;
}
.h-stack.show-grid .content {
    grid-template-columns: 1fr 1fr;
    width: 100%;
    display: grid;
}
.h-stack.show-grid .content > * {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
}
.v-stack {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.v-stack .scroll {
    width: 100%;
    height: 100%;
    overflow: hidden auto;
}
.v-stack .scroll .v-stack-content {
    flex-direction: column;
    width: 100%;
    height: max-content;
    display: flex;
}
.user-avatar {
    aspect-ratio: 1;
    background: rgba(var(--primary), 0.12);
    border-radius: 50%;
    width: 30px;
    position: relative;
    overflow: hidden;
}
.user-avatar img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.user-avatar .placeholder {
    background: rgba(var(--primary), 0.12);
    place-content: center;
    width: 100%;
    height: 100%;
    display: grid;
}
.user-avatar .placeholder span {
    letter-spacing: -0.4px;
    font:
        500 15px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .user-avatar .placeholder span {
        letter-spacing: -0.1px;
        font:
            500 15.5px/20px Inter,
            sans-serif;
    }
}
.tag {
    background: rgba(var(--primary), 1);
    color: rgba(var(--secondary), 1);
    border-radius: 8px;
    max-width: max-content;
    padding: 2px 6px;
}
.tag.tag-small {
    border-radius: 5px;
    padding: 2px 4px;
    font-weight: 500;
}
.performance-mode-control-head {
    text-align: center;
    border-bottom: solid 1px rgba(var(--primary), 0.06);
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 12px 0;
    display: flex;
    position: relative;
}
.performance-mode-control-head:before {
    content: '';
    z-index: -1;
    background: linear-gradient(rgba(var(--performance), 0.1), transparent);
    position: absolute;
    inset: -16px;
}
.performance-mode-control-head > svg {
    width: 40px;
    height: 40px;
    color: rgba(var(--performance), 1);
    margin-bottom: 4px;
}
.performance-mode-control-head > p {
    color: rgba(var(--primary), 0.6);
    letter-spacing: -0.2px;
    font:
        12.5px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .performance-mode-control-head > p {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.inline-panel-alert {
    border-radius: 12px;
    align-items: flex-start;
    gap: 6px;
    padding: 8px 12px 8px 8px;
    display: flex;
}
.inline-panel-alert > svg {
    width: 24px;
    height: 24px;
}
.inline-panel-alert .copy {
    flex-direction: column;
    flex: 1;
    gap: 2px;
    padding: 2px 0;
    display: flex;
}
.inline-panel-alert .copy > p {
    letter-spacing: -0.4px;
    font:
        500 14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .inline-panel-alert .copy > p {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.inline-panel-alert .copy > span {
    letter-spacing: -0.2px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .inline-panel-alert .copy > span {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.inline-panel-alert .copy > :first-child {
    font-weight: 500 !important;
}
.inline-panel-alert .copy > :nth-child(2):not(button) {
    opacity: 0.7;
}
.inline-panel-alert .copy button {
    width: 100%;
    margin-top: 6px;
}
.inline-panel-alert.default-variant {
    background: rgba(var(--primary), 0.06);
}
.inline-panel-alert.default-variant svg {
    color: rgba(var(--primary), 0.6);
}
.inline-panel-alert.info-variant {
    background: rgba(var(--info), 0.1);
}
.inline-panel-alert.info-variant svg,
.inline-panel-alert.info-variant button span {
    color: rgba(var(--info), 1);
}
.inline-panel-alert.warning-variant {
    background: rgba(var(--alert), 0.1);
}
.inline-panel-alert.warning-variant svg,
.inline-panel-alert.warning-variant button span {
    color: rgba(var(--alert), 1);
}
.inline-panel-alert.danger-variant {
    background: rgba(var(--danger), 0.1);
}
.inline-panel-alert.danger-variant svg,
.inline-panel-alert.danger-variant button span {
    color: rgba(var(--danger), 1);
}
.inline-panel-alert.success-variant {
    background: rgba(var(--success), 0.1);
}
.inline-panel-alert.success-variant svg,
.inline-panel-alert.success-variant button span {
    color: rgba(var(--success), 1);
}
.h-divider {
    background: rgba(var(--primary), 0.06);
    min-width: 100%;
    max-width: 100%;
    min-height: 1px;
    max-height: 1px;
}
.v-divider {
    background: rgba(var(--primary), 0.06);
    min-width: 1px;
    max-width: 1px;
    min-height: 100%;
    max-height: 100%;
}
.app-stage-tag {
    letter-spacing: 0.4px;
    color: rgba(var(--secondary), 1);
    border-radius: 20px;
    padding: 2px 7px;
    font:
        600 12px/16px Inter,
        sans-serif;
}
.app-stage-tag.beta {
    background: rgba(var(--primary), 0.36);
}
.app-stage-tag.alpha {
    background: rgba(var(--info), 1);
}
.loading-screen {
    background: rgba(var(--background), 1);
    z-index: 2147483647;
    place-items: center;
    width: 100vw;
    height: 100vh;
    display: grid;
    position: fixed;
    top: 0;
    left: 0;
}
.loading-screen .preloader svg {
    transform: scale(1.6);
}
.loading-screen .preloader circle {
    stroke: rgba(var(--primary), 0.6);
}
.loading-screen .loading-logo {
    width: 120px;
    height: 120px;
    animation: 0.8s logo-all;
    position: relative;
}
.loading-screen .loading-logo img {
    animation-duration: 0.4s;
    animation-timing-function: ease;
    position: absolute;
}
.loading-screen .loading-logo .layer-1st {
    z-index: 2;
    animation-name: logo-1;
    top: -17%;
    left: -5%;
}
.loading-screen .loading-logo .layer-2nd {
    z-index: 1;
    animation-name: logo-2;
    top: 12%;
    left: -5%;
}
.loading-screen .loading-logo .layer-3rd {
    z-index: 0;
    animation-name: logo-3;
    top: 30%;
}
@keyframes logo-all {
    0% {
        transform: scale(0.7);
    }
    50% {
        transform: scale(1);
    }
    to {
        transform: scale(1.3);
    }
}
@keyframes logo-1 {
    0% {
        transform: translate(-10%, -30%) rotate(-10deg);
    }
}
@keyframes logo-2 {
    0% {
        transform: translateY(-10%) rotate(-5deg);
    }
}
@keyframes logo-3 {
    0% {
        transform: translate(5%, 30%) rotate(10deg);
    }
}
.logo-name {
    align-items: center;
    gap: 10px;
    display: flex;
}
.logo-name img {
    width: 40px;
}
.logo-name span {
    letter-spacing: -0.8px;
    font:
        500 23px/32px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .logo-name span {
        letter-spacing: -0.75px;
        font:
            500 21.5px/28px Inter,
            sans-serif;
    }
}
.command-bar-desktop {
    flex: 1;
    gap: 8px;
    padding: 4px;
    display: flex;
}
.command-bar-desktop .v-divider {
    margin: 4px 0;
}
.command-bar-desktop .left,
.command-bar-desktop .center,
.command-bar-desktop .right {
    align-items: center;
    gap: 8px;
    display: flex;
}
.command-bar-desktop .left.left,
.command-bar-desktop .center.left,
.command-bar-desktop .right.left {
    flex: 1;
    justify-content: flex-start;
}
.command-bar-desktop .left.right,
.command-bar-desktop .center.right,
.command-bar-desktop .right.right {
    flex: 1;
    justify-content: flex-end;
}
.command-bar-mobile {
    top: calc(env(safe-area-inset-top, 0px) + 56px);
    z-index: 20;
    padding: 0 12px;
    display: flex;
    position: fixed;
    left: 0;
    right: 0;
}
.command-bar-mobile .templates-btn-mobile {
    gap: 4px !important;
    padding: 0 4px !important;
}
.command-bar-mobile .templates-btn-mobile .logo {
    pointer-events: none;
    width: auto;
    height: 34px;
}
.command-bar-mobile .templates-btn-mobile > svg {
    width: 16px;
    height: 16px;
    color: rgba(var(--primary), 0.6);
}
.command-bar-mobile .left,
.command-bar-mobile .center,
.command-bar-mobile .right {
    align-items: center;
    gap: 10px;
    display: flex;
}
.command-bar-mobile .left.left,
.command-bar-mobile .center.left,
.command-bar-mobile .right.left {
    flex: 1;
    justify-content: flex-start;
}
.command-bar-mobile .left.right,
.command-bar-mobile .center.right,
.command-bar-mobile .right.right {
    flex: 1;
    justify-content: flex-end;
}
.command-bar-panels-control {
    position: relative;
}
.command-bar-panels-control .buttons-wrapper {
    background: rgba(var(--panel), 1);
    z-index: 1;
    opacity: 0;
    border-radius: 12px;
    align-items: center;
    width: max-content;
    padding: 4px;
    transition: all 0.3s;
    display: flex;
    position: absolute;
    top: -4px;
    left: -125%;
    overflow: hidden;
    transform: scaleX(0.2) scaleY(0.8);
}
.command-bar-panels-control .buttons-wrapper button:nth-child(2) {
    margin: 0 4px;
}
.command-bar-panels-control .buttons-wrapper:hover {
    opacity: 1;
    transform: none !important;
}
.alert-modal {
    border-radius: 32px;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 16px;
    display: flex;
}
.alert-modal:before {
    content: '';
    z-index: -1;
    position: absolute;
    inset: 0;
}
.alert-modal .icon {
    width: 36px;
    height: 36px;
    margin-top: 16px;
    margin-bottom: -8px;
}
.alert-modal .icon svg {
    width: inherit;
    height: inherit;
}
.alert-modal .texts {
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 12px;
    display: flex;
}
.alert-modal .texts p {
    width: 90%;
}
.alert-modal .buttons {
    gap: 10px;
    width: 100%;
    display: flex;
}
.alert-modal .buttons button {
    flex: 1;
}
.alert-modal.default-type .icon {
    color: rgba(var(--primary), 1);
}
.alert-modal.default-type:before {
    background: linear-gradient(rgba(var(--primary), 0.1), transparent);
}
.alert-modal.default-type .confirm-button {
    color: rgba(var(--primary), 1);
}
.alert-modal.danger-type .icon {
    color: rgba(var(--danger), 1);
}
.alert-modal.danger-type:before {
    background: linear-gradient(rgba(var(--danger), 0.1), transparent);
}
.alert-modal.danger-type .confirm-button {
    color: rgba(var(--danger), 1);
}
.alert-modal.warning-type .icon {
    color: rgba(var(--alert), 1);
}
.alert-modal.warning-type:before {
    background: linear-gradient(rgba(var(--alert), 0.1), transparent);
}
.alert-modal.warning-type .confirm-button {
    color: rgba(var(--alert), 1);
}
.alert-modal.success-type .icon {
    color: rgba(var(--success), 1);
}
.alert-modal.success-type:before {
    background: linear-gradient(rgba(var(--success), 0.1), transparent);
}
.alert-modal.success-type .confirm-button {
    color: rgba(var(--success), 1);
}
.alert-modal.info-type .icon {
    color: rgba(var(--info), 1);
}
.alert-modal.info-type:before {
    background: linear-gradient(rgba(var(--info), 0.1), transparent);
}
.alert-modal.info-type .confirm-button {
    color: rgba(var(--info), 1);
}
.about {
    text-align: center;
    align-items: center;
}
.about .members-list {
    gap: 32px;
    display: flex;
}
.about .members-list .member {
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    display: flex;
}
.about .members-list .member:hover button {
    color: #45a1ee;
}
.about .members-list .member:hover > span {
    opacity: 0.5;
}
.about .members-list .member > span {
    color: rgba(var(--primary), 1);
    opacity: 0.3;
    font:
        500 36px/36px Comforter Brush,
        cursive;
}
.about .members-list .member button {
    color: rgba(var(--primary), 0.36);
}
.switch {
    --radius: 12px;
    --gap: 3px;
    --button-padding: 8px 0;
    --label-button-padding: 4px;
    --icon-button-padding: 4px;
    --button-gap: 6px;
    --icon-size: 20px;
    background: rgba(var(--panel-dim), 1);
    border-radius: var(--radius);
    padding: var(--gap);
    gap: var(--gap);
    display: flex;
    overflow: hidden;
}
.switch.disabled {
    pointer-events: none;
    opacity: 0.5;
}
.switch .switch-button {
    align-items: center;
    gap: var(--button-gap);
    width: 100%;
    padding: var(--button-padding);
    border-radius: calc(var(--radius) - var(--gap));
    flex-direction: column;
    display: flex;
    position: relative;
    overflow: visible;
    background: 0 0 !important;
}
.switch .switch-button .active-indicator {
    background: rgba(var(--panel-active), 1);
    border-radius: inherit;
    position: absolute;
    inset: 0;
    box-shadow: 0 3px 6px -3px #0000003d;
}
.switch .switch-button p {
    color: rgba(var(--primary), 1);
    z-index: 1;
    opacity: 0.5;
    letter-spacing: -0.2px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .switch .switch-button p {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.switch .switch-button .visual {
    z-index: 1;
    place-items: center;
    display: grid;
}
.switch .switch-button .visual .icon-wrapper {
    width: var(--icon-size);
    height: var(--icon-size);
    justify-content: center;
    display: flex;
    position: relative;
}
.switch .switch-button .visual .icon-wrapper > svg {
    width: inherit;
    height: inherit;
    opacity: 0.5;
}
.switch .switch-button .visual .image-wrapper {
    border-radius: 6px;
    width: 24px;
    height: 24px;
}
.switch .switch-button .visual .image-wrapper img {
    object-fit: cover;
    border-radius: 10px;
    width: 100%;
    height: 100%;
    position: absolute;
    inset: 0;
}
.switch .switch-button.label-only {
    padding: var(--label-button-padding);
}
.switch .switch-button.label-only p {
    letter-spacing: -0.2px;
    font:
        12.5px/20px Inter,
        sans-serif;
    font-weight: 500 !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .switch .switch-button.label-only p {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.switch .switch-button.icon-only {
    padding: var(--icon-button-padding);
}
.switch .switch-button.has-image {
    padding: 14px 0 6px;
}
.switch .switch-button.has-image .active-indicator {
    display: none;
}
.switch .switch-button.has-image .image-wrapper {
    opacity: 0.4;
}
.switch .switch-button.is-active p,
.switch .switch-button.is-active .icon-wrapper svg,
.switch .switch-button.is-active .image-wrapper {
    opacity: 1;
}
@media only screen and (width>=0) and (width<=800px) {
    .switch {
        --radius: 14px;
        --gap: 3px;
        --button-padding: 8px 0;
        --label-button-padding: 8px 0;
        --icon-button-padding: 8px 0;
        --button-gap: 8px;
        --icon-size: 24px;
    }
}
.switch-component {
    align-items: center;
    display: flex;
}
.switch-component.disabled {
    opacity: 0.5;
    pointer-events: none;
}
.switch-component.secondary-variant {
    background: rgba(var(--panel-dim), 1);
    border-radius: 12px;
    padding: 8px 8px 8px 12px;
}
.switch-component.has-label {
    justify-content: space-between;
    width: 100%;
}
.switch-component.has-label .copy {
    flex-direction: column;
    display: flex;
}
.switch-component .switcher {
    background: rgba(var(--primary), 0.06);
    cursor: pointer;
    border-radius: 40px;
    width: 44px;
    height: 26px;
    position: relative;
}
.switch-component .switcher .nudge {
    background: rgba(var(--primary), 1);
    border-radius: 40px;
    width: 22px;
    height: 22px;
    position: absolute;
    top: 2px;
}
.switch-component.is-active .switcher {
    background: rgba(var(--panel-active), 1);
}
.dev-utils {
    z-index: 100;
    width: max-content;
    display: flex;
    position: relative;
}
.dev-utils .panel-data {
    position: relative;
}
.dev-utils .panel-data button:hover ~ .panel-data-view {
    visibility: visible;
}
.dev-utils .panel-data .panel-data-view {
    visibility: hidden;
    flex-direction: column;
    gap: 6px;
    width: 280px;
    padding: 8px;
    display: flex;
    position: absolute;
    top: 44px;
    right: -10px;
}
.dev-utils .panel-data .panel-data-view section {
    flex-direction: column;
    gap: 4px;
    display: flex;
}
.dev-utils .panel-data .panel-data-view h5 {
    margin: 4px 4px 0;
    transform: scale(0.94);
}
.dev-utils .panel-data .data-view {
    background: rgba(var(--primary), 0.06);
    border-radius: 8px;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    display: flex;
}
.export-demo {
    z-index: 99;
    min-width: 100vw;
    max-width: 100vw;
    min-height: 100vh;
    max-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    overflow: scroll;
}
.export-demo .export-layer {
    z-index: 999999;
    background: #141414;
    position: relative;
}
.export-demo .export-layer .canvas {
    border: none;
    border-radius: 0;
    display: block;
}
.drop-test {
    background: red;
    inset: 0;
    transform: translateY(-240px);
    position: fixed !important;
}
.styles-wrapper .col-2,
.styles-wrapper .col-3 {
    display: grid;
}
.styles-wrapper .col-2 {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}
.styles-wrapper .col-2 .style-item .thumbnail .image-wrapper {
    outline-offset: 3px;
    border-radius: 8px;
}
.styles-wrapper .col-2 .footnote,
.styles-wrapper .col-2 .tag.tag-small {
    margin: 10px 5px;
}
.styles-wrapper .col-3 {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px;
}
.styles-wrapper .col-3 .style-item .thumbnail .image-wrapper {
    outline-offset: 2px;
    border-radius: 6px;
}
.styles-wrapper .col-3 .footnote,
.styles-wrapper .col-3 .tag.tag-small {
    margin: 8px 4px;
}
.style-item {
    cursor: pointer;
    text-align: center;
    flex-direction: column;
    gap: 0;
    width: 100%;
}
.style-item .thumbnail {
    width: 100%;
    position: relative;
}
.style-item .thumbnail .image-wrapper {
    aspect-ratio: 4/3;
    background: rgba(var(--panel-dim), 1);
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.style-item .thumbnail img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.text-font-picker .text-item,
.text-elements .text-item {
    display: none;
    position: relative;
}
.text-font-picker.variant-7 .text-item:first-child,
.text-elements.variant-7 .text-item:first-child {
    display: initial;
    position: relative;
    margin-bottom: 0.6em !important;
}
.text-font-picker.variant-7 .text-item:first-child:after,
.text-elements.variant-7 .text-item:first-child:after {
    content: '';
    background-color: var(--dynamic-color);
    opacity: 0.225;
    z-index: -1;
    border-radius: 25px;
    width: calc(100% + 1.3em);
    height: calc(100% + 0.5em);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.text-font-picker.variant-7 .text-item:nth-child(2),
.text-elements.variant-7 .text-item:nth-child(2) {
    display: initial;
}
.text-font-picker.variant-8 .text-item:first-child,
.text-elements.variant-8 .text-item:first-child {
    display: initial;
    position: relative;
    margin-bottom: 0.6em !important;
}
.text-font-picker.variant-8 .text-item:first-child:after,
.text-elements.variant-8 .text-item:first-child:after {
    content: '';
    z-index: -1;
    border: 0.05em solid var(--dynamic-color);
    border-radius: 25px;
    width: calc(100% + 1.3em);
    height: calc(100% + 0.5em);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.text-font-picker.variant-8 .text-item:nth-child(2),
.text-elements.variant-8 .text-item:nth-child(2),
.text-font-picker.variant-1 .text-item:nth-child(2),
.text-elements.variant-1 .text-item:nth-child(2),
.text-font-picker.variant-1 .text-item:nth-child(3),
.text-elements.variant-1 .text-item:nth-child(3),
.text-font-picker.variant-2 .text-item:nth-child(2),
.text-elements.variant-2 .text-item:nth-child(2),
.text-font-picker.variant-3 .text-item:first-child,
.text-elements.variant-3 .text-item:first-child,
.text-font-picker.variant-3 .text-item:nth-child(2),
.text-elements.variant-3 .text-item:nth-child(2),
.text-font-picker.variant-3 .text-item:nth-child(3),
.text-elements.variant-3 .text-item:nth-child(3),
.text-font-picker.variant-4 .text-item:nth-child(2),
.text-elements.variant-4 .text-item:nth-child(2) {
    display: initial;
}
.text-font-picker.variant-4 .text-item:nth-child(3),
.text-elements.variant-4 .text-item:nth-child(3) {
    display: initial;
    font-weight: 500 !important;
}
.text-font-picker.variant-5 .text-item:first-child,
.text-elements.variant-5 .text-item:first-child,
.text-font-picker.variant-5 .text-item:nth-child(2),
.text-elements.variant-5 .text-item:nth-child(2) {
    display: initial;
}
.text-font-picker.variant-6 .text-item:first-child,
.text-elements.variant-6 .text-item:first-child {
    display: initial;
    margin-bottom: 0.6em !important;
    font-weight: 500 !important;
}
.text-font-picker.variant-6 .text-item:first-child:after,
.text-elements.variant-6 .text-item:first-child:after {
    content: '';
    z-index: -1;
    background: red;
    border-radius: 0.6em;
    position: absolute;
    inset: -0.2em;
}
.text-font-picker.variant-6 .text-item:nth-child(2),
.text-elements.variant-6 .text-item:nth-child(2) {
    display: initial;
}
.text-adaptive-exclusion {
    mix-blend-mode: exclusion;
}
.text-adaptive-exclusion .text-item {
    color: #fff !important;
}
.text-adaptive-blend {
    mix-blend-mode: luminosity;
    filter: brightness(3) contrast(200%) sepia(200%) contrast(90%) saturate(200%);
}
.text-adaptive-blend .text-item {
    color: #878787c9 !important;
}
.text-adaptive-burn {
    mix-blend-mode: color-burn;
    filter: brightness(2) contrast(200%) sepia(200%) contrast(90%) saturate(130%);
}
.text-adaptive-burn .text-item {
    color: #171717 !important;
}
.text-adaptive-overlay {
    mix-blend-mode: overlay;
}
.text-adaptive-overlay .text-item {
    color: #2e2e2e !important;
}
.text-adaptive-luminosity {
    mix-blend-mode: luminosity;
    filter: brightness(0.4) contrast(200%) sepia() contrast(0%);
}
.text-adaptive-luminosity .text-item {
    color: gray !important;
}
.text-adaptive-difference {
    mix-blend-mode: difference;
    filter: brightness(1.1) contrast(1.1);
}
.text-adaptive-difference .text-item {
    color: #a3a3a3 !important;
}
.text-adaptive-overlaylight {
    mix-blend-mode: overlay;
}
.text-adaptive-overlaylight .text-item {
    color: #fff !important;
}
.text-adaptive-overlaydark {
    mix-blend-mode: overlay;
}
.text-adaptive-overlaydark .text-item {
    color: #000 !important;
}
.text-adaptive-difference2 {
    mix-blend-mode: difference;
    filter: brightness(0.1) contrast(0.1);
}
.text-adaptive-difference2 .text-item {
    color: gray !important;
}
.text-adaptive-difference3 {
    mix-blend-mode: difference;
    filter: drop-shadow(0 1em 1em #960000);
}
.text-adaptive-difference3 .text-item {
    color: red !important;
}
.text-adaptive-difference4 {
    mix-blend-mode: difference;
    filter: blur(0.05em) drop-shadow(0 0 1em #009600);
}
.text-adaptive-difference4 .text-item {
    color: green !important;
}
.text-adaptive-difference5 {
    mix-blend-mode: difference;
    filter: blur(0.05em) drop-shadow(0 0 1em #009600);
}
.text-adaptive-difference5 .text-item {
    color: #ff0 !important;
}
.create-animation-button {
    background: rgba(var(--panel), 1);
    outline: solid 1px rgba(var(--background), 1);
    margin: 0 auto;
    position: fixed;
    bottom: 16px;
    left: 0;
    right: 0;
    overflow: visible;
    box-shadow: 0 5px 15px -5px #0000003d;
}
.create-animation-button svg {
    transform: scale(1.1);
}
.create-animation-button .gradient-effect-wrapper,
.create-animation-button .light-backdrop {
    z-index: -1;
    border-radius: inherit;
    place-items: center;
    display: grid;
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.create-animation-button .gradient-effect-wrapper.light-backdrop,
.create-animation-button .light-backdrop.light-backdrop {
    z-index: -2;
    inset: -3px;
}
.create-animation-button .gradient-effect-wrapper .gradient-effect,
.create-animation-button .light-backdrop .gradient-effect {
    aspect-ratio: 1;
    opacity: 0;
    background: linear-gradient(120deg, #ff6432 25%, #ff0065 45%, #7b2eff 75%);
    width: 150%;
    position: absolute;
}
.create-animation-button .gradient-effect-wrapper .light-effect,
.create-animation-button .light-backdrop .light-effect {
    aspect-ratio: 1;
    opacity: 0.15;
    background: linear-gradient(#fff 0%, #0000 100%);
    width: 105%;
    position: absolute;
}
.create-animation-button:hover {
    color: #fff;
}
.create-animation-button:hover .light-effect,
.create-animation-button:hover .gradient-effect {
    animation: 2s linear infinite gradientEffect;
    opacity: 1 !important;
}
.create-animation-button:hover svg {
    animation: 2s infinite arrow;
}
.create-animation-button.is-glowing {
    color: #fff;
}
.create-animation-button.is-glowing .light-effect,
.create-animation-button.is-glowing .gradient-effect {
    animation: 2s linear infinite gradientEffect;
    opacity: 1 !important;
}
.create-animation-button.is-glowing svg {
    animation: 2s arrow;
}
@keyframes gradientEffect {
    0% {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}
@keyframes arrow {
    50% {
        transform: translate(2px) scale(1.2);
    }
}
.auth-flow {
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: 24px;
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}
.auth-flow .auth-view {
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    width: 100%;
    height: 100%;
    padding: 32px;
    display: flex;
    position: relative;
}
.auth-flow .auth-view > :not(.backdrop) {
    width: 100%;
    max-width: 340px;
}
.auth-flow .backdrop {
    z-index: -1;
    border-radius: inherit;
    background: linear-gradient(
        80.18deg,
        #d1ccdd 0%,
        #c893e1 25%,
        #eb47a7 50%,
        #f94a73 75%,
        #fb7a53 100%
    );
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.auth-flow .backdrop:after {
    content: '';
    background:
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%),
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%);
    background:
        linear-gradient(rgba(var(--background), 1) 0%, rgba(var(--background), 0.1) 100%),
        linear-gradient(rgba(var(--background), 1) 0%, rgba(var(--background), 0.1) 100%);
    position: absolute;
    inset: 0;
}
.auth-flow .back-button {
    z-index: 1;
    position: absolute;
    top: 14px;
    left: 14px;
}
.auth-flow .reset-password-option {
    justify-content: space-between;
    align-items: center;
    padding-left: 8px;
    display: flex;
}
.auth-flow .head {
    text-align: center;
    flex-direction: column;
    flex: 1;
    justify-content: center;
    align-items: center;
    gap: 16px;
    display: flex;
}
.auth-flow .head .logo {
    width: 64px;
}
.auth-flow .head svg {
    width: 48px;
    height: 48px;
}
.auth-flow .head span {
    text-wrap: pretty;
    width: 80%;
}
.auth-flow .inputs {
    flex-direction: column;
    gap: 12px;
    display: flex;
}
.auth-flow .inputs input {
    text-align: center;
    border-radius: 14px;
    width: 100%;
    padding: 12px;
}
.auth-flow .buttons {
    flex-direction: column;
    justify-content: flex-end;
    gap: 12px;
    display: flex;
}
.auth-flow .buttons .alternate-option {
    align-items: center;
    gap: 10px;
    display: flex;
}
.auth-flow .buttons .alternate-option:before,
.auth-flow .buttons .alternate-option:after {
    content: '';
    background: rgba(var(--primary), 0.12);
    flex: 1;
    height: 1px;
}
.auth-flow .terms-and-privacy {
    text-align: center;
    color: rgba(var(--primary), 0.6);
}
.auth-flow .terms-and-privacy a {
    color: rgba(var(--primary), 1);
}
.beta-login-page {
    z-index: 999;
    background: rgba(var(--background), 1);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
    display: flex;
    position: fixed;
    inset: 0;
}
.beta-login-page .beta-login-auth {
    width: 100%;
    height: 100%;
    position: relative;
}
.beta-login-page .auth-flow {
    border-radius: 0;
    outline: none;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    padding: 32px;
    display: flex;
    position: relative;
}
.beta-login-page .auth-flow > :not(.backdrop) {
    width: 100%;
    max-width: 340px;
}
.ad-container {
    background: rgba(var(--panel), 1);
    aspect-ratio: 3/2;
    cursor: pointer;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}
.ad-container:after {
    content: '';
    border-radius: inherit;
    pointer-events: none;
    border: solid 1px rgba(var(--primary), 0.12);
    z-index: 10;
    position: absolute;
    inset: 0;
}
.ad-container .image-container {
    width: 100%;
    height: 100%;
}
.ad-container .image-container img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.ad-container .close-button {
    background: rgba(var(--background), 0.8);
    width: 22px;
    height: 22px;
    position: absolute;
    top: 4px;
    right: 4px;
    padding: 0 !important;
}
.ad-container .close-button svg {
    width: 12px;
    height: 12px;
}
.ad-container .new-plans-cta {
    inset: 5px;
    top: unset;
    width: unset;
    background: rgba(var(--background), 0.8);
    pointer-events: none;
    position: absolute;
}
.ad-modal {
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
    overflow: hidden;
}
.ad-modal .cover {
    aspect-ratio: 3/2;
    width: 100%;
}
.ad-modal .cover img {
    object-fit: contain;
    width: 100%;
    height: 100%;
}
.ad-modal .content {
    text-align: center;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 32px;
    padding: 0 16px 16px;
    display: flex;
}
.ad-modal .content .bottom {
    gap: 12px;
    width: 100%;
    padding: 0 16px;
}
.ad-modal .content .bottom .email-address span {
    -webkit-user-select: all;
    user-select: all;
}
.ad-modal .content .perks {
    flex-direction: column;
    gap: 16px;
    width: 85%;
    display: flex;
}
.ad-modal .content .perks div {
    align-items: center;
    gap: 16px;
    display: flex;
}
.ad-modal .content .perks div > div {
    text-align: left;
    flex-direction: column;
    flex: 1;
    align-items: flex-start;
    gap: 4px;
    display: flex;
}
.ad-modal .content .perks div > div span:first-child {
    letter-spacing: -0.3px;
    font:
        16px/22px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .ad-modal .content .perks div > div span:first-child {
        letter-spacing: -0.2px;
        font:
            17px/22px Inter,
            sans-serif;
    }
}
.ad-modal .content .perks div > div span:nth-child(2) {
    color: rgba(var(--primary), 0.6);
    letter-spacing: -0.2px;
    font:
        14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .ad-modal .content .perks div > div span:nth-child(2) {
        letter-spacing: -0.07px;
        font:
            13px/18px Inter,
            sans-serif;
    }
}
.ad-modal .content .perks div svg {
    width: 32px;
    height: 32px;
}
.ad-modal .backdrop {
    z-index: -1;
    background: linear-gradient(
        80.18deg,
        #d1ccdd 0%,
        #c893e1 25%,
        #eb47a7 50%,
        #f94a73 75%,
        #fb7a53 100%
    );
    position: absolute;
    inset: 0;
}
.ad-modal .backdrop:after {
    content: '';
    background:
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%),
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%);
    position: absolute;
    inset: 0;
}
.mobile-install-prompt {
    z-index: 9999;
    background: rgba(var(--background), 1);
    align-items: center;
    justify-content: baseline;
    text-align: center;
    flex-direction: column;
    gap: 20px;
    padding: 16px;
    display: flex;
    position: fixed;
    inset: 0;
    overflow: hidden;
}
.mobile-install-prompt .logo {
    outline: solid 1px rgba(var(--primary), 0.1);
    outline-offset: -1px;
    border-radius: 16px;
    width: 64px;
    height: 64px;
    min-height: 64px;
    margin-bottom: -8px;
    overflow: hidden;
}
.mobile-install-prompt .logo img {
    width: 100%;
    height: 100%;
}
.mobile-install-prompt .texts {
    flex-direction: column;
    gap: 8px;
    display: flex;
}
.mobile-install-prompt .preview {
    aspect-ratio: 393/852;
    flex: 1;
    position: relative;
}
.mobile-install-prompt .preview .mask-layer {
    aspect-ratio: 393/852;
    background: rgba(var(--panel-dim), 1);
    position: relative;
    inset: 0;
    -webkit-mask-size: 100%;
    mask-size: 100%;
}
.mobile-install-prompt .preview .mask-layer video {
    width: 100%;
    height: 100%;
    position: absolute;
    inset: 0;
}
.mobile-install-prompt .android-preview {
    aspect-ratio: 393/803;
    z-index: 0;
    flex: 1;
    margin: -48px 0 12px;
    position: relative;
    -webkit-mask-image: linear-gradient(#0000 10%, #000 50%);
    mask-image: linear-gradient(#0000 10%, #000 50%);
    -webkit-mask-size: 100%;
    mask-size: 100%;
}
.mobile-install-prompt .android-preview img {
    aspect-ratio: 393/803;
    position: absolute;
    inset: 0;
}
.mobile-install-prompt .bottom {
    flex-direction: column;
    gap: 0;
    min-height: max-content;
    display: flex;
}
.mobile-install-prompt .backdrop {
    z-index: -1;
    background: linear-gradient(
        80.18deg,
        #d1ccdd 0%,
        #c893e1 25%,
        #eb47a7 50%,
        #f94a73 75%,
        #fb7a53 100%
    );
    position: absolute;
    inset: 0;
}
.mobile-install-prompt .backdrop:after {
    content: '';
    background:
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%),
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%),
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%),
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%);
    position: absolute;
    inset: 0;
}
.frame-scene-panel-button .rich-preview {
    border-radius: inherit;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.frame-scene-panel-button .rich-preview .label-wrapper {
    z-index: 1;
    border-radius: inherit;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2px;
    display: flex;
    position: absolute;
    inset: 0;
}
.frame-scene-panel-button .rich-preview .label-wrapper > span {
    z-index: 1;
    color: rgba(var(--primary), 1);
    letter-spacing: -0.4px;
    font:
        500 14px/20px Inter,
        sans-serif;
    position: relative;
    font-weight: 600 !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .frame-scene-panel-button .rich-preview .label-wrapper > span {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.frame-scene-panel-button .rich-preview .label-wrapper > p {
    text-transform: capitalize;
    color: rgba(var(--primary), 0.36);
    letter-spacing: -0.2px;
    margin-bottom: -3%;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .frame-scene-panel-button .rich-preview .label-wrapper > p {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.frame-lens-scene-button .rich-preview.default-mode {
    background: #ddd;
}
.frame-lens-scene-button .rich-preview.default-mode .label-wrapper > * {
    color: #000 !important;
}
.frame-lens-scene-button .rich-preview.stage-mode {
    background: #000;
}
.frame-lens-scene-button .rich-preview.stage-mode .label-wrapper > * {
    color: #fff !important;
}
.frame-lens-scene-button .rich-preview .label-wrapper .preview-lens-effect {
    z-index: 1;
    border-radius: inherit;
    position: absolute;
    inset: 0;
}
.frame-shadow-scene-button .rich-preview.preview-mode {
    background: #ddd;
}
.frame-shadow-scene-button .rich-preview.preview-mode .label-wrapper > * {
    color: #000 !important;
}
.frame-shadow-scene-button .rich-preview > img {
    object-fit: cover;
    width: 110%;
    height: 110%;
    transform: translate(-5%, -5%);
}
.frame-effect-scene-button .rich-preview.preview-mode .label-wrapper > * {
    color: #fff !important;
}
.frame-effect-scene-button .rich-preview > img {
    object-fit: cover;
    width: 110%;
    height: 110%;
    transform: translate(-5%, -5%);
}
.scene-popover-header {
    z-index: -1;
    align-items: flex-end;
    height: 100px;
    display: flex;
    position: relative;
}
.scene-popover-header span {
    z-index: 2;
    letter-spacing: -0.6px;
    font:
        500 17px/24px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .scene-popover-header span {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
.scene-popover-header img {
    object-fit: cover;
    object-position: top center;
}
.scene-popover-header:after {
    content: '';
    background:
        linear-gradient(0deg, rgba(var(--panel), 1) 10%, transparent 50%),
        linear-gradient(15deg, rgba(var(--panel), 1) 10%, transparent 50%);
    z-index: 1;
}
.scene-popover-header img,
.scene-popover-header:after {
    width: calc(100% + 24px);
    height: calc(100% + 52px);
    position: absolute;
    top: -12px;
    left: -12px;
}
.frame-shadow-scene-popover .bottom {
    background: linear-gradient(transparent 0%, rgba(var(--panel), 1) 40%);
    border-radius: inherit;
    flex-direction: column;
    gap: 10px;
    padding: 20px 10px 10px;
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}
.account-view {
    flex-direction: column;
    gap: 24px;
    display: flex;
}
.account-view .account-details {
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    display: flex;
}
.account-view .buttons {
    flex-direction: column;
    gap: 16px;
    display: flex;
}
.account-view button {
    background: rgba(var(--primary), 0.12);
}
.sub-card-backdrop,
.sub-card-blur {
    border-radius: inherit;
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.sub-card-backdrop.sub-card-blur,
.sub-card-blur.sub-card-blur {
    filter: blur(16px);
    z-index: -1;
    inset: 16px 8px 0;
}
.sub-card-backdrop:before,
.sub-card-blur:before {
    content: '';
    border-radius: inherit;
    background: linear-gradient(120deg, #ff6432 25%, #ff0065 45%, #7b2eff 75%);
    width: 200%;
    position: absolute;
    inset: 0;
}
.sub-card-backdrop:after,
.sub-card-blur:after {
    content: '';
    border-radius: inherit;
    opacity: 0.9;
    background: linear-gradient(#eee 0%, #0000 70%);
    position: absolute;
    inset: 0;
}
.sub-card-backdrop.plus-plan:before,
.sub-card-blur.plus-plan:before {
    left: 0;
}
.sub-card-backdrop.pro-plan:before,
.sub-card-blur.pro-plan:before {
    left: -60%;
}
.subscription-view {
    flex-direction: column;
    gap: 24px;
    display: flex;
}
.subscription-view .see-plans-btn {
    background: rgba(var(--primary), 0.12);
    justify-content: space-between;
}
.subscription-view .cancel-sub-section {
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    display: flex;
}
.subscription-view .cancel-sub-section button {
    background: rgba(var(--primary), 0.12);
}
.subscription-view .sub-card {
    aspect-ratio: 4/5;
    outline: solid 1.5px rgba(var(--primary), 0.12);
    outline-offset: -1.5px;
    color: #fff;
    border-radius: 24px;
    padding: 20px;
    position: relative;
}
.subscription-view .sub-card .card-content {
    z-index: 1;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
}
.subscription-view .sub-card .card-content .top {
    justify-content: space-between;
    align-items: flex-start;
    display: flex;
}
.subscription-view .sub-card .card-content .top .shots-logo {
    width: 48px;
}
.subscription-view .sub-card .card-content .bottom {
    flex-direction: column;
    gap: 10px;
    display: flex;
}
.subscription-view .sub-card .card-content .bottom .plan-details {
    justify-content: space-between;
    align-items: flex-end;
    display: flex;
}
.subscription-view .sub-card .card-content .bottom .plan-details > div {
    flex-direction: column;
    gap: 4px;
    display: flex;
}
.subscription-view .sub-card .card-content .bottom .plan-details > div .h1 {
    font-size: 36px;
}
.subscription-view .sub-card .card-content .bottom .plan-details .plan-price {
    align-items: center;
    gap: 4px;
    display: flex;
}
.subscription-view .sub-card .card-content .bottom .cycle-details {
    border-top: solid 1px rgba(var(--primary), 0.12);
    justify-content: space-between;
    padding-top: 10px;
    display: flex;
}
.subscription-view .sub-card .card-content .bottom .cycle-details span {
    letter-spacing: -0.2px;
    font:
        12.5px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .subscription-view .sub-card .card-content .bottom .cycle-details span {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.subscription-view .sub-card-drawer {
    background: rgba(var(--primary), 1);
    width: 100%;
    color: rgba(var(--secondary), 1);
    border-radius: 0 0 24px 24px;
    margin-top: -48px;
    padding-top: 24px;
}
.subscription-view .sub-card-drawer span,
.subscription-view .sub-card-drawer svg {
    color: rgba(var(--secondary), 1);
}
.subscription-view .sub-card-drawer .switch-to-yearly {
    padding: 8px 16px;
}
.subscription-view .sub-card-drawer .change-subscription {
    align-items: center;
    gap: 8px;
    padding: 16px 12px 16px 16px;
    display: flex;
}
.subscription-view .sub-card-drawer .change-subscription > svg {
    width: 24px;
    height: 24px;
}
.subscription-view .sub-card-drawer .change-subscription .copy {
    flex-direction: column;
    flex: 1;
    display: flex;
}
.subscription-view .sub-card-drawer .change-subscription button {
    background: rgba(var(--secondary), 0.1);
}
.subscription-view .sub-card-drawer .is-cancelling > svg {
    color: rgba(var(--danger), 1);
}
.subscription-view .sub-card-drawer .is-switching > svg {
    color: rgba(var(--info), 1);
}
.version-checker-wrapper {
    z-index: 9999;
    z-index: 999;
    justify-content: center;
    margin: 0 auto;
    display: flex;
    position: fixed;
    top: 6px;
    left: 240px;
    right: 240px;
}
.version-checker-wrapper:before {
    content: '';
    background: rgba(var(--background), 1);
    z-index: -1;
    height: 60px;
    position: absolute;
    inset: 0;
}
@media only screen and (width>=0) and (width<=800px) {
    .version-checker-wrapper {
        width: 100vw;
        top: 60px;
        left: 0 !important;
        right: 0 !important;
    }
    .version-checker-wrapper:before {
        display: none;
    }
}
.app-version-checker {
    background:
        linear-gradient(#00000026, #00000026),
        linear-gradient(140deg, #ff6432 12.8%, #ff0065 43.52%, #7b2eff 84.34%);
    overflow: hidden;
}
.app-version-checker .view-mini,
.app-version-checker .view-expand {
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: inherit;
    color: #fff;
    align-items: center;
    display: flex;
}
.app-version-checker .view-mini button,
.app-version-checker .view-expand button {
    color: #000;
    background: #fff;
}
.app-version-checker .view-mini {
    gap: 8px;
    padding: 10px 16px;
}
.app-version-checker .view-mini svg {
    width: 18px;
    height: 18px;
}
.app-version-checker .view-expand {
    flex-direction: column;
    gap: 16px;
    padding: 10px;
}
.app-version-checker .view-expand .texts {
    text-align: center;
    flex-direction: column;
    gap: 4px;
    margin-top: 4px;
    display: flex;
}
.shortcuts-popover .shortcut-group {
    flex-direction: column;
    display: flex;
}
.shortcuts-popover .shortcut-group .shortcut-group-title {
    margin-bottom: 8px;
}
.shortcuts-popover .shortcut-item {
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    display: flex;
}
.shortcuts-popover .shortcut-item:not(:last-child) {
    border-bottom: solid 1px rgba(var(--primary), 0.06);
}
.shortcuts-popover .shortcut-item .keys {
    align-items: center;
    gap: 6px;
    display: flex;
}
.shortcuts-popover .shortcut-item .keys span {
    letter-spacing: -0.5px;
    background: rgba(var(--primary), 1);
    width: max-content;
    min-width: 20px;
    height: 20px;
    color: rgba(var(--secondary), 1);
    border-radius: 5px;
    place-items: center;
    padding: 0 2px;
    font:
        500 14px/14px Inter,
        sans-serif;
    display: grid;
    position: relative;
}
.shortcuts-popover .shortcut-item .keys span:before {
    content: '';
    background: rgba(var(--primary), 0.36);
    z-index: -1;
    border-radius: 6px;
    position: absolute;
    inset: -1px;
    transform: translateY(1px);
}
.start-new-placeholder {
    z-index: 110;
    flex-direction: column;
    align-items: center;
    display: flex;
    position: relative;
}
.start-new-placeholder .backdrop {
    z-index: -1;
    background: #0000004d;
    position: fixed;
    inset: 0;
}
.start-new-component {
    background: rgba(var(--panel), 1);
    flex-direction: column;
    align-items: center;
    display: flex;
    position: relative;
    overflow: hidden;
}
.start-new-component .view-mini {
    cursor: pointer;
    justify-content: center;
    align-items: center;
    gap: 4px;
    display: flex;
}
.start-new-component .view-mini svg {
    width: 20px;
    height: 20px;
    margin-left: -4px;
}
.start-new-component .view-expand {
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    gap: 20px;
    padding: 14px;
    display: flex;
}
.start-new-component .view-expand .icon-image {
    width: 120px;
}
.start-new-component .view-expand .texts {
    text-align: center;
    flex-direction: column;
    gap: 6px;
    display: flex;
}
.start-new-component .view-expand .buttons {
    gap: 10px;
    width: 100%;
    margin-top: 4px;
    display: flex;
}
.start-new-component .view-expand .buttons button {
    flex: 1;
}
.animation-onboarding-container {
    z-index: 999;
    background: rgba(var(--background), 0.8);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    justify-content: center;
    align-items: center;
    display: flex;
    position: fixed;
    inset: 0;
}
.animation-onboarding-container .animation-onboarding {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 40px;
    width: 100vw;
    max-width: 1080px;
    height: 100vh;
    padding: 40px;
    display: flex;
    position: relative;
    overflow: hidden;
}
.animation-onboarding-container .animation-onboarding .buttons {
    flex-direction: column;
    gap: 16px;
    display: flex;
}
.animation-onboarding-container .animation-onboarding .buttons .templates-btn {
    padding: 6px;
}
.animation-onboarding-container .animation-onboarding .progress-indicators {
    gap: 8px;
    display: flex;
}
.animation-onboarding-container .animation-onboarding .progress-indicators .progress-indicator {
    background: rgba(var(--primary), 0.36);
    border-radius: 3px;
    width: 6px;
    height: 6px;
    transition: all 0.2s;
    overflow: hidden;
}
.animation-onboarding-container
    .animation-onboarding
    .progress-indicators
    .progress-indicator.is-active {
    width: 50px;
}
.animation-onboarding-container
    .animation-onboarding
    .progress-indicators
    .progress-indicator
    .progress {
    background: rgba(var(--primary), 0.6);
    height: 100%;
    transition: inherit;
}
.animation-onboarding-container .animation-onboarding .cards {
    align-items: center;
    gap: 10px;
    width: 100%;
    height: 100%;
    max-height: 560px;
    display: flex;
}
.animation-onboarding-container .animation-onboarding .tip-card {
    aspect-ratio: 9.5/16;
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: 28px;
    flex-direction: column;
    justify-content: flex-end;
    width: 100%;
    display: flex;
    position: relative;
    overflow: hidden;
}
.animation-onboarding-container .animation-onboarding .tip-card .media {
    z-index: -1;
    background: rgba(var(--panel-dim), 1);
    position: absolute;
    inset: 0;
}
.animation-onboarding-container .animation-onboarding .tip-card .media video,
.animation-onboarding-container .animation-onboarding .tip-card .media img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.animation-onboarding-container .animation-onboarding .tip-card .copy {
    z-index: 1;
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 32px 16px;
    display: flex;
}
.animation-onboarding-container .animation-onboarding .tip-card .copy > * {
    color: #ffffffd9;
    width: 80%;
}
.app-about {
    text-align: center;
    align-items: center;
}
.app-about .members-list {
    gap: 32px;
    display: flex;
}
.app-about .members-list .member {
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    display: flex;
}
.app-about .members-list .member:hover button {
    color: #45a1ee;
}
.app-about .members-list .member:hover > span {
    opacity: 0.5;
}
.app-about .members-list .member > span {
    color: rgba(var(--primary), 1);
    opacity: 0.3;
    font:
        500 36px/36px Comforter Brush,
        cursive;
}
.app-about .members-list .member button {
    color: rgba(var(--primary), 0.36);
}
.dropzone {
    width: max-content;
    height: max-content;
    position: relative;
}
.dropzone .dropped-image {
    object-fit: cover;
    object-position: top center;
    min-width: 100%;
    min-height: 100%;
    display: block;
}
.dropzone .file-drop {
    z-index: 2;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.dropzone .empty-state {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: inherit;
    display: flex;
    position: absolute;
    inset: 0;
}
.dropzone .empty-state svg {
    aspect-ratio: 1;
}
.dropzone .empty-state.dropping-state {
    background: rgba(var(--panel-dim), 1);
}
.dropzone .empty-state.dropping-state svg {
    width: 8em;
    height: 8em;
}
.dropzone .empty-state.select-state {
    background: rgba(var(--panel-dim), 0.9);
}
.dropzone .empty-state.select-state .icon svg {
    width: 7em;
}
.dropzone .empty-state.start-state .icons {
    position: relative;
}
.dropzone .empty-state.start-state .icons .icon {
    z-index: 1;
    position: relative;
}
.dropzone .empty-state.start-state .icons .icon svg {
    width: 7em;
}
.dropzone .empty-state.start-state .icons .image-icon,
.dropzone .empty-state.start-state .icons .video-icon {
    z-index: 0;
    position: absolute;
    -webkit-mask-image: linear-gradient(#000 40%, #0000 90%);
    mask-image: linear-gradient(#000 40%, #0000 90%);
}
.dropzone .empty-state.start-state .icons .image-icon svg,
.dropzone .empty-state.start-state .icons .video-icon svg {
    width: 9em;
}
.dropzone .empty-state.start-state .icons .image-icon {
    bottom: 40%;
    left: -80%;
    transform: scale(0.9) rotate(-8deg);
}
.dropzone .empty-state.start-state .icons .image-icon svg {
    color: rgba(var(--info), 1);
}
.dropzone .empty-state.start-state .icons .video-icon {
    bottom: 40%;
    right: -80%;
    transform: rotate(8deg);
}
.dropzone .empty-state.start-state .icons .video-icon svg {
    color: #ff342a;
}
.dropzone .empty-state .title {
    letter-spacing: -0.02em;
    color: rgba(var(--primary), 0.6);
    margin: 0.5em 0 0.6em;
    font:
        500 5em/100% Inter,
        sans-serif;
}
.dropzone .empty-state .subtitle {
    letter-spacing: -0.01em;
    color: rgba(var(--primary), 0.6);
    font:
        500 2.5em/100% Inter,
        sans-serif;
}
.dropzone .mobile-empty-drop svg {
    width: 16em !important;
    height: 16em !important;
}
.dropzone.active-state {
    filter: brightness(70%);
    animation: 0.6s infinite alternate highlight;
}
.dropzone.active-state:after {
    content: '';
    background: rgba(var(--primary), 0.12);
    pointer-events: none;
    position: absolute;
    inset: 0;
}
@keyframes highlight {
    to {
        filter: brightness(130%);
    }
}
.btn-dropzone {
    aspect-ratio: 5/4;
    background: rgba(var(--panel-dim), 1);
    border: dashed 1px rgba(var(--primary), 0.36);
    border-radius: 10px;
    width: 100%;
    position: relative;
    overflow: hidden;
}
@media only screen and (width>=0) and (width<=800px) {
    .btn-dropzone {
        aspect-ratio: 3/2;
    }
}
.btn-dropzone.active-dropzone {
    border-color: rgba(var(--primary), 1);
}
.btn-dropzone .dropped-image {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.btn-dropzone .file-drop {
    z-index: 2;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.btn-dropzone .file-drop:hover ~ .empty-drop .hovered {
    opacity: 1;
    visibility: visible;
}
.btn-dropzone .file-drop:hover ~ .empty-drop .default {
    opacity: 0;
    visibility: hidden;
}
.btn-dropzone .empty-drop,
.btn-dropzone .active-drop {
    position: absolute;
    inset: 0;
}
.btn-dropzone .empty-drop .content,
.btn-dropzone .active-drop .content {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 6px;
    transition: inherit;
    display: flex;
    position: absolute;
    inset: 0;
}
.btn-dropzone .empty-drop {
    background: rgba(var(--background), 0.8);
}
.btn-dropzone .empty-drop svg {
    width: 24px;
    height: 24px;
}
.btn-dropzone .empty-drop span {
    letter-spacing: -0.4px;
    font:
        500 15px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .btn-dropzone .empty-drop span {
        letter-spacing: -0.1px;
        font:
            500 15.5px/20px Inter,
            sans-serif;
    }
}
.btn-dropzone .empty-drop p {
    color: rgba(var(--primary), 0.6);
    letter-spacing: -0.2px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .btn-dropzone .empty-drop p {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.btn-dropzone .empty-drop .hovered {
    opacity: 0;
    visibility: hidden;
}
.btn-dropzone .active-drop {
    background: rgba(var(--panel), 1);
}
.btn-dropzone .active-drop svg {
    width: 32px;
    height: 32px;
}
.dropzone-edit-popover {
    box-shadow: none;
    background: 0 0;
    overflow: visible;
    width: calc(100vw - 488px) !important;
    margin: 0 auto !important;
    left: 228px !important;
    right: 228px !important;
}
.dropzone-edit-popover:after {
    border: none;
}
.dropzone-edit-popover:before {
    content: '';
    background:
        linear-gradient(0deg, transparent 0%, rgba(var(--background), 1) 80%),
        linear-gradient(0deg, transparent 0%, rgba(var(--background), 1) 80%);
    z-index: -1;
    pointer-events: none;
    position: absolute;
    inset: 0 0 -80%;
}
.dropzone-edit-popover .content {
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 16px;
    display: flex;
}
.dropzone-edit-popover .content .media-list {
    align-items: center;
    gap: 12px;
    display: flex;
}
.dropzone-edit-popover .content .media-list .new-asset {
    aspect-ratio: 1;
    background: rgba(var(--primary), 0.12);
    place-content: center;
    width: 62px;
    margin: 4px 12px;
    display: grid;
    position: relative;
}
.dropzone-edit-popover .content .media-list .new-asset svg {
    width: 28px;
    height: 28px;
    color: rgba(var(--primary), 0.6);
}
.dropzone-edit-popover .content .media-list .new-asset > span {
    text-align: center;
    color: rgba(var(--primary), 0.6);
    pointer-events: none;
    margin: 0 auto;
    position: absolute;
    bottom: -26px;
    left: 0;
    right: 0;
}
.dropzone-edit-popover .content .media-list .media-item {
    cursor: pointer;
    border-radius: 14px;
    padding: 8px;
    position: relative;
}
.dropzone-edit-popover .content .media-list .media-item.is-active .media-display img,
.dropzone-edit-popover .content .media-list .media-item.is-active .media-display video {
    outline-offset: 5px !important;
}
.dropzone-edit-popover .content .media-list .media-item.is-landscape {
    aspect-ratio: 5/4;
    width: 100px;
    min-width: 100px;
}
.dropzone-edit-popover .content .media-list .media-item.is-portrait {
    aspect-ratio: 3/4;
    width: 76px;
    min-width: 76px;
}
.dropzone-edit-popover .content .media-list .media-item .remove-button {
    z-index: 1;
    background: rgba(var(--modal), 1);
    color: rgba(var(--danger), 1);
    width: 40px;
    margin: 0 auto;
    position: absolute;
    top: -10px;
    left: 0;
    right: 0;
}
.dropzone-edit-popover .content .media-list .media-item .media-safearea {
    width: 100%;
    height: 100%;
    position: relative;
}
.dropzone-edit-popover .content .media-list .media-item .media-safearea .media-display {
    max-height: 100%;
    margin: auto;
    position: absolute;
    inset: 0;
}
.dropzone-edit-popover .content .media-list .media-item .media-safearea .media-display img,
.dropzone-edit-popover .content .media-list .media-item .media-safearea .media-display video {
    outline: solid 1px rgba(var(--primary), 0.12);
    outline-offset: -1px;
    border-radius: 8px;
    width: 100%;
    height: 100%;
}
.dropzone-edit-popover
    .content
    .media-list
    .media-item
    .media-safearea
    .media-display
    .media-upload-progress {
    z-index: 1;
    background: rgba(var(--panel), 0.5);
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: 8px;
    place-items: center;
    display: grid;
    position: absolute;
    inset: 0;
}
.dropzone-edit-popover .content .media-list .media-item .media-safearea .media-display .media-icon {
    z-index: 1;
    pointer-events: none;
    width: 20px;
    height: 20px;
    margin: 0 auto;
    position: absolute;
    bottom: -30px;
    left: 0;
    right: 0;
}
.dropzone-edit-popover
    .content
    .media-list
    .media-item
    .media-safearea
    .media-display
    .media-icon
    svg {
    width: inherit;
    height: inherit;
    color: rgba(var(--primary), 0.6);
}
@media only screen and (width>=1200px) {
    .dropzone-edit-popover .content .media-list .media-item .remove-button {
        opacity: 0;
        visibility: hidden;
    }
    .dropzone-edit-popover .content .media-list .media-item:hover {
        transform: scale(1.1) translateY(8px);
    }
    .dropzone-edit-popover .content .media-list .media-item:hover .remove-button {
        opacity: 1;
        visibility: visible;
    }
}
.dropzone-edit-popover .content .buttons {
    justify-content: center;
    gap: 8px;
    width: 100%;
    display: flex;
}
@media only screen and (width>=800px) and (width<=1200px) {
    .dropzone-edit-popover {
        width: 100vw !important;
        left: 0 !important;
        right: 0 !important;
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .dropzone-edit-popover {
        width: 100vw !important;
        left: 0 !important;
        right: 0 !important;
    }
    .dropzone-edit-popover:before {
        background:
            linear-gradient(180deg, transparent 0%, rgba(var(--background), 1) 60%),
            linear-gradient(180deg, transparent 0%, rgba(var(--background), 1) 60%);
        pointer-events: all;
        top: -100%;
        bottom: 0 !important;
    }
    .dropzone-edit-popover .content {
        padding-bottom: calc(16px + (env(safe-area-inset-bottom, 16px)));
        align-items: center;
        padding-top: 48px;
    }
    .dropzone-edit-popover .content .media-list {
        justify-content: center;
        align-items: center;
        width: calc(100% + 32px);
        margin: 0 -16px;
        padding: 20px 0 40px;
        overflow: auto hidden;
    }
    .dropzone-edit-popover .content .media-list .new-asset {
        min-width: 80px;
    }
}
.panel-control {
    flex-direction: column;
    gap: 8px;
    display: flex;
    position: relative;
}
.panel-control .controls {
    flex-direction: column;
    gap: 8px;
    transition: opacity 0.2s;
    display: flex;
}
.panel-control.disabled .controls {
    pointer-events: none;
    opacity: 0.5;
}
.panel-control-mobile {
    background:
        linear-gradient(transparent 0%, rgba(var(--background), 1) 25%),
        linear-gradient(transparent 0%, rgba(var(--background), 1) 50%);
    flex-direction: column;
    gap: 20px;
    padding: 20px;
    display: flex;
    position: relative;
}
.panel-control-segment-wrapper,
.panel-control-segment-wrapper .segment-section {
    flex-direction: column;
    gap: 20px;
    display: flex;
}
.panel-control-segment-wrapper .segment-buttons {
    justify-content: center;
    display: flex;
}
.panel-control-stack {
    width: calc(100% + 40px);
    margin: -20px;
    padding: 20px;
    overflow-x: auto;
}
.panel-control-stack .stack-content {
    gap: 12px;
    width: max-content;
    display: flex;
}
.panel-control-grid {
    width: 100%;
    display: grid;
}
.panel-control-grid > * {
    width: 100% !important;
}
.panel-control-grid.col-1 {
    grid-template-columns: 1fr;
    gap: 8px;
}
.panel-control-grid.col-2 {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}
.panel-control-grid.col-3 {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px;
}
.panel-control-grid.col-4 {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 6px;
}
.panel-button {
    text-align: center;
    cursor: pointer;
    flex-direction: column;
    gap: 0;
    width: 100%;
    height: max-content;
    display: flex;
    position: relative;
}
.panel-button .preview {
    background: rgba(var(--panel-dim), 1);
    outline-offset: 2px;
    border-radius: 10px;
    place-items: center;
    width: 100%;
    display: grid;
    position: relative;
    overflow: hidden;
}
.panel-button .preview:after {
    content: '';
    border-radius: inherit;
    position: absolute;
    inset: 0;
}
.panel-button .preview .image-wrapper {
    border-radius: inherit;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.panel-button .preview .image-wrapper img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.panel-button .preview .icon {
    width: 20px;
    height: 20px;
    color: rgba(var(--primary), 0.6);
}
.panel-button .preview .icon svg {
    width: inherit;
    height: inherit;
}
.panel-button .label-wrapper {
    align-items: center;
    padding: 4px;
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
}
.panel-button .label-wrapper .footnote {
    text-align: center;
    text-transform: capitalize;
    width: 100%;
    color: rgba(var(--primary), 0.6);
}
.panel-button.has-label {
    padding-bottom: 24px;
}
.panel-button.true-active .footnote {
    color: rgba(var(--primary), 0.8);
}
@media only screen and (width>=1200px) {
    .panel-button:hover .preview:after {
        background: rgba(var(--primary), 0.08);
    }
}
.shots-mobile-ui .panel-button {
    width: 76px;
}
.shots-mobile-ui .panel-button:active {
    opacity: 0.75;
    transform: scale(0.95);
}
.shots-mobile-ui .panel-button .preview {
    border-radius: 12px;
}
.shots-mobile-ui .panel-button .label-wrapper {
    padding: 12px 4px 0;
}
.pack-control-mobile .mesh-item,
.pack-control-mobile .gradient-item,
.pack-control-mobile .solid-item,
.pack-control-mobile .image-item {
    --size: 56px;
    --radius: 14px;
    width: var(--size);
    height: var(--size);
    margin-right: -2px;
}
.pack-control-mobile .mesh-item .display,
.pack-control-mobile .gradient-item .display,
.pack-control-mobile .solid-item .display,
.pack-control-mobile .image-item .display {
    border-radius: var(--radius) !important;
}
.pack-control-mobile .solid-item,
.pack-control-mobile .gradient-item {
    --radius: 12px;
    width: calc(var(--size) / 1.4);
}
.is-mobile-controls-open .canvas {
    height: 40% !important;
}
.shots-mobile-ui.app-main {
    flex-direction: column;
    gap: 0;
    padding: 0;
}
.shots-mobile-ui .canvas {
    border: none;
    height: 60%;
    margin-top: 100px;
    padding: 0;
    transition: height 0.4s;
}
.shots-mobile-ui .canvas .preview-frame {
    border-radius: 0;
}
.shots-mobile-ui .panel {
    z-index: 10;
    background: 0 0;
    border-radius: 0;
    flex-direction: column;
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    pointer-events: none !important;
}
.shots-mobile-ui .panel:before {
    display: none;
}
.shots-mobile-ui .panel .panel-tabs {
    margin-top: 12px;
    margin-bottom: calc(8px + env(safe-area-inset-bottom, 8px));
    background: 0 0;
    border-radius: 50px;
    order: 1;
    align-self: center;
    gap: 0;
    width: max-content;
    padding: 0;
    position: relative;
    pointer-events: all !important;
}
.shots-mobile-ui .panel .panel-tabs button {
    color: rgba(var(--primary), 0.36);
    border-radius: 40px;
    min-width: max-content;
    box-shadow: none !important;
    width: 104px !important;
    max-width: 104px !important;
    padding: 10px !important;
}
.shots-mobile-ui .panel .panel-tabs button span {
    letter-spacing: -0.4px;
    font:
        500 15px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .shots-mobile-ui .panel .panel-tabs button span {
        letter-spacing: -0.1px;
        font:
            500 15.5px/20px Inter,
            sans-serif;
    }
}
.shots-mobile-ui .panel .panel-scroll-view {
    border-radius: 0;
    order: 0;
    align-items: flex-end;
    position: relative;
    overflow: hidden;
}
.shots-mobile-ui .panel .panel-scroll-view .panel-fragment {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    height: max-content;
    pointer-events: all !important;
}
.shots-mobile-ui .panel .panel-scroll-view .panel-fragment .panel-fragment-scroll-view {
    height: max-content;
}
.shots-mobile-ui .panel .panel-scroll-view .panel-fragment .divide-line {
    display: none;
}
.shots-mobile-ui .panel .panel-controls-stack {
    max-width: 100%;
    height: 100%;
    gap: 0 !important;
    padding: 0 !important;
}
.shots-mobile-ui .panel .panel-controls-stack .panel-control-switcher {
    overflow-x: auto;
}
.shots-mobile-ui .panel .panel-controls-stack .panel-control-switcher .stack {
    gap: 2px;
    width: max-content;
    padding: 0 20px;
    display: flex;
}
.shots-mobile-ui .panel .panel-controls-stack .panel-control-switcher .stack .divider {
    background: rgba(var(--primary), 0.08);
    min-width: 1px;
    margin: 12px 4px;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile {
    border-radius: 12px;
    justify-content: flex-start;
    gap: 6px;
    max-width: 180px;
    height: 60px;
    padding-left: 2px;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .preview {
    place-items: center;
    display: grid;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .preview
    img {
    object-fit: cover;
    width: 40px;
    height: 48px;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .details {
    text-align: left;
    flex-direction: column;
    flex: 1;
    align-items: flex-start;
    gap: 2px;
    display: flex;
    overflow: hidden;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .details
    p {
    letter-spacing: -0.4px;
    font:
        500 14px/20px Inter,
        sans-serif;
    font-weight: 500 !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .shots-mobile-ui
        .panel
        .panel-controls-stack
        .panel-control-switcher
        .stack
        .panel-picker-button-mobile
        .details
        p {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .details
    > span {
    opacity: 0.5;
    text-transform: capitalize;
    color: inherit;
    letter-spacing: -0.2px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .shots-mobile-ui
        .panel
        .panel-controls-stack
        .panel-control-switcher
        .stack
        .panel-picker-button-mobile
        .details
        > span {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .icon {
    flex-direction: column;
    margin-left: 4px;
    display: flex;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .icon
    svg {
    opacity: 0.5;
    width: 12px;
    height: 10px;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .panel-picker-button-mobile
    .icon
    svg:first-child {
    rotate: 180deg;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .frame-picker-button-mobile
    .preview {
    width: 40px;
    height: 40px;
    padding: 4px;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .frame-picker-button-mobile
    .frame-preview {
    width: 100%;
    height: 100%;
    position: relative;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .frame-picker-button-mobile
    .frame-preview
    .current-frame-icon {
    background: rgba(var(--primary), 0.36);
    border: solid 1px rgba(var(--primary), 1);
    border-radius: 3px;
    max-height: 100%;
    margin: auto;
    transition: all 0.2s;
    position: absolute;
    inset: 0;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .frame-picker-button-mobile.true-active {
    color: rgba(var(--secondary), 1);
    background: rgba(var(--primary), 1) !important;
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .frame-picker-button-mobile.true-active
    .preview
    .frame-preview
    .current-frame-icon {
    filter: invert(100);
}
.shots-mobile-ui
    .panel
    .panel-controls-stack
    .panel-control-switcher
    .stack
    .frame-picker-button-mobile:hover {
    background: 0 0;
}
.shots-mobile-ui .mobile-control-switcher-button {
    border-radius: 12px !important;
    flex-direction: column !important;
    gap: 4px !important;
    width: 56px !important;
    height: 60px !important;
    padding: 2px !important;
}
.shots-mobile-ui .mobile-control-switcher-button span {
    letter-spacing: 0;
    text-transform: capitalize;
    font:
        500 10px/12px Inter,
        sans-serif;
}
.shots-mobile-ui .mobile-control-switcher-button svg {
    width: 30px;
    height: 30px;
}
.shots-mobile-ui .mobile-control-switcher-button.true-active {
    color: rgba(var(--secondary), 1);
    background: rgba(var(--primary), 1) !important;
}
.shots-mobile-ui .mobile-control-switcher-button .is-changed {
    z-index: 1;
    background: rgba(var(--primary), 1);
    pointer-events: none;
    border-radius: 6px;
    width: 4px;
    height: 4px;
    margin: 0 auto;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}
.shots-mobile-ui .mobile-backpack-item > span {
    order: 2;
}
.shots-mobile-ui .mobile-backpack-item .backpack-preview {
    width: 36px;
    height: 30px;
    margin-top: -2px;
    margin-bottom: 2px;
    position: relative;
}
.shots-mobile-ui .mobile-backpack-item .backpack-preview div {
    z-index: 3;
    border-radius: 5px;
    position: absolute;
    inset: 0;
}
.shots-mobile-ui .mobile-backpack-item .backpack-preview img {
    object-fit: cover;
    z-index: 3;
    border-radius: 5px;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.shots-mobile-ui .mobile-backpack-item .backpack-preview div:nth-child(2),
.shots-mobile-ui .mobile-backpack-item .backpack-preview img:nth-child(2) {
    z-index: 2;
    opacity: 0.5;
    transform: scaleX(1.04) rotate(-12deg);
}
.shots-mobile-ui .panel-backpack-switcher {
    overflow-x: auto;
}
.shots-mobile-ui .panel-backpack-switcher .stack {
    gap: 8px;
    width: max-content;
    display: flex;
}
.shots-mobile-ui .panel-backpack-switcher .stack .button.true-active {
    background: rgba(var(--primary), 1);
    color: rgba(var(--secondary), 1);
}
.shots-mobile-ui .panel-backpack-switcher .stack .button span {
    text-transform: capitalize;
}
.shots-mobile-ui .mobile-text-color {
    position: relative;
}
.shots-mobile-ui .mobile-text-color .panel-control-grid {
    flex-direction: row !important;
    gap: 12px !important;
}
.panel {
    border-radius: 16px;
    position: relative;
}
.panel:before {
    content: '';
    background: linear-gradient(transparent, rgba(var(--panel), 1));
    z-index: 10;
    pointer-events: none;
    border-radius: 0 0 16px 16px;
    height: 30px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}
.panel .panel-tabs {
    background: rgba(var(--panel), 1);
    inset: 0;
    bottom: unset;
    z-index: 99;
    border-radius: 16px 16px 0 0;
    justify-content: space-between;
    padding: 8px;
    display: flex;
    position: absolute;
}
.panel .panel-tabs .tabs {
    flex: 1;
    align-items: center;
    gap: 4px;
    display: flex;
}
.panel .panel-tabs .tabs button {
    color: rgba(var(--primary), 0.6);
    letter-spacing: -0.6px;
    flex: 1;
    gap: 6px;
    padding: 6px;
    font:
        500 17px/24px Inter,
        sans-serif;
    font-size: 16px !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .panel .panel-tabs .tabs button {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
@media only screen and (width>=1200px) {
    .panel .panel-tabs .tabs button:hover {
        background: rgba(var(--primary), 0.06);
    }
}
.panel .panel-tabs .tabs button svg {
    width: 18px;
    height: 18px;
}
.panel .panel-tabs .tabs .is-active {
    background: rgba(var(--primary), 0.06);
    color: rgba(var(--primary), 1) !important;
}
.panel .panel-selector {
    z-index: 1000;
    background:
        linear-gradient(0deg, rgba(var(--panel), 0) 0%, rgba(var(--panel), 1) 80%),
        linear-gradient(0deg, rgba(var(--panel), 0) 0%, rgba(var(--panel), 1) 80%);
    padding: 0 8px 20px;
    position: absolute;
    top: 52px;
    left: 0;
    right: 0;
}
.panel .panel-selector-btn-desktop {
    background: rgba(var(--modal), 1);
    border-radius: 12px;
    align-items: center;
    gap: 8px;
    width: 100%;
    height: 48px;
    padding: 10px 12px 10px 8px;
    display: flex;
    box-shadow: 0 6px 12px #00000029;
}
.panel .panel-selector-btn-desktop .current-mock img {
    aspect-ratio: 4/3;
    height: 34px;
}
.panel .panel-selector-btn-desktop .details {
    text-align: left;
    flex-direction: column;
    flex: 1;
    align-items: flex-start;
    gap: 2px;
    display: flex;
    overflow: hidden;
}
.panel .panel-selector-btn-desktop .details p {
    letter-spacing: -0.4px;
    font:
        500 14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .panel .panel-selector-btn-desktop .details p {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.panel .panel-selector-btn-desktop .details > span {
    color: rgba(var(--primary), 0.36);
    text-transform: capitalize;
    font:
        500 10px/14px Inter,
        sans-serif;
}
.panel .panel-selector-btn-desktop svg {
    width: 12px;
    height: 12px;
    color: rgba(var(--primary), 0.36);
}
.panel .panel-scroll-view {
    scroll-snap-type: x mandatory;
    border-radius: 16px;
    justify-content: space-between;
    align-items: stretch;
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
    overflow: auto hidden;
}
.panel .panel-scroll-view #mockupControls {
    scroll-snap-align: start;
}
.panel .panel-scroll-view #frameControls {
    scroll-snap-align: center;
}
.panel .panel-scroll-view #textControls {
    scroll-snap-align: end;
}
.panel .panel-scroll-view .panel-fragment {
    width: 228px;
    min-width: 228px;
    max-width: 228px;
    height: 100%;
    position: relative;
    overflow: hidden;
}
.panel .panel-scroll-view .panel-fragment .panel-fragment-scroll-view {
    width: 100%;
    height: 100%;
    overflow-y: auto;
}
.panel .panel-scroll-view .panel-fragment .panel-fragment-scroll-view.disable-panel-view {
    pointer-events: none;
    filter: grayscale(1) blur(2px); // grayscale中的1是我自己填充的
    opacity: 0.1;
    overflow: hidden;
}
.panel .panel-scroll-view .panel-fragment .panel-fragment-scroll-view .panel-controls-stack {
    flex-direction: column;
    gap: 20px;
    padding: 120px 10px 60px;
    display: flex;
}
.panel .panel-scroll-view .panel-fragment .divide-line {
    background: rgba(var(--primary), 0.06);
    background: linear-gradient(
        180deg,
        transparent 0%,
        rgba(var(--panel-dim), 1) 10%,
        rgba(var(--panel-dim), 1) 90%,
        transparent 100%
    );
    min-width: 1px;
    height: 100%;
    margin: 52px 6px;
}
.panel-empty-state {
    z-index: 5;
    border-radius: 16px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 12px;
    padding: 12px;
    display: flex;
    position: absolute;
    inset: 0;
}
.panel-empty-state .texts {
    text-align: center;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 8px;
    display: flex;
}
.panel-empty-state > svg {
    width: 36px;
}
@media only screen and (width>=0) and (width<=800px) {
    .panel-empty-state {
        flex-direction: row;
        height: max-content;
        margin-top: auto;
        padding: 12px 20px;
    }
    .panel-empty-state .texts {
        text-align: left;
        flex: 1;
    }
    .panel-empty-state > svg {
        width: 24px;
    }
    .panel-empty-state button {
        width: max-content;
    }
}
.panel-animation-disabled-state {
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    cursor: pointer;
    z-index: 9999;
    background: rgba(var(--panel), 0.9);
}
.export-button-placeholder {
    z-index: 100;
    width: 100%;
    height: 44px;
    display: flex;
    position: relative;
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button-placeholder {
        width: 132px;
        height: 48px;
    }
}
.export-button-cta {
    z-index: 1010;
    outline-offset: -1px;
    cursor: pointer;
    background: linear-gradient(
        80.18deg,
        #d1ccdd 0%,
        #c893e1 25%,
        #eb47a7 50%,
        #f94a73 75%,
        #fb7a53 100%
    );
    border-radius: 14px;
    outline: 1px solid #0000001f;
    align-items: center;
    gap: 8px;
    padding: 0 12px;
    display: flex;
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.export-button-cta:after {
    content: '';
    z-index: -1;
    background: linear-gradient(#fff9 0%, #0000 80%);
    position: absolute;
    inset: -1px;
}
.export-button-cta > * {
    z-index: 2;
    opacity: 0.9;
    color: #000;
}
.export-button-cta span {
    font-size: 16px;
}
.export-button-cta svg {
    width: 20px;
    height: 20px;
}
.export-button-cta.upgrade-cta svg {
    animation: 1s infinite alternate upgrade-arrow;
    transform: translateY(2px);
}
@keyframes upgrade-arrow {
    to {
        transform: translateY(-2px);
    }
}
.export-button {
    color: #fff;
    z-index: 10;
    background: #000;
    align-items: center;
    width: 100%;
    min-height: 44px;
    padding: 0 10px;
    transition: background 0.2s;
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    overflow: hidden;
    outline: 1.5px solid #ffffff14 !important;
}
.export-button.export-state {
    color: #000;
    cursor: pointer;
    background: #f0f0f0;
    outline: none;
}
.export-button.expanded {
    flex-direction: column;
    width: 124%;
    height: max-content;
    max-height: max-content;
    padding: 18px;
}
.export-button .export-button-safe-area {
    flex-direction: column;
    gap: 16px;
    width: 100%;
    display: flex;
    position: relative;
}
.export-button .export-state-compact,
.export-button .rendering-state,
.export-button .loading-state,
.export-button .export-state {
    align-items: center;
    gap: 8px;
    width: 100%;
    padding-left: 2px;
    display: flex;
}
.export-button .export-state-compact > h6,
.export-button .rendering-state > h6,
.export-button .loading-state > h6,
.export-button .export-state > h6 {
    text-transform: capitalize;
    letter-spacing: -0.6px;
    font:
        500 17px/24px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button .export-state-compact > h6,
    .export-button .rendering-state > h6,
    .export-button .loading-state > h6,
    .export-button .export-state > h6 {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
.export-button .export-state-compact > span,
.export-button .rendering-state > span,
.export-button .loading-state > span,
.export-button .export-state > span {
    color: #fff;
    letter-spacing: -0.2px;
    flex: 1;
    font:
        12.5px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button .export-state-compact > span,
    .export-button .rendering-state > span,
    .export-button .loading-state > span,
    .export-button .export-state > span {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.export-button .export-state-expanded,
.export-button .error-state,
.export-button .ready-state {
    flex-direction: column;
    gap: 12px;
    width: 100%;
    display: flex;
}
.export-button .export-state-expanded > h6,
.export-button .error-state > h6,
.export-button .ready-state > h6 {
    text-transform: capitalize;
    letter-spacing: -0.4px;
    font:
        500 19px/26px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button .export-state-expanded > h6,
    .export-button .error-state > h6,
    .export-button .ready-state > h6 {
        letter-spacing: -0.5px;
        font:
            500 19.5px/25px Inter,
            sans-serif;
    }
}
.export-button .export-state-expanded > span,
.export-button .error-state > span,
.export-button .ready-state > span {
    opacity: 0.6;
    color: #fff;
    letter-spacing: -0.2px;
    flex: 1;
    margin-top: -8px;
    font:
        12.5px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button .export-state-expanded > span,
    .export-button .error-state > span,
    .export-button .ready-state > span {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.export-button .export-state > span {
    color: #000;
}
.export-button .export-state .icon {
    width: 20px;
    height: 20px;
}
.export-button .export-state .icon svg {
    width: inherit;
    height: inherit;
}
.export-button .rendering-state > span,
.export-button .rendering-state > h6 {
    color: #f8af40;
}
.export-button .rendering-state > span {
    letter-spacing: -0.2px;
    font:
        14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button .rendering-state > span {
        letter-spacing: -0.07px;
        font:
            13px/18px Inter,
            sans-serif;
    }
}
.export-button .ready-state .close-button {
    color: #fff;
    background: #ffffff29;
    position: absolute;
    top: -5px;
    right: -5px;
}
.export-button .ready-state .display-preview {
    width: 88%;
    margin: 10px auto 20px;
}
.export-button .ready-state .display-preview video {
    object-fit: contain;
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: 10px;
    width: 100%;
    transform: rotate(-2deg);
}
.export-button .ready-state .export-buttons {
    gap: 8px;
    margin: 0 -4px -4px;
    display: flex;
}
.export-button .ready-state .export-buttons > button {
    flex: 1;
    padding: 10px 12px !important;
}
.export-button .ready-state .export-buttons .copy-link-btn {
    color: #fff;
    background: #ffffff1a;
}
.export-button .ready-state .export-buttons .download-btn {
    color: #000;
    background: #fff;
    justify-content: flex-start;
}
.export-button .ready-state .export-buttons .download-btn.is-loading {
    color: #fff !important;
    background: #ffffff1a !important;
    justify-content: center !important;
}
.export-button .error-state h6 {
    color: #f33;
}
.export-button .error-state svg {
    color: #f33;
    width: 20px;
    height: 20px;
    position: absolute;
    top: 0;
    right: 0;
}
.export-button .error-state .dismiss-btn {
    color: #fff;
    background: #ffffff29;
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button {
        width: calc(100vw - 24px);
        min-height: 48px;
        padding: 0 12px;
        outline-color: rgba(var(--primary), 0.06) !important;
        border-radius: 24px !important;
    }
    .export-button .export-button-safe-area {
        gap: 24px;
    }
    .export-button.expanded {
        width: calc(100vw - 24px);
        padding: 24px;
    }
    .export-button .export-state-compact,
    .export-button .export-state,
    .export-button .loading-state,
    .export-button .rendering-state {
        gap: 8px;
    }
    .export-button .export-state-compact > h6,
    .export-button .export-state > h6,
    .export-button .loading-state > h6,
    .export-button .rendering-state > h6 {
        text-transform: capitalize;
        letter-spacing: -0.6px;
        font:
            500 17px/24px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) and (width>=0) and (width<=800px) {
    .export-button .export-state-compact > h6,
    .export-button .export-state > h6,
    .export-button .loading-state > h6,
    .export-button .rendering-state > h6 {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button .export-state-compact > span,
    .export-button .export-state > span,
    .export-button .loading-state > span,
    .export-button .rendering-state > span {
        opacity: 0.4;
        letter-spacing: -0.2px;
        color: #fff;
        flex: 1;
        font:
            12.5px/20px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) and (width>=0) and (width<=800px) {
    .export-button .export-state-compact > span,
    .export-button .export-state > span,
    .export-button .loading-state > span,
    .export-button .rendering-state > span {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button .export-state-expanded,
    .export-button .ready-state,
    .export-button .error-state {
        flex-direction: column;
        gap: 12px;
        width: 100%;
        display: flex;
    }
    .export-button .export-state-expanded > h6,
    .export-button .ready-state > h6,
    .export-button .error-state > h6 {
        text-transform: capitalize;
        letter-spacing: -0.4px;
        font:
            500 19px/26px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) and (width>=0) and (width<=800px) {
    .export-button .export-state-expanded > h6,
    .export-button .ready-state > h6,
    .export-button .error-state > h6 {
        letter-spacing: -0.5px;
        font:
            500 19.5px/25px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button .export-state-expanded > span,
    .export-button .ready-state > span,
    .export-button .error-state > span {
        opacity: 0.6;
        letter-spacing: -0.2px;
        color: #fff;
        flex: 1;
        margin-top: -8px;
        font:
            12.5px/20px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) and (width>=0) and (width<=800px) {
    .export-button .export-state-expanded > span,
    .export-button .ready-state > span,
    .export-button .error-state > span {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button.export-state {
        width: calc(100% - 40px);
        min-height: 34px;
        padding: 0 4px;
        top: 6px;
        right: 40px;
        border-radius: 22px !important;
    }
    .export-button .export-state {
        gap: 4px;
    }
    .export-button .export-state > h6 {
        display: none;
    }
    .export-button .export-state > span {
        color: #000;
        letter-spacing: -0.2px;
        opacity: 1;
        font:
            12.5px/20px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) and (width>=0) and (width<=800px) {
    .export-button .export-state > span {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .export-button .export-state .icon {
        width: 18px;
        height: 18px;
    }
}
.mobile-export-settings-drop .drop-menu {
    border-radius: 20px;
    flex-direction: column;
    gap: 16px;
    padding: 14px;
    display: flex;
    right: -80px !important;
}
.mobile-export-progress-modal {
    flex-direction: column;
    align-items: center;
    gap: 28px;
    padding: 40px 16px 16px;
    display: flex;
    box-shadow: none !important;
    background: rgba(var(--secondary), 1) !important;
    border-radius: 36px !important;
}
.mobile-export-progress-modal .export-display {
    aspect-ratio: 4/3;
    width: 220px;
    margin: 28px 0 4px;
    position: relative;
}
.mobile-export-progress-modal .export-display .preview-frame {
    outline: solid 1px rgba(var(--primary), 0.12);
    border-radius: 10px;
    max-height: 100%;
    margin: auto;
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none !important;
}
.mobile-export-progress-modal .export-display .preview-frame.is-exporting:after {
    content: '';
    background: rgba(var(--primary), 0.12);
    pointer-events: none;
    z-index: 100;
    mix-blend-mode: overlay;
    background: linear-gradient(90deg, #0000 0%, #fff9 25% 75%, #0000 100%);
    width: 200%;
    animation: 2s infinite export-effect;
    position: absolute;
    inset: 0;
    transform: translate(-100%);
}
@keyframes export-effect {
    to {
        transform: translate(200%);
    }
}
.mobile-export-progress-modal .texts {
    text-align: center;
    flex-direction: column;
    gap: 6px;
    width: 100%;
    display: flex;
}
.mobile-export-progress-modal .texts span {
    text-transform: capitalize;
}
.mobile-export-progress-modal .progress-bar {
    background: rgba(var(--primary), 0.12);
    border-radius: 4px;
    width: 100%;
    height: 6px;
    display: flex;
    overflow: hidden;
}
.mobile-export-progress-modal .progress-bar div {
    background: linear-gradient(90deg, rgba(var(--primary), 0.36), rgba(var(--primary), 1));
    border-radius: inherit;
    height: 100%;
    transition: all 0.2s;
}
.mobile-export-progress-modal .loader-wrapper {
    justify-content: center;
    align-items: center;
    width: 70%;
    height: 48px;
    display: flex;
    transform: translateY(-16px);
}
.mobile-export-progress-modal .linear-loader {
    background: rgba(var(--primary), 0.12);
    border-radius: 3px;
    width: 100%;
    height: 6px;
    position: relative;
    overflow: hidden;
}
.mobile-export-progress-modal .linear-loader div {
    background: rgba(var(--primary), 0.6);
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(var(--primary), 1) 50%,
        transparent 100%
    );
    border-radius: inherit;
    width: 200%;
    height: 100%;
    animation: 1s linear infinite linear-loader;
    position: absolute;
    left: -200%;
}
@keyframes linear-loader {
    to {
        left: 100%;
    }
}
.mobile-export-progress-modal .share-buttons {
    gap: 8px;
    width: 100%;
    display: flex;
}
.mobile-export-progress-modal .share-buttons button {
    background: rgba(var(--primary), 0.12);
    flex: 1;
}
.copy-button-wrapper,
.export-settings-btn {
    z-index: 100;
    color: #000;
    cursor: pointer;
    border-radius: 14px;
    width: 40px;
    height: 44px;
    padding: 4px;
    position: absolute;
    top: 0;
}
.copy-button-wrapper {
    right: 36px;
}
@media only screen and (width>=1200px) {
    .copy-button-wrapper:hover .copy-button {
        background: #0003;
    }
}
.copy-button-wrapper .copy-button {
    color: rgba(var(--secondary), 1);
    pointer-events: none;
    background: #0000001a;
    border-radius: 10px;
    width: 100%;
    height: 100%;
}
.copy-button-wrapper .copy-button svg {
    color: #000;
    width: 22px;
    height: 22px;
}
.export-settings-btn {
    right: 0;
}
.export-settings-btn:before {
    content: '';
    z-index: -1;
    pointer-events: none;
    background: #0000001a;
    border-radius: 10px;
    transition: inherit;
    position: absolute;
    inset: 4px;
}
.export-settings-btn svg {
    width: 22px;
    height: 22px;
}
.export-settings-btn .danger {
    width: 18px;
    height: 18px;
    position: absolute;
    bottom: 4px;
    right: 4px;
}
.export-settings-btn .danger svg {
    width: inherit;
    height: inherit;
}
@media only screen and (width>=1200px) {
    .export-settings-btn:hover:before {
        background: #0003;
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .export-settings-btn {
        border-radius: 22px;
        width: 34px;
        height: 34px;
    }
    .export-settings-btn:before {
        display: none;
    }
    .export-settings-btn svg {
        width: 20px;
        height: 20px;
    }
}
.export-settings-drop .image-format button p {
    letter-spacing: 1px;
    font-weight: 500;
}
@media only screen and (width>=0) and (width<=800px) {
    .export-settings-drop {
        z-index: 10;
        background: #fff;
        border-radius: 50px;
        margin-top: 6px;
    }
}
.canvas {
    border: 1px solid #0000;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.canvas > .canvas-safe-area {
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
}
.canvas .preview-frame {
    border-radius: 14px;
    display: flex;
    position: relative;
}
.canvas .preview-frame:after {
    content: '';
    z-index: 5;
    border: solid 1px rgba(var(--primary), 0.06);
    border-radius: inherit;
    pointer-events: none;
    position: absolute;
    inset: 0;
}
.canvas .preview-frame:hover .panel-resize-handle {
    visibility: visible;
    opacity: 1;
}
@media only screen and (width>=1920px) {
    .canvas {
        padding: 20px 88px 88px;
    }
}
@media only screen and (width>=1200px) and (width<=1920px) {
    .canvas {
        padding: 6px 16px 66px;
    }
}
@media only screen and (width>=800px) and (width<=1200px) {
    .canvas {
        padding: 6px 16px 66px;
    }
}
.display-container-single {
    z-index: 10;
    width: 100%;
    height: 100%;
}
.frame {
    display: flex;
    position: relative;
    overflow: hidden;
    container: frame-container/size;
}
.frame .frame-content {
    width: 100%;
    height: 100%;
}
@container frame-container (width>0) {
    .frame .frame-content {
        font-size: calc(0.666667cqw + 0.666667cqh);
    }
}
.frame .frame-background-display {
    z-index: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.frame .frame-background-display .frame-background {
    object-fit: cover;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.frame .frame-effect-layer {
    z-index: 15;
    pointer-events: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.frame .frame-noise {
    mix-blend-mode: overlay;
    z-index: 2;
    pointer-events: none;
    // background: url(https://assets.shots.so/canvas/noise2.svg) 0 0/50%;
    position: absolute;
    inset: 0;
}
.frame .frame-blur {
    z-index: 1;
    pointer-events: none;
    position: absolute;
    inset: 0;
}
.frame .lens-scene-layer {
    pointer-events: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 12 !important;
}
.frame .lens-scene-backdrop-layer {
    pointer-events: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0 !important;
}
.frame .shadow-scene-layer {
    z-index: 9;
    pointer-events: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.frame .shadow-scene-layer.is-overlay {
    z-index: 11;
}
.frame .shadow-scene-layer img {
    object-fit: cover;
}
.frame .shape-scene-object {
    pointer-events: none;
    width: 30em;
    height: auto;
}
.frame .blur-mask {
    z-index: 3;
    pointer-events: none;
    display: none;
    position: absolute;
    inset: 0;
}
.frame .blur-mask:before {
    content: '';
    z-index: 1;
    -webkit-backdrop-filter: blur(16em);
    backdrop-filter: blur(16em);
    position: absolute;
    inset: 0;
}
.animation-preview-tag {
    pointer-events: none;
    top: 80px;
    left: 0;
    right: 0;
    bottom: unset;
    z-index: 10;
    width: max-content;
    color: rgba(var(--primary), 1);
    outline-offset: -1px;
    letter-spacing: -0.4px;
    background: #ff342a;
    border-radius: 20px;
    outline: 1px solid #0003;
    align-items: center;
    gap: 6px;
    margin: 0 auto;
    padding: 5px 11px;
    font:
        500 14px/20px Inter,
        sans-serif;
    display: flex;
    position: fixed;
    box-shadow: 0 4px 8px #00000029;
}
@media only screen and (width>=0) and (width<=800px) {
    .animation-preview-tag {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.animation-preview-tag svg {
    width: 20px;
    height: 20px;
}
.select-layer {
    cursor: pointer;
    -webkit-backdrop-filter: blur(4em);
    backdrop-filter: blur(4em);
    filter: contrast(80%);
    opacity: 0;
    pointer-events: all;
    border: solid rgba(var(--primary), 0.36);
    background: #0000001a;
    border-radius: 1.6em;
    position: absolute;
    inset: 0;
}
.select-layer:hover {
    opacity: 1;
}
.select-layer.mockup-select-layer {
    z-index: 0;
}
.select-layer.frame-select-layer {
    z-index: 2;
}
.select-layer.text-select-layer {
    z-index: 0;
}
.panel-resize-handle {
    z-index: 10;
    filter: contrast(80%);
    visibility: hidden;
    opacity: 0;
    justify-content: center;
    align-items: center;
    display: flex;
    position: relative;
}
.panel-resize-handle.vertical {
    height: 0;
}
.panel-resize-handle.vertical:after {
    cursor: ns-resize;
    width: 60px;
    height: 5px;
}
.panel-resize-handle.horizontal {
    width: 0;
}
.panel-resize-handle.horizontal:after {
    cursor: ew-resize;
    min-width: 5px;
    height: 60px;
}
.panel-resize-handle:after {
    content: '';
    -webkit-backdrop-filter: blur(4em);
    backdrop-filter: blur(4em);
    background: #fff;
    border-radius: 100px;
}
.watermark-icon-button .preview-image {
    padding: 8px;
    position: absolute;
    inset: 0;
}
.watermark-icon-button .preview-image img {
    object-fit: contain;
    width: 100%;
    height: 100%;
}
.watermark-icon-button svg {
    z-index: 1;
}
.watermark-color-list {
    gap: 8px;
    display: flex;
}
.watermark-color-button {
    aspect-ratio: 1;
    width: 28px;
}
.watermark-color-button .color-display {
    border-radius: inherit;
    outline-offset: -1px;
    outline: 1px solid #6464644d;
    position: absolute;
    inset: 0;
}
.watermark-layout-control {
    aspect-ratio: 2;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    display: flex;
    position: relative;
}
.watermark-layout-control > button {
    z-index: 3;
    background: rgba(var(--secondary), 0.2) !important;
}
.watermark-layout-control .backdrop {
    z-index: 0;
    pointer-events: none;
    position: absolute;
    inset: 0 -16px -32px;
    overflow: hidden;
}
.watermark-layout-control .backdrop:after {
    content: '';
    z-index: 1;
    background: linear-gradient(to bottom, rgba(var(--panel), 0.3) 50%, rgba(var(--panel), 1) 100%);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    position: absolute;
    inset: 0;
}
.watermark-layout-control .backdrop img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.watermark-layout-control .watermark-layout-indicators {
    z-index: 3;
    pointer-events: none;
    justify-content: center;
    gap: 5px;
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}
.watermark-layout-control .watermark-layout-indicators span {
    aspect-ratio: 1;
    background: rgba(var(--primary), 0.12);
    border-radius: 50%;
    width: 5px;
}
.watermark-layout-control .watermark-layout-indicators span.is-active {
    background: rgba(var(--primary), 0.6);
}
.watermark-layout-control .items-scroll {
    scroll-snap-type: x mandatory;
    pointer-events: none;
    position: absolute;
    inset: 0;
    overflow: auto hidden;
    -webkit-mask-image: linear-gradient(90deg, #0000 0%, #000 20% 80%, #0000 100%);
    mask-image: linear-gradient(90deg, #0000 0%, #000 20% 80%, #0000 100%);
    container-type: inline-size;
}
.watermark-layout-control .items-scroll .all-items {
    align-items: center;
    gap: 10px;
    width: max-content;
    height: 100%;
    display: flex;
}
.watermark-layout-control .items-scroll .all-items .watermark-layout-item {
    scroll-snap-align: center;
    width: 100cqw;
    min-width: 100cqw;
    height: 100%;
    position: relative;
}
.watermark-layout-control
    .items-scroll
    .all-items
    .watermark-layout-item
    .custom-watermark-display {
    z-index: 1;
    place-items: center;
    width: 100%;
    height: 100%;
    font-size: 0.45em;
    display: grid;
}
.watermark-content-control {
    gap: 6px;
    display: flex;
}
.watermark-content-control .watermark-icon-button {
    width: 38px;
}
.watermark-content-control .input-box {
    flex: 1;
}
.watermark-layout-button .custom-watermark-display {
    place-items: center;
    font-size: 0.4em;
    display: grid;
}
.watermark-position-button {
    flex-direction: column;
    align-items: center;
    gap: 6px;
    display: flex;
}
.watermark-position-button .position-display {
    cursor: pointer;
    aspect-ratio: 1;
    background: rgba(var(--panel-dim), 1);
    border-radius: 14px;
    width: 68px;
}
.watermark-position-button .position-display .knob-area {
    width: 100%;
    height: 100%;
    position: relative;
}
.watermark-position-button .position-display .knob-area div {
    aspect-ratio: 4/5;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10%;
    width: 32px;
    display: flex;
}
.watermark-position-button .position-display .knob-area div svg {
    color: rgba(var(--primary), 1);
    aspect-ratio: 1;
    width: 55%;
}
.watermark-position-button .position-display .knob-area div:before {
    content: '';
    background: rgba(var(--panel-active), 1);
    aspect-ratio: 5;
    border-radius: 10px;
    order: 2;
    width: 60%;
}
.custom-watermark-display {
    z-index: 20;
    pointer-events: none;
    position: absolute;
    inset: 0;
}
.custom-watermark-display .watermark-element {
    --image-display: none;
    --image-height: 4em;
    --title-display: none;
    --title-size: 2em;
    --subtitle-display: none;
    --subtitle-size: 1.5em;
    position: relative;
}
.custom-watermark-display .watermark-element .image-placeholder {
    display: var(--image-display);
    height: var(--image-height);
    min-height: var(--image-height);
    position: relative;
}
.custom-watermark-display .watermark-element .image-placeholder img {
    height: var(--image-height);
    object-fit: contain;
    border-radius: 0.8em;
    width: auto;
}
.custom-watermark-display .watermark-element .image-placeholder.no-image {
    aspect-ratio: 1;
    background: #64646480;
    border-radius: 1.2em;
    padding: 1em;
}
.custom-watermark-display .watermark-element .image-placeholder.no-image svg {
    width: 100%;
    height: 100%;
    color: rgba(var(--primary), 0.36);
}
.custom-watermark-display .watermark-element .title-text {
    display: var(--title-display);
    font-size: var(--title-size);
    letter-spacing: -0.01em;
    font-family: Inter, sans-serif;
    font-weight: 500;
    line-height: 100%;
}
.custom-watermark-display .watermark-element .subtitle-text {
    display: var(--subtitle-display);
    font-size: var(--subtitle-size);
    letter-spacing: -0.01em;
    font-family: Inter, sans-serif;
    font-weight: 500;
    line-height: 100%;
}
.custom-watermark-display .watermark-none {
    --title-display: initial;
    --title-size: 2em;
    align-items: center;
    gap: 1em;
    display: flex;
}
.custom-watermark-display .watermark-none span,
.custom-watermark-display .watermark-none svg {
    color: rgba(var(--primary), 0.6);
}
.custom-watermark-display .watermark-none svg {
    width: 3em;
    height: 3em;
}
.custom-watermark-display .watermark-layout-1 {
    --image-display: initial;
    --title-display: initial;
    --image-height: 4em;
    --title-size: 2em;
    --subtitle-display: initial;
    --subtitle-size: 1.4em;
    align-items: center;
    gap: 1em;
    display: flex;
}
.custom-watermark-display .watermark-layout-1 .texts-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.7em;
    display: flex;
}
.custom-watermark-display .watermark-layout-2 {
    --image-display: block;
    --image-height: 5em;
}
.custom-watermark-display .watermark-layout-3 {
    --title-display: initial;
    --title-size: 2em;
    --subtitle-display: initial;
    --subtitle-size: 1.5em;
}
.custom-watermark-display .watermark-layout-3 .texts-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 0.8em;
    display: flex;
}
.custom-watermark-display .watermark-style-default.watermark-light {
    color: #fff !important;
}
.custom-watermark-display .watermark-style-default.watermark-dark {
    color: #000 !important;
}
.custom-watermark-display .watermark-style-1 .texts-wrapper {
    color: #fff !important;
}
.custom-watermark-display .watermark-style-1:before {
    content: '';
    z-index: -1;
    filter: blur(3em);
    background: #0009;
    border-radius: 30%;
    position: absolute;
    inset: -15%;
}
.custom-watermark-display .watermark-style-2 {
    padding: 1em 2em;
}
.custom-watermark-display .watermark-style-2:before {
    content: '';
    z-index: -1;
    -webkit-backdrop-filter: blur(5em) saturate(150%);
    backdrop-filter: blur(5em) saturate(150%);
    outline-offset: -0.15em;
    background: #6464644d;
    border-radius: 5em;
    outline: 0.15em solid #b4b4b44d;
    position: absolute;
    inset: 0;
}
.custom-watermark-display .watermark-style-2.watermark-layout-2 {
    padding: 1em;
}
.custom-watermark-display .watermark-style-2.watermark-layout-2:before {
    border-radius: 2em;
}
.custom-watermark-display .watermark-style-2.watermark-layout-3 {
    padding: 1.5em;
}
.custom-watermark-display .watermark-style-2.watermark-light {
    color: #fff !important;
}
.custom-watermark-display .watermark-style-2.watermark-light:before {
    background: #6464644d;
    outline-color: #b4b4b44d;
}
.custom-watermark-display .watermark-style-2.watermark-dark {
    color: #fff !important;
}
.custom-watermark-display .watermark-style-2.watermark-dark:before {
    background: #1e1e1e4d;
    outline-color: #7878784d;
}
.custom-watermark-display .watermark-style-3 {
    outline-offset: -0.25em;
    background: #fff;
    border-radius: 1.8em;
    outline: 0.25em solid #0003;
    padding: 1em;
}
.custom-watermark-display .watermark-style-3.watermark-layout-3 {
    padding: 1.5em;
}
.custom-watermark-display .watermark-style-3.watermark-light {
    background: #fff;
    color: #000 !important;
}
.custom-watermark-display .watermark-style-3.watermark-dark {
    background: #000;
    outline-color: #ffffff1f;
    color: #fff !important;
}
.layout-panel {
    overflow: hidden;
}
.layout-panel .layout-filters {
    gap: 4px;
    display: flex;
}
.layout-panel .modal-view {
    width: 100%;
    height: 100%;
}
.layout-panel.disable-panel-view {
    pointer-events: none;
    filter: grayscale(1) blur(1px); // grayscale中的1是我自己填充的
    opacity: 0.1;
    overflow: hidden;
}
.layout-item {
    background: rgba(var(--primary), 0.06);
    cursor: pointer;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}
.layout-item.layout-item-3d .device-asset {
    display: none !important;
}
.layout-item.text-layout-item > .frame {
    transform: scale(1.8);
}
.layout-item .layout-frame * {
    pointer-events: none !important;
}
.layout-item .layout-frame .empty-drop * {
    visibility: hidden;
    display: none;
}
.layout-item > .tag {
    z-index: 10;
    position: absolute;
    bottom: 6px;
    left: 6px;
}
.animation-control-top-bar {
    z-index: 20;
    flex-direction: column;
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}
.animation-control-top-bar .manage-controls {
    background: rgba(var(--panel), 1);
    align-items: center;
    gap: 8px;
    padding: 8px;
    display: flex;
}
.animation-control-top-bar .manage-controls .h5 {
    flex: 1;
    font-weight: 600;
}
.animation-control-top-bar .manage-controls .back-btn {
    background: rgba(var(--primary), 0.06);
}
.animation-control-top-bar .manage-controls .nav-btn {
    letter-spacing: -0.6px;
    gap: 6px;
    padding: 9px;
    font:
        500 17px/24px Inter,
        sans-serif;
    font-size: 16px !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .animation-control-top-bar .manage-controls .nav-btn {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
.animation-control-top-bar .manage-controls .nav-btn svg {
    width: 18px;
    height: 18px;
}
.animation-control-top-bar .control-wrapper {
    background:
        linear-gradient(0deg, transparent 0%, rgba(var(--panel), 1) 50%),
        linear-gradient(0deg, transparent 0%, rgba(var(--panel), 1) 50%);
    flex-direction: column;
    padding: 2px 10px 16px;
    display: flex;
}
.animation-transition-control {
    align-items: center;
    gap: 4px;
    display: flex;
}
.animation-transition-control > * {
    width: 100%;
}
.animation-transition-control button {
    max-width: 30px !important;
    height: 30px !important;
    padding: 0 !important;
}
.animation-transition-control button > svg {
    width: 22px !important;
    height: 22px !important;
}
.layouts-panel-controls {
    inset: 0;
    top: unset;
    z-index: 20;
    background:
        linear-gradient(transparent 0%, rgba(var(--panel), 1) 50%),
        linear-gradient(transparent 0%, rgba(var(--panel), 1) 50%);
    position: absolute;
}
.layouts-panel-controls:before,
.layouts-panel-controls:after {
    content: '';
    pointer-events: none;
    opacity: 0;
    position: absolute;
}
.layouts-panel-controls:before {
    background: rgba(var(--panel), 1);
    inset: 10px 0 0;
}
.layouts-panel-controls:after {
    background: linear-gradient(transparent, rgba(var(--panel), 1));
    height: 40px;
    top: -30px;
    left: 0;
    right: 0;
}
.layouts-panel-controls.expanded:before,
.layouts-panel-controls.expanded:after {
    opacity: 1 !important;
}
.layouts-panel-controls .transform-controls-wrapper {
    flex-direction: column;
    gap: 8px;
    height: max-content;
    padding: 10px;
    display: flex;
    position: relative;
    overflow: hidden;
}
.layouts-panel-controls .transform-controls-wrapper .position-controls,
.layouts-panel-controls .transform-controls-wrapper .advanced-controls {
    flex-direction: column;
    gap: 10px;
    display: flex;
}
.layouts-panel-controls .transform-controls-wrapper .position-controls .position-pad-safearea {
    justify-content: center;
    align-items: flex-end;
    display: flex;
    position: relative;
}
.layouts-panel-controls .transform-controls-wrapper .position-controls.minimized-controls > * {
    pointer-events: none;
}
.layouts-panel-controls
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .drag-pad-wrapper {
    border-radius: 40px;
}
.layouts-panel-controls
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .drag-pad-wrapper
    .drag-pad
    .drag-handle:after {
    transform: scale(1.8) !important;
}
.layouts-panel-controls
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .slider-component {
    border-radius: 100px;
}
.layouts-panel-controls
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .slider-component
    .labels {
    opacity: 0;
}
.layouts-panel-controls .transform-controls-wrapper .advanced-controls {
    background: rgba(var(--panel), 1);
    border-top: 1px solid rgba(var(--primary), 0.06);
    padding-top: 8px;
}
.mockup-layout-3d-alert-graphic {
    pointer-events: none;
    align-items: center;
    gap: 12px;
    margin-top: 12px;
    display: flex;
}
.mockup-layout-3d-alert-graphic > svg {
    color: rgba(var(--primary), 0.5);
    width: 20px;
    height: 20px;
    margin-bottom: 20px;
}
.layouts-panel-controls-top {
    inset: 0;
    bottom: unset;
    z-index: 20;
    position: absolute;
}
.layouts-panel-controls-top:before,
.layouts-panel-controls-top:after {
    content: '';
    z-index: -1;
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}
.layouts-panel-controls-top.minimized-backdrop:before {
    background: rgba(var(--panel), 1);
    height: calc(100% - 10px);
    top: calc(10px - 100%);
}
.layouts-panel-controls-top.minimized-backdrop:after {
    background:
        linear-gradient(rgba(var(--panel), 1) 30%, transparent 100%),
        linear-gradient(rgba(var(--panel), 1) 30%, transparent 100%);
    height: 100px;
}
.layouts-panel-controls-top.expanded-backdrop:before {
    background: rgba(var(--panel), 1);
    height: calc(100% - 10px);
}
.layouts-panel-controls-top.expanded-backdrop:after {
    background: linear-gradient(rgba(var(--panel), 1), transparent);
    height: 40px;
    top: calc(100% - 10px);
}
.layouts-panel-controls-top .transform-controls-wrapper {
    pointer-events: none;
    flex-direction: column;
    gap: 8px;
    height: max-content;
    padding: 10px;
    display: flex;
    position: relative;
    overflow: hidden;
}
.layouts-panel-controls-top .transform-controls-wrapper .position-controls {
    pointer-events: all;
    flex-direction: column;
    gap: 16px;
    display: flex;
}
.layouts-panel-controls-top .transform-controls-wrapper .position-controls .position-pad-safearea {
    background: rgba(var(--panel-dim), 1);
    border-radius: 14px;
    flex-direction: column;
    align-items: center;
    display: flex;
    position: relative;
}
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls
    .position-pad-safearea
    > * {
    width: 100%;
}
.layouts-panel-controls-top .transform-controls-wrapper .position-controls.minimized-controls {
    filter: drop-shadow(0 10px 10px #0009);
}
.layouts-panel-controls-top .transform-controls-wrapper .position-controls.minimized-controls > * {
    pointer-events: none;
}
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .transition-button,
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .switch,
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .slider-component {
    outline: solid 2px rgba(var(--primary), 0.12);
    outline-offset: -2px;
}
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .zoom-panel-control {
    transform: translateY(-30px);
}
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .zoom-panel-control
    > .label {
    visibility: hidden !important;
}
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .position-pad-safearea,
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .drag-pad-wrapper {
    border-radius: 28px;
}
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .drag-pad-wrapper
    .drag-pad {
    outline: solid 2px rgba(var(--primary), 0.12);
    outline-offset: 2px;
    border-radius: 24px;
}
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .drag-pad-wrapper
    .drag-pad
    .drag-handle:after {
    transform: scale(1.8) !important;
}
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .slider-component {
    border-radius: 12px;
}
.layouts-panel-controls-top
    .transform-controls-wrapper
    .position-controls.minimized-controls
    .slider-component
    .labels {
    opacity: 0;
}
.layouts-panel-nav-controls {
    z-index: 30;
    background: rgba(var(--panel), 1);
    align-items: center;
    gap: 8px;
    padding: 8px 8px 8px 14px;
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}
.layouts-panel-nav-controls .h5 {
    flex: 1;
    font-weight: 600;
}
.layouts-panel-transform-selectors {
    justify-content: center;
    gap: 8px;
    display: flex;
}
.layouts-panel-transform-selectors .panel-button {
    width: 52px;
}
.layouts-panel-transform-selectors .panel-button .preview {
    background: 0 0;
    aspect-ratio: 4/3 !important;
}
.mockup-zoom-selector-button .mockup-transform-icon {
    place-items: center;
    width: 100%;
    height: 100%;
    display: grid;
}
.mockup-zoom-selector-button .mockup-transform-icon svg {
    width: 24px;
    height: 24px;
}
.mockup-transform-selector-button img {
    width: 100%;
    height: 100%;
}
.navbar {
    z-index: 100;
    background: rgba(var(--panel), 1);
    border-radius: 14px;
    justify-content: space-between;
    align-items: center;
    height: 44px;
    padding: 4px;
    display: flex;
    position: relative;
}
@media only screen and (width>=0) and (width<=800px) {
    .navbar {
        margin-top: env(safe-area-inset-top, 0);
        height: 52px;
        box-shadow: none;
        background: 0 0;
        border-radius: 0;
        padding: 8px 12px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
    }
}
.navbar .mobile-menu {
    gap: 3px !important;
    padding: 3px 4px !important;
}
.navbar .mobile-menu .logo {
    pointer-events: none;
    width: auto;
    height: 30px;
}
.navbar .mobile-menu > svg {
    width: 16px;
    height: 16px;
    color: rgba(var(--primary), 0.6);
}
.navbar .templates-btn {
    gap: 3px !important;
    padding: 1px 4px !important;
}
.navbar .left,
.navbar .center,
.navbar .right {
    align-items: center;
    gap: 6px;
    display: flex;
}
.navbar .left {
    flex: 1;
    justify-content: flex-start;
}
.navbar .right {
    flex: 1;
    justify-content: flex-end;
}
.mock-orientation-buttons {
    margin: 0 auto;
    display: flex;
}
.mock-style-3d-tag {
    background: rgba(var(--panel-active), 1);
    max-width: max-content;
    color: rgba(var(--primary), 1);
    opacity: 0.4;
    z-index: 1;
    background: 0 0;
    border-radius: 5px;
    position: absolute;
    bottom: 4px;
    right: 4px;
    padding: 1px 3px !important;
    font-weight: 600 !important;
}
.mock-assets-control {
    justify-content: space-evenly;
    gap: 7px;
    display: flex;
}
.mock-assets-control.items-1 .display-preview {
    max-width: 45%;
}
.mock-assets-control.items-2 .display-preview {
    max-width: 90%;
}
.mock-assets-control.items-3 .display-preview {
    width: 100%;
}
.mock-assets-control .device-item {
    text-align: center;
    cursor: pointer;
    background: rgba(var(--panel-dim), 1);
    border-radius: 10px;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    width: 100%;
    height: 88px;
    padding: 12px;
    display: flex;
    position: relative;
}
.mock-assets-control .device-item.drop-active .drop-indicator {
    display: grid !important;
}
.mock-assets-control .device-item.drop-active .display-preview {
    pointer-events: none;
}
.mock-assets-control .device-item .drop-indicator {
    z-index: 1;
    background: rgba(var(--panel-dim), 1);
    border-radius: inherit;
    border: dashed 1.5px rgba(var(--primary), 1);
    pointer-events: none;
    place-items: center;
    display: none;
    position: absolute;
    inset: 0;
}
.mock-assets-control .device-item .drop-indicator svg {
    width: 24px;
    height: 24px;
}
.mock-assets-control .device-item .display-preview {
    width: 100%;
    height: 100%;
}
.mock-assets-control .device-item .display-preview .safe-area {
    width: 100%;
    height: 100%;
    position: relative;
}
.mock-assets-control .device-item .display-preview .safe-area .device-screen {
    max-height: 100%;
    margin: auto;
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.mock-assets-control .device-item .display-preview .safe-area .device-screen img,
.mock-assets-control .device-item .display-preview .safe-area .device-screen video {
    object-fit: cover;
    object-position: top center;
    width: 100%;
    height: 100%;
}
.mock-assets-control .device-item .display-preview .safe-area .device-screen .empty-state,
.mock-assets-control .device-item .display-preview .safe-area .device-screen .overlay-plus {
    place-items: center;
    display: grid;
    position: absolute;
    inset: 0;
}
.mock-assets-control .device-item .display-preview .safe-area .device-screen .empty-state svg,
.mock-assets-control .device-item .display-preview .safe-area .device-screen .overlay-plus svg {
    width: 18px;
}
.mock-assets-control .device-item .display-preview .safe-area .device-screen .empty-state {
    background: rgba(var(--panel-active), 1);
}
.mock-assets-control .device-item .display-preview .safe-area .device-screen .overlay-plus {
    z-index: 1;
    opacity: 0;
    background: #6464644d;
}
.mock-assets-control .device-item:hover .overlay-plus {
    opacity: 1 !important;
}
.mock-assets-control .device-item:hover > span {
    color: rgba(var(--primary), 1) !important;
}
.mock-controls-transform-hint {
    aspect-ratio: 2;
    border-radius: 12px;
    width: 100%;
    position: relative;
    overflow: hidden;
}
.mock-controls-transform-hint .copy {
    background:
        linear-gradient(70deg, rgba(var(--panel-dim), 1), transparent 50%),
        linear-gradient(70deg, rgba(var(--panel-dim), 1), transparent 50%);
    color: rgba(var(--primary), 0.36);
    align-items: flex-end;
    padding: 12px;
    display: flex;
    position: absolute;
    inset: 0;
}
.mock-controls-transform-hint video,
.mock-controls-transform-hint img {
    object-fit: cover;
    object-position: bottom center;
    width: 100%;
    height: 100%;
}
.mockup-layout-popover .bottom-head {
    inset: 0;
    top: unset;
    z-index: 1;
    background: linear-gradient(180deg, rgba(var(--panel), 0) 0%, rgba(var(--panel), 1) 70%);
    padding: 15px 5px 5px;
    position: absolute;
}
.mock-browser-window-button .window-preview {
    width: 90%;
    height: 60%;
    position: relative;
}
.mock-browser-window-button .window-preview > div {
    background: rgba(var(--primary), 0.36);
    border-radius: 3px;
    max-height: 100%;
    margin: auto;
    position: absolute;
    inset: 20%;
}
@media only screen and (width>=0) and (width<=800px) {
    .mock-browser-window-button {
        width: 60px !important;
    }
}
.mock-browser-address-bar {
    align-items: center;
    gap: 8px;
    display: flex;
}
.mock-browser-address-bar .input-text {
    width: 100%;
}
.mock-browser-icon-button .preview-image {
    z-index: 0;
    position: absolute;
    inset: 0;
}
.mock-browser-icon-button .preview-image:before {
    content: '';
    background: rgba(var(--panel-dim), 0.5);
    position: absolute;
    inset: 0;
}
.mock-browser-icon-button .preview-image img {
    object-fit: contain;
    width: 100%;
    height: 100%;
}
.mock-browser-icon-button svg {
    z-index: 1;
}
.drag-pad-wrapper {
    background: rgba(var(--panel-dim), 1);
    border-radius: 14px;
    position: relative;
    overflow: hidden;
}
.drag-pad-wrapper.zoom-pad .drag-pad .drag-handle:after {
    outline-offset: -0.5px;
    outline: 0.5px solid #0003;
    background: #ffffffb3 !important;
}
.drag-pad-wrapper.zoom-pad .viewfinder-div,
.drag-pad-wrapper.zoom-pad .ghost-viewfinder {
    pointer-events: none;
    border-radius: 14px;
}
.drag-pad-wrapper.zoom-pad .default-viewfinder {
    -webkit-backdrop-filter: brightness(200%);
    backdrop-filter: brightness(200%);
    background: #64646433;
}
.drag-pad-wrapper.zoom-pad .ghost-viewfinder {
    filter: invert(100);
    border: 1.5px solid #646464b3;
}
.drag-pad-wrapper.zoom-pad .pad-preview {
    z-index: 0;
    filter: brightness(40%);
    position: absolute;
    inset: 0;
}
.drag-pad-wrapper.zoom-pad .pad-preview .layout-item {
    background: rgba(var(--panel-dim), 1);
}
.drag-pad-wrapper.zoom-pad .pad-preview .layout-item .mock-style-3d-tag,
.drag-pad-wrapper.zoom-pad .pad-preview .layout-item .frame-effect-layer,
.drag-pad-wrapper.zoom-pad .pad-preview .layout-item .lens-scene-layer,
.drag-pad-wrapper.zoom-pad .pad-preview .layout-item .lens-scene-backdrop-layer {
    display: none !important;
}
.drag-pad-wrapper .drag-hint {
    opacity: 0;
    color: rgba(var(--primary), 0.3);
    text-align: center;
    pointer-events: none;
    z-index: 10;
    letter-spacing: -0.2px;
    font:
        11px/14px Inter,
        sans-serif;
    position: absolute;
    bottom: 10px;
    left: 8px;
    right: 8px;
}
@media only screen and (width>=0) and (width<=800px) {
    .drag-pad-wrapper .drag-hint {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.drag-pad-wrapper:hover .drag-hint {
    opacity: 1;
}
.drag-pad {
    position: relative;
}
.drag-pad .cursor-indicator {
    background: #ffffff80;
    border-radius: 50%;
    width: 10px;
    height: 10px;
}
.drag-pad .drag-handle {
    cursor: grab;
    z-index: 2;
    position: absolute;
}
.drag-pad .drag-handle:after {
    content: '';
    aspect-ratio: 1;
    background: rgba(var(--panel-active), 1);
    border-radius: 50%;
    margin: auto;
    position: absolute;
    inset: 0;
    transform: scale(1.1);
}
.drag-pad .drag-handle:hover:after {
    transform: scale(1.1);
}
.drag-pad .drag-handle:active {
    transition: none !important;
}
.drag-pad .drag-handle:active:after {
    transform: scale(1.2);
}
.drag-pad .drag-handle.active-handle:after {
    outline: solid 1.5px rgba(var(--info), 1);
    outline-offset: -1.5px;
}
.drag-pad .axis-lock {
    background: rgba(var(--primary), 0.12);
    z-index: 1;
    position: absolute;
}
.drag-pad .axis-lock.x-axis-lock,
.drag-pad .axis-lock.y-axis-lock {
    opacity: 1;
}
.drag-pad.drag-disable {
    pointer-events: none;
    opacity: 0.5;
}
.drag-grid-tile {
    border-radius: inherit;
    height: 100%;
    display: flex;
    position: relative;
    overflow: hidden;
}
.drag-grid-tile .preset-col {
    flex-direction: column;
    flex: 1;
    display: flex;
}
.drag-grid-tile .preset-button {
    cursor: pointer;
    flex: 1;
}
.drag-grid-tile .preset-button .tile {
    background: rgba(var(--primary), 0.12);
    width: 100%;
    height: 100%;
    transform: scale(0.1);
    border-radius: 50% !important;
}
@media only screen and (width>=1200px) {
    .drag-grid-tile .preset-button:hover .tile {
        transform: scale(0.8);
    }
}
.mockup-details {
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: 10px;
    flex-direction: column;
    gap: 6px;
    padding: 12px;
    display: flex;
}
.mockup-details .row {
    justify-content: space-between;
    align-items: center;
    display: flex;
}
.mockup-details .row span {
    color: rgba(var(--primary), 0.36);
}
.mockup-details .row p {
    color: rgba(var(--primary), 0.6);
}
@media only screen and (width>=0) and (width<=800px) {
    .mockup-details {
        border-radius: 12px;
        gap: 8px;
        padding: 16px;
    }
}
.rotate-pad,
.rotate-pad .pad {
    background: rgba(var(--panel-dim), 1);
    border-radius: 50%;
}
.rotate-pad .tick-item {
    background: rgba(var(--primary), 0.36);
}
.rotate-pad .handle {
    background: rgba(var(--primary), 0.6);
}
.rotate-pad .handle-circle {
    background: rgba(var(--panel-dim), 1);
    outline: solid 2px rgba(var(--panel-active), 1);
}
.tilt-pad {
    background: rgba(var(--panel-dim), 1);
}
.tilt-pad .knob {
    background: rgba(var(--primary), 0.6);
}
.frame-layout-control .layout-item {
    width: 108px;
}
.frame-shapes-scene-picker-button .preview {
    height: 40px;
    position: relative;
    aspect-ratio: unset !important;
}
.frame-shapes-scene-picker-button .preview:before {
    content: '';
    z-index: 0;
    background:
        linear-gradient(90deg, transparent 0%, rgba(var(--panel-dim), 1) 70%),
        linear-gradient(90deg, transparent 0%, rgba(var(--panel-dim), 1) 70%);
    width: 40px;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
}
.frame-shapes-scene-picker-button .preview svg {
    width: 14px;
    height: 14px;
    color: rgba(var(--primary), 1);
    z-index: 1;
    position: absolute;
    right: 12px;
}
.frame-shapes-scene-picker-button .rich-preview {
    align-items: center;
    gap: 6px;
    max-width: 100%;
    padding-left: 8px;
    padding-right: 15px;
    display: flex;
    overflow: hidden;
}
.frame-shapes-scene-picker-button .rich-preview img {
    width: 28px;
}
.frame-shapes-scene-popover .active-shape {
    padding: 0 10px;
}
.frame-shapes-scene-popover .active-shape .list {
    border-bottom: solid 1px rgba(var(--primary), 0.06);
    align-items: center;
    gap: 6px;
    padding: 10px 0;
    display: flex;
}
.frame-shapes-scene-popover .active-shape .list button {
    flex: 1;
}
.frame-shapes-scene-popover .active-shape .list button img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
@media only screen and (width>=0) and (width<=800px) {
    .frame-shapes-scene-popover .active-shape .list {
        border: none;
        width: max-content;
        margin: 0 auto;
    }
}
.frame-shapes-scene-layout-preview {
    pointer-events: none;
    z-index: 1;
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    container: scene-layout-preview-container/size;
}
.frame-shapes-scene-layout-preview .display {
    place-items: center;
    width: 100%;
    height: 100%;
    display: grid;
}
@container scene-layout-preview-container (width>0) {
    .frame-shapes-scene-layout-preview .display {
        font-size: calc(0.666667cqw + 0.666667cqh);
    }
}
.frame-shapes-scene-layout-preview .display .preview-wrapper {
    aspect-ratio: 5/4;
    height: 75%;
    position: relative;
    transform: scale(0.8);
}
.frame-shapes-scene-layout-preview .display .preview-wrapper .item-card {
    background: linear-gradient(135deg, rgba(var(--panel-active), 1), rgba(var(--panel), 1));
    outline: solid 1em rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: 7em;
    position: absolute;
    inset: 0;
}
.frame-shapes-scene-layout-preview .display .preview-wrapper .shape-scene-object {
    width: 30em;
    height: auto;
}
.custom-back-btn .icon {
    width: 24px !important;
    height: 24px !important;
    color: rgba(var(--primary), 1) !important;
}
.custom-back-btn .color-picker-preview {
    outline: solid 1px rgba(var(--primary), 0.12);
    outline-offset: -1px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
}
.custom-back-btn .image-picker-preview {
    outline: solid 1px rgba(var(--primary), 0.12);
    outline-offset: -1px;
    border-radius: 6px;
    width: 60%;
    height: 60%;
    overflow: hidden;
}
.custom-back-btn .image-picker-preview img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
@media only screen and (width>=0) and (width<=800px) {
    .custom-back-btn > .preview {
        aspect-ratio: 4/3 !important;
    }
}
.custom-color-recent {
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    display: grid;
}
.custom-color-recent li {
    aspect-ratio: 1;
    cursor: pointer;
    border-radius: 50%;
    width: 100%;
}
.custom-image-recent {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    display: grid;
}
.custom-image-recent li {
    aspect-ratio: 3/2;
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
}
.custom-image-recent li img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.magic-backs {
    gap: 12px;
}
.magic-backs .title {
    z-index: 1;
    align-items: center;
    gap: 4px;
    display: flex;
}
.magic-backs .title svg {
    width: 16px;
    height: 16px;
}
.magic-backs-empty-state {
    z-index: 0;
    position: relative;
}
.magic-backs-empty-state .empty-state {
    margin-top: -28px;
    display: flex;
}
.magic-backs-empty-state .empty-state .footnote {
    color: rgba(var(--primary), 0.6);
    flex: 1;
    margin-top: 26px;
    font-size: 10px;
}
.magic-backs-empty-state .empty-state .previews {
    flex-direction: column;
    padding: 0 0 10px 8px;
    display: flex;
}
.magic-backs-empty-state .empty-state .previews .preview-image {
    aspect-ratio: 4/3;
    background: rgba(var(--panel-dim), 1);
    object-fit: cover;
    outline: solid 1px rgba(var(--background), 0.8);
    outline-offset: -1px;
    border-radius: 8px;
    width: 56px;
    box-shadow: 0 2px 4px #0009;
}
.magic-backs-empty-state .empty-state .previews .preview-image:first-child {
    margin-top: -4px;
    transform: rotate(-5deg) translate(-5px);
}
.magic-backs-empty-state .empty-state .previews .preview-image:nth-child(2) {
    margin-top: -18px;
    transform: rotate(5deg);
}
@media only screen and (width>=1200px) {
    .magic-backs-empty-state .empty-state .previews:hover .preview-image:first-child {
        transform: rotate(-7deg) translate(-20px, -5px) scale(1.2);
    }
    .magic-backs-empty-state .empty-state .previews:hover .preview-image:nth-child(2) {
        transform: rotate(7deg) translate(5px, 5px) scale(1.2);
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .magic-backs-empty-state .empty-state {
        flex-direction: column-reverse;
        align-items: center;
        gap: 16px;
        margin-top: 0;
        padding-bottom: 8px;
    }
    .magic-backs-empty-state .empty-state .footnote {
        letter-spacing: -0.4px;
        text-align: center;
        margin-top: 0;
        font:
            500 14px/20px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) and (width>=0) and (width<=800px) {
    .magic-backs-empty-state .empty-state .footnote {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .magic-backs-empty-state .empty-state .previews {
        flex-direction: row;
        align-items: center;
        padding: 0;
        display: flex;
    }
    .magic-backs-empty-state .empty-state .previews .preview-image {
        border-radius: 12px;
        width: 80px;
    }
    .magic-backs-empty-state .empty-state .previews .preview-image:first-child {
        transform: rotate(-7deg) translate(10px, -10px);
    }
    .magic-backs-empty-state .empty-state .previews .preview-image:nth-child(2) {
        transform: rotate(7deg) translate(-10px, 10px);
    }
}
.magic-backs-empty-state .loading-state {
    text-align: center;
    flex-direction: column;
    gap: 4px;
    display: flex;
}
.magic-backs-empty-state .loading-state .h6 {
    text-wrap: balance;
}
.magic-backs-empty-state .loading-state .graphic {
    justify-content: center;
    align-items: center;
    gap: 16px;
    height: 80px;
    display: flex;
}
.magic-backs-empty-state .loading-state .graphic .loading-item {
    position: relative;
}
.magic-backs-empty-state .loading-state .graphic .loading-item img {
    aspect-ratio: 1;
    object-fit: cover;
    background: rgba(var(--panel), 1);
    border-radius: 8px;
    width: 56px;
    box-shadow: 0 2px 4px #0009;
}
.magic-backs-empty-state .loading-state .graphic .loading-item .pallet {
    border-radius: 4px;
    width: 56px;
    height: 16px;
    display: flex;
    position: absolute;
    bottom: -4px;
    left: 8px;
    overflow: hidden;
    transform: rotate(-8deg);
    box-shadow: 0 2px 4px #0009;
}
.magic-backs-empty-state .loading-state .graphic .loading-item .pallet div {
    flex: 1;
    height: 100%;
}
.magic-backs-empty-state .loading-state .graphic .loading-item .effect {
    z-index: -1;
    border-radius: 10px;
    position: absolute;
    inset: -2px;
    overflow: hidden;
}
.magic-backs-empty-state .loading-state .graphic .loading-item .effect div {
    background: conic-gradient(
        from 180deg at 50% 50%,
        rgba(var(--panel), 1) 0deg,
        #fffc 180deg,
        rgba(var(--panel), 1) 360deg
    );
    background: conic-gradient(
        from 180deg at 50% 50.28%,
        transparent -133.13deg,
        transparent 131.25deg,
        rgba(var(--primary), 1) 178.12deg,
        transparent 226.87deg,
        transparent 491.25deg
    );
    animation: 1s linear infinite generateAnim;
    position: absolute;
    inset: -40%;
}
@keyframes generateAnim {
    to {
        transform: rotate(360deg);
    }
}
.magic-backs-empty-state .visual {
    z-index: -1;
    grid-template-columns: repeat(4, 1fr);
    gap: 7px;
    margin-top: -24px;
    display: grid;
    position: relative;
}
.magic-backs-empty-state .visual div {
    aspect-ratio: 4/3;
    background: rgba(var(--primary), 0.06);
    border-radius: 8px;
}
.magic-backs-empty-state .visual:before {
    content: '';
    background: linear-gradient(180deg, rgba(var(--panel), 1) 0%, transparent 100%);
    z-index: 1;
    position: absolute;
    inset: 0;
}
@media only screen and (width>=0) and (width<=800px) {
    .magic-backs-empty-state .visual {
        display: none;
        overflow: hidden;
    }
    .magic-backs-empty-state .visual div {
        aspect-ratio: 2/3;
        min-width: 52px;
    }
    .magic-backs-empty-state .visual:before {
        background:
            linear-gradient(-90deg, rgba(var(--background), 1) 0%, transparent 40%),
            linear-gradient(90deg, rgba(var(--background), 1) 0%, transparent 40%);
    }
}
.magic-backs-empty-state.is-empty .visual {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}
.magic-backs-empty-state.is-loading .visual div {
    background: rgba(var(--primary), 0.12);
    border-color: rgba(var(--primary), 0.12);
    opacity: 0;
    animation: 0.8s linear infinite alternate visualAnim;
}
.magic-backs-empty-state.is-loading .visual div:first-child {
    animation-delay: 0.1s;
}
.magic-backs-empty-state.is-loading .visual div:nth-child(2) {
    animation-delay: 0.2s;
}
.magic-backs-empty-state.is-loading .visual div:nth-child(3) {
    animation-delay: 0.3s;
}
.magic-backs-empty-state.is-loading .visual div:nth-child(4) {
    animation-delay: 0.4s;
}
.magic-backs-empty-state.is-loading .visual div:nth-child(5) {
    animation-delay: 0.5s;
}
.magic-backs-empty-state.is-loading .visual div:nth-child(6) {
    animation-delay: 0.6s;
}
.magic-backs-empty-state.is-loading .visual div:nth-child(7) {
    animation-delay: 0.7s;
}
.magic-backs-empty-state.is-loading .visual div:nth-child(8) {
    animation-delay: 0.8s;
}
@keyframes visualAnim {
    to {
        opacity: 1;
    }
}
.unsplash-control-modal .v-stack {
    height: 100%;
    transform: translate(0);
}
.unsplash-control-modal .unsplash-control .search-bar {
    padding: 16px;
}
.unsplash-control-popover .v-stack {
    height: 100%;
    transform: translate(0);
}
.unsplash-control-popover .unsplash-control .search-bar {
    padding: 12px;
}
.unsplash-control {
    flex-direction: column;
    gap: 12px;
    height: 100%;
    display: flex;
    position: relative;
}
.unsplash-control .search-bar {
    z-index: 10;
    background:
        linear-gradient(rgba(var(--panel), 1) 50%, transparent 100%),
        linear-gradient(rgba(var(--panel), 1) 50%, transparent 100%);
    gap: 4px;
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}
.unsplash-control .search-bar .input-box {
    flex: 1;
}
.unsplash-control .initial-state {
    flex-direction: column;
    flex: 1;
    justify-content: center;
    align-items: center;
    gap: 12px;
    display: flex;
}
.unsplash-control .initial-state > svg {
    width: 50%;
    color: rgba(var(--primary), 0.12);
    margin: 32px 0;
}
.unsplash-control .initial-state .suggest-buttons {
    flex-flow: wrap;
    justify-content: center;
    gap: 8px;
    display: flex;
}
.backpack {
    flex-direction: column;
    gap: 12px;
    display: flex;
}
.backpack .title {
    align-items: center;
    gap: 6px;
    margin-top: 8px;
    display: flex;
    position: relative;
}
.backpack .title .expand-btn {
    width: max-content;
    position: absolute;
    right: 0;
}
.backpack .expand-btn {
    width: 100%;
    color: rgba(var(--primary), 0.36);
    border-radius: 6px;
    grid-column-start: 1;
    padding: 9px;
}
.backpack .expand-btn:hover {
    background: rgba(var(--primary), 0.06);
    color: rgba(var(--primary), 0.6);
}
.backpack .expand-btn svg {
    width: 14px;
    height: 14px;
    transition: inherit;
}
.backpack .expand-btn.expanded svg {
    transform: rotate(180deg);
}
.backpack .backpack-colors-list {
    display: grid;
}
.backpack .backpack-colors-list.image-list,
.backpack .backpack-colors-list .gradient-list {
    grid-template-columns: repeat(3, 1fr);
    gap: 7px;
}
.backpack .backpack-colors-list.solid-list {
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
}
.backpack .backpack-colors-list.magic-list {
    flex-wrap: wrap;
    gap: 7px;
    display: flex;
}
.backpack .backpack-colors-list.magic-list > * {
    flex-basis: calc(25% - 5.25px);
    order: 4;
}
.backpack .backpack-colors-list.magic-list > :first-child {
    order: 0;
}
.backpack .backpack-colors-list.magic-list > :nth-child(2) {
    order: 1;
}
.backpack .backpack-colors-list.magic-list > :nth-child(3) {
    order: 2;
}
.backpack .backpack-colors-list.magic-list .expand-btn {
    aspect-ratio: 4/3;
    border-radius: 8px;
    order: 3 !important;
}
.backpack .backpack-colors-list.magic-list .gradient-item,
.backpack .backpack-colors-list.magic-list .mesh-item,
.backpack .backpack-colors-list.magic-list .magic-image-item,
.backpack .backpack-colors-list.magic-list .magic-unsplash-item,
.backpack .backpack-colors-list.magic-list .solid-item {
    aspect-ratio: 4/3;
}
.backpack .backpack-colors-list.magic-list .gradient-item .display,
.backpack .backpack-colors-list.magic-list .mesh-item .display,
.backpack .backpack-colors-list.magic-list .magic-image-item .display,
.backpack .backpack-colors-list.magic-list .magic-unsplash-item .display,
.backpack .backpack-colors-list.magic-list .solid-item .display {
    border-radius: 8px;
}
.backpack .backpack-colors-list.magic-list .magic-image-item .display {
    width: 100%;
    height: 100%;
    position: relative;
}
.mesh-display {
    border-radius: inherit;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.mesh-display:after {
    content: '';
    z-index: 1;
    -webkit-backdrop-filter: blur(6em);
    backdrop-filter: blur(6em);
    will-change: backdrop-filter;
    border-radius: inherit;
    position: absolute;
    inset: 0;
    overflow: hidden;
    transform: translateZ(0);
}
.mesh-display > div {
    width: 100%;
    height: 100%;
}
.image-item,
.gradient-item,
.magic-image-item,
.magic-unsplash-item,
.mesh-item,
.text-color-item,
.solid-item {
    width: 100%;
    position: relative;
    overflow: visible;
}
.image-item:hover,
.gradient-item:hover,
.magic-image-item:hover,
.magic-unsplash-item:hover,
.mesh-item:hover,
.text-color-item:hover,
.solid-item:hover {
    background: 0 0;
}
.image-item:hover .link-label,
.gradient-item:hover .link-label,
.magic-image-item:hover .link-label,
.magic-unsplash-item:hover .link-label,
.mesh-item:hover .link-label,
.text-color-item:hover .link-label,
.solid-item:hover .link-label {
    visibility: visible;
    opacity: 1;
}
.image-item .link-label,
.gradient-item .link-label,
.magic-image-item .link-label,
.magic-unsplash-item .link-label,
.mesh-item .link-label,
.text-color-item .link-label,
.solid-item .link-label {
    inset: 3px;
    top: unset;
    z-index: 1;
    text-align: left;
    background: rgba(var(--background), 0.8);
    color: rgba(var(--primary), 1);
    visibility: hidden;
    opacity: 0;
    border-radius: 4px;
    padding: 2px 4px;
    font-size: 8px;
    position: absolute;
}
.image-item .display,
.gradient-item .display,
.magic-image-item .display,
.magic-unsplash-item .display,
.mesh-item .display,
.text-color-item .display,
.solid-item .display {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}
.image-item .display > div,
.gradient-item .display > div,
.magic-image-item .display > div,
.magic-unsplash-item .display > div,
.mesh-item .display > div,
.text-color-item .display > div,
.solid-item .display > div {
    width: 100%;
    height: 100%;
}
.image-item .display:after,
.gradient-item .display:after,
.magic-image-item .display:after,
.magic-unsplash-item .display:after,
.mesh-item .display:after,
.text-color-item .display:after,
.solid-item .display:after {
    content: '';
    box-shadow: inset 0px 0px 0px 1px rgba(var(--primary), 0.12);
    border-radius: inherit;
    z-index: 3;
    position: absolute;
    inset: 0;
}
.image-item.image-item,
.image-item.magic-unsplash-item,
.gradient-item.image-item,
.gradient-item.magic-unsplash-item,
.magic-image-item.image-item,
.magic-image-item.magic-unsplash-item,
.magic-unsplash-item.image-item,
.magic-unsplash-item.magic-unsplash-item,
.mesh-item.image-item,
.mesh-item.magic-unsplash-item,
.text-color-item.image-item,
.text-color-item.magic-unsplash-item,
.solid-item.image-item,
.solid-item.magic-unsplash-item {
    aspect-ratio: 4/3;
}
.image-item.image-item .display,
.image-item.magic-unsplash-item .display,
.gradient-item.image-item .display,
.gradient-item.magic-unsplash-item .display,
.magic-image-item.image-item .display,
.magic-image-item.magic-unsplash-item .display,
.magic-unsplash-item.image-item .display,
.magic-unsplash-item.magic-unsplash-item .display,
.mesh-item.image-item .display,
.mesh-item.magic-unsplash-item .display,
.text-color-item.image-item .display,
.text-color-item.magic-unsplash-item .display,
.solid-item.image-item .display,
.solid-item.magic-unsplash-item .display {
    border-radius: 9px;
}
.image-item.image-item .display img,
.image-item.magic-unsplash-item .display img,
.gradient-item.image-item .display img,
.gradient-item.magic-unsplash-item .display img,
.magic-image-item.image-item .display img,
.magic-image-item.magic-unsplash-item .display img,
.magic-unsplash-item.image-item .display img,
.magic-unsplash-item.magic-unsplash-item .display img,
.mesh-item.image-item .display img,
.mesh-item.magic-unsplash-item .display img,
.text-color-item.image-item .display img,
.text-color-item.magic-unsplash-item .display img,
.solid-item.image-item .display img,
.solid-item.magic-unsplash-item .display img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.image-item.gradient-item,
.gradient-item.gradient-item,
.magic-image-item.gradient-item,
.magic-unsplash-item.gradient-item,
.mesh-item.gradient-item,
.text-color-item.gradient-item,
.solid-item.gradient-item {
    aspect-ratio: 18/9;
}
.image-item.gradient-item .display,
.gradient-item.gradient-item .display,
.magic-image-item.gradient-item .display,
.magic-unsplash-item.gradient-item .display,
.mesh-item.gradient-item .display,
.text-color-item.gradient-item .display,
.solid-item.gradient-item .display {
    border-radius: 9px;
}
.image-item.solid-item,
.gradient-item.solid-item,
.magic-image-item.solid-item,
.magic-unsplash-item.solid-item,
.mesh-item.solid-item,
.text-color-item.solid-item,
.solid-item.solid-item {
    aspect-ratio: 1;
}
.image-item.solid-item .display,
.gradient-item.solid-item .display,
.magic-image-item.solid-item .display,
.magic-unsplash-item.solid-item .display,
.mesh-item.solid-item .display,
.text-color-item.solid-item .display,
.solid-item.solid-item .display {
    border-radius: 40px;
}
.image-item.mesh-item .shuffle-button,
.gradient-item.mesh-item .shuffle-button,
.magic-image-item.mesh-item .shuffle-button,
.magic-unsplash-item.mesh-item .shuffle-button,
.mesh-item.mesh-item .shuffle-button,
.text-color-item.mesh-item .shuffle-button,
.solid-item.mesh-item .shuffle-button {
    z-index: 10;
    background: rgba(var(--secondary), 0.3);
    width: 100%;
    position: absolute;
    inset: 0;
}
.image-item.mesh-item .display,
.gradient-item.mesh-item .display,
.magic-image-item.mesh-item .display,
.magic-unsplash-item.mesh-item .display,
.mesh-item.mesh-item .display,
.text-color-item.mesh-item .display,
.solid-item.mesh-item .display {
    cursor: pointer;
    position: relative;
}
.magic-unsplash-display {
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.magic-unsplash-display img {
    object-fit: cover;
    width: 100%;
    height: 100%;
    transform: scale(1.1);
}
.magic-unsplash-display:after {
    content: '';
    background: inherit;
    z-index: 1;
    -webkit-backdrop-filter: blur(10em);
    backdrop-filter: blur(10em);
    will-change: backdrop-filter;
    position: absolute;
    inset: 0;
    overflow: hidden;
    transform: translateZ(0);
}
.magic-image-display {
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.magic-image-display img {
    object-fit: cover;
    filter: blur(10em);
    width: 100%;
    height: 100%;
    transform: scale(1.2);
}
.magic-image-display:after {
    content: '';
    background: inherit;
    z-index: 1;
    opacity: 0.5;
    position: absolute;
    inset: 0;
}
.magic-image-display.style-1:after {
    display: none;
}
.magic-image-display.style-2:after {
    mix-blend-mode: overlay;
    opacity: 0.5;
}
.magic-pallet-customize {
    flex-direction: column;
    gap: 6px;
    margin-bottom: 8px;
    display: flex;
    position: relative;
}
.magic-pallet-customize > span {
    text-align: center;
    color: rgba(var(--primary), 0.36);
    letter-spacing: -0.2px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .magic-pallet-customize > span {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.magic-pallet-customize .pallet-colors {
    background: rgba(var(--panel-dim), 1);
    border-radius: 8px;
    gap: 3px;
    padding: 3px;
    display: flex;
}
.magic-pallet-customize .pallet-colors .pallet-color-item {
    cursor: pointer;
    border-radius: 5px;
    flex: 1;
    height: 28px;
    position: relative;
    overflow: hidden;
}
@media only screen and (width>=1200px) {
    .magic-pallet-customize .pallet-colors .pallet-color-item:hover .icon-overlay {
        background: #0000004d;
    }
    .magic-pallet-customize .pallet-colors .pallet-color-item:hover .icon-overlay svg {
        opacity: 1;
    }
}
.magic-pallet-customize .pallet-colors .pallet-color-item:active .icon-overlay svg {
    transition: inherit;
    transform: scale(0.7);
}
.magic-pallet-customize .pallet-colors .pallet-color-item .color-layer,
.magic-pallet-customize .pallet-colors .pallet-color-item .icon-overlay,
.magic-pallet-customize .pallet-colors .pallet-color-item:after {
    transition: inherit;
    position: absolute;
    inset: 0;
}
.magic-pallet-customize .pallet-colors .pallet-color-item .color-layer {
    z-index: 0;
}
.magic-pallet-customize .pallet-colors .pallet-color-item .icon-overlay {
    z-index: 1;
    place-items: center;
    display: grid;
}
.magic-pallet-customize .pallet-colors .pallet-color-item .icon-overlay svg {
    color: #ffffffb3;
    opacity: 0;
    width: 15px;
    height: 15px;
}
.magic-pallet-customize .pallet-colors .pallet-color-item:after {
    content: '';
    box-shadow: inset 0px 0px 0px 1px rgba(var(--primary), 0.12);
    border-radius: inherit;
    z-index: 3;
}
.magic-pallet-customize .pallet-colors .pallet-color-item.pallet-color-disabled {
    transform: scaleY(0.8) scaleX(0.9);
}
.magic-pallet-customize .pallet-colors .pallet-color-item.pallet-color-disabled .color-layer {
    opacity: 0.4;
}
.magic-pallet-customize .pallet-colors .pallet-color-item.pallet-color-disabled .icon-overlay {
    background: rgba(var(--primary), 0.06);
}
.magic-pallet-customize .pallet-colors .pallet-color-item.pallet-color-disabled .icon-overlay svg {
    transform: rotate(-135deg);
}
.magic-pallet-customize
    .pallet-colors
    .pallet-color-item.pallet-color-disabled:active
    .icon-overlay
    svg {
    transition: inherit;
    transform: scale(0.7) rotate(-135deg);
}
@media only screen and (width>=0) and (width<=800px) {
    .magic-pallet-customize {
        border-bottom: solid 1px rgba(var(--primary), 0.06);
        padding-bottom: 10px;
    }
    .magic-pallet-customize .pallet-colors {
        border-radius: 14px;
        gap: 4px;
        padding: 4px;
    }
    .magic-pallet-customize .pallet-colors .pallet-color-item {
        border-radius: 5px;
        height: 32px;
    }
    .magic-pallet-customize .pallet-colors .pallet-color-item:first-child {
        border-radius: 10px 5px 5px 10px;
    }
    .magic-pallet-customize .pallet-colors .pallet-color-item:last-child {
        border-radius: 5px 10px 10px 5px;
    }
}
.magic-media-control {
    gap: 8px;
    margin-bottom: 8px;
    display: flex;
}
.magic-media-control > button {
    margin-top: -14px;
}
.magic-media-picker {
    flex-wrap: wrap;
    flex: 1;
    gap: 4px;
    display: flex;
}
@media only screen and (width>=0) and (width<=800px) {
    .magic-media-picker {
        justify-content: center;
        padding-right: 0;
    }
}
.magic-media-picker .magic-media-item {
    transform-origin: 0;
    cursor: pointer;
    border-radius: 3px;
    min-width: max-content;
    padding: 4px;
    position: relative;
}
@media only screen and (width>=1200px) {
    .magic-media-picker .magic-media-item:hover {
        z-index: 5;
        transform: scale(2.2);
    }
    .magic-media-picker .magic-media-item:hover .colors-wrapper {
        outline-offset: 1.5px;
        box-shadow: 0 0 12px 6px rgba(var(--panel-dim), 1);
        outline-width: 1px !important;
    }
    .magic-media-picker .magic-media-item:hover .src-image {
        visibility: visible;
        opacity: 1;
    }
}
.magic-media-picker .magic-media-item .video-wrapper {
    border-radius: inherit;
    width: 40px;
    height: 10px;
    transition: inherit;
    display: flex;
    position: relative;
    overflow: hidden;
}
.magic-media-picker .magic-media-item .video-wrapper:after {
    content: '';
    border-radius: inherit;
    -webkit-backdrop-filter: blur(4px) saturate(200%);
    backdrop-filter: blur(4px) saturate(200%);
    box-shadow: inset 0px 0px 0px 1px rgba(var(--primary), 0.12);
    position: absolute;
    inset: 0;
}
.magic-media-picker .magic-media-item .colors-wrapper {
    border-radius: inherit;
    transition: inherit;
    display: flex;
    position: relative;
    overflow: hidden;
}
.magic-media-picker .magic-media-item .colors-wrapper:after {
    content: '';
    border-radius: inherit;
    box-shadow: inset 0px 0px 0px 1px rgba(var(--primary), 0.12);
    position: absolute;
    inset: 0;
}
.magic-media-picker .magic-media-item .colors-wrapper .pallet-color {
    aspect-ratio: 4/5;
    min-width: 8px;
}
.magic-media-picker .magic-media-item .src-image {
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
    z-index: 1;
    border-radius: 3px;
    width: 28px;
    margin: 0 auto;
    transition: inherit;
    position: absolute;
    bottom: 110%;
    left: 0;
    right: 0;
    overflow: hidden;
}
.magic-media-picker .magic-media-item .src-image img,
.magic-media-picker .magic-media-item .src-image video {
    width: 100%;
}
.backpack-new {
    flex-direction: column;
    gap: 6px;
    display: flex;
}
.backpack-new .title {
    align-items: center;
    gap: 6px;
    display: flex;
    position: relative;
}
.backpack-new .backpack-colors-list {
    gap: 7px;
    display: grid;
}
.backpack-new .backpack-colors-list > * {
    order: 5;
}
.backpack-new .backpack-colors-list > :first-child {
    order: 0;
}
.backpack-new .backpack-colors-list > :nth-child(2) {
    order: 1;
}
.backpack-new .backpack-colors-list > :nth-child(3) {
    order: 2;
}
.backpack-new .backpack-colors-list.image-list,
.backpack-new .backpack-colors-list.solid-list,
.backpack-new .backpack-colors-list.gradient-list {
    grid-template-columns: repeat(4, 1fr) !important;
}
.backpack-new .backpack-colors-list.image-list > *,
.backpack-new .backpack-colors-list.solid-list > *,
.backpack-new .backpack-colors-list.gradient-list > * {
    aspect-ratio: 1.25 !important;
}
.backpack-new .backpack-colors-list.image-list .display,
.backpack-new .backpack-colors-list.solid-list .display,
.backpack-new .backpack-colors-list.gradient-list .display {
    border-radius: 10px;
}
.backpack-new .backpack-colors-list.magic-list {
    grid-template-columns: repeat(4, 1fr) !important;
}
.backpack-new .backpack-colors-list.magic-list > * {
    aspect-ratio: 1.25 !important;
}
.backpack-new .backpack-colors-list.magic-list .display {
    border-radius: 10px;
}
.backpack-new .backpack-colors-list.magic-list .magic-image-item .display {
    width: 100%;
    height: 100%;
    position: relative;
}
.backpack-new .backpack-colors-list .expand-button {
    aspect-ratio: 1.25;
    cursor: pointer;
    border-radius: 10px;
    order: 3;
    place-items: center;
    transition: all 0.3s;
    display: grid;
    position: relative;
}
.backpack-new .backpack-colors-list .expand-button svg {
    color: #ffffffb3;
    z-index: 2;
    width: 14px;
    height: 14px;
    transition: inherit;
}
.backpack-new .backpack-colors-list .expand-button .items-preview {
    transition: inherit;
    position: absolute;
    inset: 0;
}
.backpack-new .backpack-colors-list .expand-button .background-item {
    aspect-ratio: inherit;
    pointer-events: none;
    transition: inherit;
    position: absolute;
    inset: 0;
}
.backpack-new .backpack-colors-list .expand-button .background-item .plus-badge-wrapper {
    display: none !important;
}
.backpack-new .backpack-colors-list .expand-button .background-item .display {
    outline: none !important;
}
.backpack-new .backpack-colors-list .expand-button .background-item .display:before {
    content: '';
    border-radius: inherit;
    z-index: 1;
    background: #14141466;
    position: absolute;
    inset: 0;
}
.backpack-new .backpack-colors-list .expand-button .background-item:first-child {
    transform: scale(0.85) rotate(5deg);
}
.backpack-new .backpack-colors-list .expand-button .background-item:nth-child(2) {
    z-index: -1;
    opacity: 0.4;
    transform: scale(0.9) rotate(-8deg);
}
.backpack-new .backpack-colors-list .expand-button:hover .background-item:first-child {
    transform: scale(0.9) rotate(10deg);
}
.backpack-new .backpack-colors-list .expand-button:hover .background-item:nth-child(2) {
    z-index: -1;
    opacity: 0.4;
    transform: scale(0.95) rotate(-14deg);
}
.backpack-new .backpack-colors-list .expand-button.is-expanded {
    background: rgba(var(--panel-dim), 1);
}
.backpack-new .backpack-colors-list .expand-button.is-expanded svg {
    transform: rotate(180deg);
}
.backpack-new .backpack-colors-list .expand-button.is-expanded .items-preview {
    opacity: 0;
    transform: scale(0.7);
}
.welcome-container {
    z-index: 999999;
    -webkit-backdrop-filter: blur(3px);
    backdrop-filter: blur(3px);
    background: #00000080;
    justify-content: center;
    align-items: center;
    display: flex;
    position: fixed;
    inset: 0;
}
.welcome-modal {
    background: rgba(var(--panel), 1);
    border-radius: 24px;
    width: calc(100vw - 16px);
    max-width: 1000px;
    height: 100%;
    max-height: calc(100vh - 16px);
    position: relative;
    overflow: hidden;
    box-shadow: 0 16px 32px #0000003d;
}
.welcome-modal:after {
    content: '';
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    pointer-events: none;
    border-radius: inherit;
    z-index: 10;
    position: absolute;
    inset: 0;
}
.welcome-modal .v-stack {
    width: 100%;
    height: 100%;
}
.welcome-modal section {
    flex-direction: column;
    gap: 32px;
    width: 100%;
    padding: 32px;
    display: flex;
}
.welcome-modal .section-header {
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-bottom: -8px;
    padding: 64px 32px 0;
    display: flex;
}
.welcome-modal .section-header p {
    max-width: 80%;
}
@media only screen and (width>=0) and (width<=800px) {
    .welcome-modal .section-header {
        margin: 16px 0;
    }
    .welcome-modal .section-header p {
        letter-spacing: -0.6px;
        font:
            500 17px/24px Inter,
            sans-serif;
    }
}
@media only screen and (width>=0) and (width<=800px) and (width>=0) and (width<=800px) {
    .welcome-modal .section-header p {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
.welcome-modal .main-cards .cards {
    flex-flow: wrap;
    gap: 16px;
    display: flex;
}
.welcome-modal .small-cards-grid {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
    display: grid;
}
@media only screen and (width>=0) and (width<=800px) {
    .welcome-modal {
        background: rgba(var(--background), 1);
        box-shadow: none;
        width: 100vw !important;
        max-width: unset !important;
        height: 100% !important;
        max-height: unset !important;
        border-radius: 0 !important;
    }
    .welcome-modal section {
        gap: 32px;
        padding: 16px;
    }
    .welcome-modal .main-cards .cards {
        flex-flow: column;
        gap: 16px;
    }
}
.welcome-modal.is-scrolled .cta-buttons .start-btn-placeholder {
    transform: translateY(0);
}
.welcome-modal .cta-buttons {
    z-index: 10;
    inset: 0;
    top: unset;
    background:
        linear-gradient(transparent 0%, rgba(var(--panel), 1) 90%),
        linear-gradient(transparent 0%, rgba(var(--panel), 1) 90%);
    pointer-events: none;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 20px 20px 10px;
    display: flex;
    position: absolute;
}
.welcome-modal .cta-buttons > * {
    pointer-events: all;
}
.welcome-modal .cta-buttons .credits {
    justify-content: center;
    align-items: center;
    gap: 8px;
    width: 100%;
    display: flex;
}
.welcome-modal .cta-buttons .credits button:not(:hover),
.welcome-modal .cta-buttons .credits > p:not(:hover) {
    color: rgba(var(--primary), 0.36);
}
.welcome-modal .cta-buttons .start-btn-placeholder {
    margin-bottom: 20px;
    transition: all 0.6s;
    position: relative;
}
.welcome-modal .cta-buttons .start-btn {
    text-align: center;
    color: #fff;
    cursor: pointer;
    letter-spacing: -0.6px;
    border-radius: 100px;
    width: 200px;
    padding: 16px;
    font:
        500 17px/24px Inter,
        sans-serif;
    transition: all 0.4s;
    position: relative;
}
@media only screen and (width>=0) and (width<=800px) {
    .welcome-modal .cta-buttons .start-btn {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
.welcome-modal .cta-buttons .start-btn:before {
    content: '';
    z-index: -1;
    border-radius: inherit;
    filter: blur(12px) saturate(200%);
    background: inherit;
    background-position: inherit;
    background-size: inherit;
    animation: inherit;
    opacity: 0.5;
    position: absolute;
    inset: 0;
    transform: translateY(6px);
}
.welcome-modal .cta-buttons .start-btn:hover {
    transform: scale(1.05);
}
.welcome-modal .templates-preview {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding-left: 0;
    padding-right: 0;
    display: flex;
    position: relative;
}
.welcome-modal .templates-preview .row {
    justify-content: center;
    width: 100%;
    height: 220px;
    display: flex;
    overflow: hidden;
}
.welcome-modal .templates-preview .row .list {
    gap: 12px;
    display: flex;
}
.welcome-modal .templates-preview .row .list .preview-image {
    border-radius: 16px;
    height: 100%;
    position: relative;
    overflow: hidden;
}
.welcome-modal .templates-preview .row .list .preview-image > img {
    height: 100%;
}
.welcome-modal .templates-preview .btn-wrapper {
    z-index: 1;
    background:
        linear-gradient(transparent, rgba(var(--panel), 1)),
        linear-gradient(transparent, rgba(var(--panel), 1));
    justify-content: center;
    align-items: flex-end;
    width: 100%;
    height: 220px;
    margin-top: -220px;
    display: flex;
}
.welcome-hero {
    justify-content: flex-end;
    height: calc(100vh - 300px);
    margin-bottom: 32px;
    padding: 0;
    position: relative;
}
.welcome-hero .content {
    text-align: center;
    z-index: 2;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    display: flex;
    position: absolute;
    top: calc(100vh - 460px);
    left: 0;
    right: 0;
}
.welcome-hero .content .logo {
    align-items: center;
    gap: 16px;
    display: flex;
}
.welcome-hero .content .logo img {
    height: 48px;
}
.welcome-hero .content .logo h4 {
    letter-spacing: -0.8px;
    font:
        500 34px/40px Inter,
        sans-serif;
}
.welcome-hero .content .logo .app-stage-tag {
    transform: scale(1.3);
}
.welcome-hero .content p {
    color: rgba(var(--primary), 0.6);
    font:
        400 18px/28px Inter,
        sans-serif;
}
.welcome-hero .content h1 {
    letter-spacing: -1.4px;
    font:
        500 56px/68px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .welcome-hero .content h1 {
        letter-spacing: -0.8px;
        font:
            500 40px/48px Inter,
            sans-serif;
    }
}
.welcome-hero .media {
    position: absolute;
    inset: 0;
}
.welcome-hero .media:after {
    content: '';
    inset: 0;
    top: unset;
    z-index: 1;
    background:
        linear-gradient(transparent, rgba(var(--panel), 1)),
        linear-gradient(transparent, rgba(var(--panel), 1));
    height: 50%;
    position: absolute;
}
.welcome-hero .media video {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.welcome-hero .media .slideshow {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}
.welcome-hero .media .slideshow .slide-image {
    object-fit: cover;
    object-position: top center;
    opacity: 0;
    width: 100%;
    height: 100%;
    animation: 8s infinite fadeSlideshow;
    position: absolute;
}
@keyframes fadeSlideshow {
    0% {
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    25% {
        opacity: 1;
    }
    35% {
        opacity: 0;
    }
    to {
        opacity: 0;
    }
}
.welcome-modal .previews {
    gap: 10px;
    padding: 40px 0;
    position: relative;
    overflow: hidden;
}
.welcome-modal .previews .row {
    width: 100%;
    height: 200px;
    display: flex;
    overflow: hidden;
}
.welcome-modal .previews .row .list {
    gap: 10px;
    animation: 40s linear infinite alternate slide;
    display: flex;
    transform: translate(-50%);
}
@keyframes slide {
    to {
        transform: translate(-100%);
    }
}
.welcome-modal .previews .preview-image {
    border-radius: 10px;
    height: 100%;
    position: relative;
    overflow: hidden;
}
.welcome-modal .previews .preview-image > img {
    height: 100%;
}
.welcome-modal .previews .preview-image span {
    z-index: 20;
    background: #000;
    position: absolute;
    top: 0;
    left: 0;
}
@media only screen and (width>=0) and (width<=800px) {
    .welcome-modal .previews {
        gap: 8px;
    }
    .welcome-modal .previews .row {
        height: 140px;
    }
    .welcome-modal .previews .row .list {
        gap: 8px;
    }
    .welcome-modal .previews .preview-image {
        border-radius: 8px;
    }
}
.welcome-modal .feature-card {
    background: #000;
    border-radius: 24px;
    flex-direction: column;
    height: 600px;
    display: flex;
    position: relative;
    overflow: hidden;
}
.welcome-modal .feature-card .details {
    color: #fff;
    z-index: 1;
    gap: 8px;
    padding: 32px;
}
.welcome-modal .feature-card .details p {
    opacity: 0.6;
    max-width: 400px;
}
.welcome-modal .feature-card.small-size {
    flex-basis: calc(36% - 8px);
}
.welcome-modal .feature-card.large-size {
    flex-basis: calc(64% - 8px);
}
.welcome-modal .feature-card.wide-size {
    height: unset;
    flex-direction: row-reverse;
    flex-basis: 100%;
    height: 520px;
}
.welcome-modal .feature-card.wide-size video {
    float: right;
    border-radius: 10px;
    width: auto;
    height: 90%;
    margin: 5% 5% 0 0;
    box-shadow: 0 5px 20px #0009;
}
.welcome-modal .feature-card.flex-row {
    flex-direction: row;
}
.welcome-modal .feature-card.flex-row .details {
    padding-right: 16px;
}
.welcome-modal .feature-card.flex-row-reverse {
    flex-direction: row-reverse;
}
.welcome-modal .feature-card.flex-row-reverse .details {
    padding-left: 16px;
}
.welcome-modal .feature-card.flex-column {
    flex-direction: column;
}
.welcome-modal .feature-card.flex-column .details {
    padding-bottom: 16px;
}
.welcome-modal .feature-card.flex-column-reverse {
    flex-direction: column-reverse;
}
.welcome-modal .feature-card.flex-column-reverse .details {
    padding-top: 16px;
}
.welcome-modal .feature-card .media-wrapper {
    z-index: 1;
    background-size: 100%;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.welcome-modal .feature-card .media-wrapper img,
.welcome-modal .feature-card .media-wrapper video {
    object-fit: contain;
    width: 100%;
    height: 100%;
}
.welcome-modal .feature-card .background {
    object-fit: cover;
    filter: brightness(90%);
    width: 100%;
    height: 100%;
    position: absolute;
    inset: 0;
}
.welcome-modal .feature-card:nth-child(2) img {
    object-position: bottom right;
}
.welcome-modal .feature-card:nth-child(4) img {
    object-position: top right;
}
@media only screen and (width>=0) and (width<=800px) {
    .welcome-modal .feature-card {
        border-radius: 20px;
        height: max-content;
        flex: unset !important;
    }
    .welcome-modal .feature-card.reverse .details {
        padding: 10px 20px 30px;
    }
    .welcome-modal .feature-card .details {
        padding: 30px 20px 10px;
    }
    .welcome-modal .feature-card:first-child {
        aspect-ratio: 9/10;
    }
    .welcome-modal .feature-card:nth-child(2) {
        aspect-ratio: 2/3;
    }
    .welcome-modal .feature-card:nth-child(2) .image-wrapper {
        margin-top: -40%;
    }
    .welcome-modal .feature-card:nth-child(3) {
        height: 320px;
    }
    .welcome-modal .feature-card:nth-child(3) video {
        border-radius: 10px;
        height: 90%;
        margin: 5% 5% 0 0;
    }
    .welcome-modal .feature-card:nth-child(4) {
        aspect-ratio: 2/3;
    }
    .welcome-modal .feature-card:nth-child(4) .image-wrapper {
        margin-bottom: -40%;
    }
    .welcome-modal .feature-card:nth-child(5) {
        aspect-ratio: 9/10;
    }
}
.use-case-section {
    flex-direction: column;
    display: flex;
    gap: 0 !important;
    padding: 0 !important;
}
.use-case-section .scroll-view {
    scroll-snap-type: x mandatory;
    width: 100%;
    padding: 32px;
    overflow-x: scroll;
}
.use-case-section .scroll-view .all-cards {
    gap: 12px;
    width: max-content;
    display: flex;
}
.use-case-section .scroll-view .all-cards > div {
    scroll-snap-align: center;
    min-width: 936px;
    max-width: 936px;
}
.use-case-section .slider-controls {
    justify-content: center;
    gap: 8px;
    display: flex;
}
.welcome-usecase-card {
    aspect-ratio: 16/9;
    background: #000;
    border-radius: 24px;
    flex-direction: column;
    display: flex;
    position: relative;
    overflow: hidden;
}
.welcome-usecase-card .media {
    position: absolute;
    inset: 0;
}
.welcome-usecase-card .media img,
.welcome-usecase-card .media video {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.welcome-usecase-card .details {
    color: #fff;
    z-index: 1;
    flex-direction: column;
    gap: 4px;
    padding: 28px 32px;
    display: flex;
}
.welcome-usecase-card .details p {
    opacity: 0.7;
    max-width: 320px;
}
.small-feature-card {
    background: rgba(var(--secondary), 1);
    aspect-ratio: 1;
    text-align: center;
    border-radius: 24px;
    padding: 20px;
    overflow: hidden;
}
@keyframes gradient {
    0% {
        background-position: 0 100%;
    }
    25% {
        background-position: 25%;
    }
    50% {
        background-position: 50% 25%;
    }
    75% {
        background-position: 75% 0;
    }
    to {
        background-position: 100%;
    }
}
.feedback-modal .reactions {
    padding: 24px 24px 0;
}
.feedback-modal .reactions .reactions-list {
    gap: 6px;
    margin: 20px 0;
    display: flex;
}
.feedback-modal .reactions .reactions-list .reaction-item {
    aspect-ratio: 1;
    background: rgba(var(--primary), 0.06);
    border-radius: 50%;
    flex: 1;
}
.feedback-modal .reactions .reactions-list .reaction-item.is-active {
    background: rgba(var(--primary), 1);
}
.feedback-modal .message {
    padding: 12px;
}
.feedback-modal .message .multiline-input {
    min-height: 200px;
}
.feedback-modal .message button {
    margin-top: 12px;
}
.verify,
.verify-card,
.verify-card > .form {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    display: flex;
}
.verify {
    background: rgba(var(--background), 1);
    gap: 12px;
    width: 100vw;
    height: 100vh;
}
.verify .verify-card {
    background: rgba(var(--primary), 0.06);
    border: solid 1px rgba(var(--primary), 0.06);
    border-radius: 24px;
    gap: 28px;
    max-width: 380px;
    padding: 28px;
}
.verify .verify-card .details {
    text-align: center;
}
.verify .verify-card .details p {
    margin-top: 12px;
}
.verify .verify-card .form {
    align-self: stretch;
    align-items: stretch;
    gap: 16px;
}
.verify .verify-card .form input {
    text-align: center;
    border-radius: 12px;
    padding: 12px;
}
.verify .cta-card > * {
    width: 100%;
}
.verify .cta-card .details h4 {
    color: #1da1f2;
}
.verify .cta-card .details p {
    margin-top: 6px;
}
.verify .cta-card button {
    color: #1da1f2;
    background: #1da1f21a;
}
.shots-admin {
    background: rgba(var(--body), 1);
    background: #000;
    flex-direction: column-reverse;
    width: 100vw;
    height: 100vh;
    display: flex;
    position: relative;
}
.shots-admin .admin-background {
    z-index: 0;
    position: absolute;
    inset: 0;
}
.shots-admin .admin-background img {
    object-fit: cover;
    filter: brightness(80%);
    width: 100%;
    height: 100%;
}
.shots-admin .shots-admin-tabs {
    z-index: 12;
    background: #64646499;
    border-radius: 100px;
    gap: 4px;
    width: max-content;
    margin: 0 auto;
    padding: 4px;
    display: flex;
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
}
.shots-admin .shots-admin-tabs button > span {
    display: none !important;
}
.shots-admin .shots-admin-tabs button.true-active {
    background: rgba(var(--primary), 0.36);
}
.shots-admin .shots-admin-tabs button.true-active > span {
    display: initial !important;
}
.shots-admin .head {
    inset: 0;
    bottom: unset;
    z-index: 10;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
    padding: 20px;
    display: flex;
    position: absolute;
}
.shots-admin .head:before {
    content: '';
    z-index: -1;
    -webkit-backdrop-filter: blur(1.5px);
    backdrop-filter: blur(1.5px);
    pointer-events: none;
    background: #0303034d;
    height: 120px;
    position: absolute;
    inset: 0;
    -webkit-mask-image: linear-gradient(#000 60%, #0000 100%);
    mask-image: linear-gradient(#000 60%, #0000 100%);
}
.shots-admin .head .actions {
    align-items: center;
    gap: 8px;
    display: flex;
}
.shots-admin .head .title {
    flex-direction: column;
    gap: 6px;
    display: flex;
}
.shots-admin .head .title h1 {
    letter-spacing: -0.8px;
    font:
        500 23px/32px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .shots-admin .head .title h1 {
        letter-spacing: -0.75px;
        font:
            500 21.5px/28px Inter,
            sans-serif;
    }
}
.shots-admin .head .title span {
    letter-spacing: -0.4px;
    font:
        500 15px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .shots-admin .head .title span {
        letter-spacing: -0.1px;
        font:
            500 15.5px/20px Inter,
            sans-serif;
    }
}
.shots-admin .shots-admin-dashboard {
    z-index: 1;
    align-items: stretch;
    display: flex;
    width: 100vw !important;
    height: 100vh !important;
}
.shots-admin .shots-admin-dashboard > * {
    flex: 1;
    overflow: hidden;
}
.shots-admin .shots-admin-dashboard > :first-child {
    border-right: solid 1px rgba(var(--primary), 0.06);
    max-width: 380px;
}
.shots-admin .shots-admin-dashboard > :nth-child(3) {
    border-left: solid 1px rgba(var(--primary), 0.06);
    max-width: 380px;
}
.shots-admin .shots-admin-view {
    width: 100%;
    height: 100%;
    position: relative;
}
.shots-admin .shots-admin-view:before {
    content: '';
    inset: 0;
    top: unset;
    z-index: 10;
    -webkit-backdrop-filter: blur(1.5px);
    backdrop-filter: blur(1.5px);
    pointer-events: none;
    background: #0303034d;
    height: 80px;
    position: absolute;
    -webkit-mask-image: linear-gradient(#0000 0%, #000 100%);
    mask-image: linear-gradient(#0000 0%, #000 100%);
}
.shots-admin .download-feedbacks {
    align-items: center;
    gap: 10px;
    margin-top: 16px;
    display: flex;
}
.shots-admin .download-feedbacks input {
    text-align: center;
}
.shots-admin .admin-system-tab {
    flex-direction: column;
    align-items: center;
    display: flex;
}
.shots-admin .admin-system-tab .v-stack {
    max-width: 560px;
}
.shots-admin .admin-system-tab .device-card {
    z-index: 1;
    background: #6464644d;
    border-radius: 28px;
    gap: 16px;
    padding: 20px;
    position: relative;
    overflow: hidden;
}
.shots-admin .admin-system-tab .device-card .stat-item {
    flex-direction: column;
    align-items: center;
    gap: 4px;
    display: flex;
}
.shots-admin .admin-system-tab .device-card .stat-item > span {
    color: rgba(var(--primary), 0.36);
    letter-spacing: 0.4px;
    text-transform: uppercase;
    font:
        600 11px/16px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .shots-admin .admin-system-tab .device-card .stat-item > span {
        font:
            500 11px/16px Inter,
            sans-serif;
    }
}
.shots-admin .admin-system-tab .device-card .stats {
    flex-wrap: wrap;
    gap: 20px;
    max-width: 66%;
    display: flex;
}
.shots-admin .admin-system-tab .device-card .task {
    background: rgba(var(--body), 1);
    border-radius: 20px;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    padding: 16px;
    display: flex;
}
.shots-admin .admin-system-tab .device-card .image {
    width: auto;
    height: 100%;
    max-height: 192px;
    position: absolute;
    top: 0;
    right: 0;
}
.shots-admin .generated-images {
    flex-wrap: wrap;
    gap: 6px;
    padding: 6px;
    display: flex;
}
.shots-admin .generated-images .top {
    flex-basis: 100%;
    justify-content: center;
    margin-top: 32px;
    display: flex;
}
.shots-admin .generated-images .admin-generated-item {
    aspect-ratio: 1;
    cursor: pointer;
    border-radius: 10px;
    flex: 240px;
    position: relative;
    overflow: hidden;
}
.shots-admin .generated-images .admin-generated-item .user-avatar {
    z-index: 1;
    outline: solid 1px rgba(var(--primary), 0.12);
    outline-offset: -1px;
    width: 36px;
    position: absolute;
    top: 10px;
    right: 10px;
}
.shots-admin .generated-images .admin-generated-item .export-info {
    inset: 10px;
    top: unset;
    z-index: 1;
    pointer-events: none;
    flex-wrap: wrap;
    gap: 4px;
    display: flex;
    position: absolute;
}
.shots-admin .generated-images .admin-generated-item .generated-video,
.shots-admin .generated-images .admin-generated-item .generated-image {
    aspect-ratio: 1;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}
.shots-admin .generated-images .admin-generated-item .generated-image img {
    object-fit: contain;
    z-index: 0;
    border-radius: 10px;
    width: 100%;
    max-height: 100%;
    margin: auto;
    position: absolute;
    inset: 0;
}
.shots-admin .generated-images .admin-generated-item .generated-image .date {
    z-index: 1;
    background: rgba(var(--background), 0.8);
    border-radius: 6px;
    padding: 4px 8px;
    position: absolute;
    top: 10px;
    left: 10px;
}
.info-items-stack {
    flex-wrap: wrap;
    gap: 8px;
    display: flex;
}
.info-item {
    background: rgba(var(--primary), 0.06);
    border-radius: 24px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    display: flex;
}
.admin-feedback-messages .v-stack .v-stack-content {
    flex-direction: column-reverse;
}
.admin-feedback-item {
    gap: 10px;
    width: 100%;
    position: relative;
}
.admin-feedback-item .user-info {
    flex-direction: column;
    display: flex;
}
.admin-feedback-item .item {
    flex-direction: column;
    flex: 1;
    gap: 20px;
    padding-right: 100px;
    display: flex;
    position: relative;
}
.admin-feedback-item .item .message {
    background: #6464644d;
    border-radius: 20px;
    width: max-content;
    max-width: 100%;
    padding: 10px 40px 10px 14px;
    position: relative;
}
.admin-feedback-item .item .message .emoji {
    font-size: 40px;
    position: absolute;
    bottom: 0;
    right: 0;
    transform: translate(10px, 10px) rotate(-5deg);
}
.admin-feedback-item .item .date {
    position: absolute;
    top: 0;
    right: 0;
}
.admin-feedback-item .info-items-stack {
    max-width: 520px;
}
.user-generated-images-modal {
    z-index: 100;
    place-items: center;
    display: grid;
    position: fixed;
    inset: 0;
    overflow-y: auto;
}
.user-generated-images-modal .backdrop {
    z-index: -1;
    background: rgba(var(--background), 0.8);
    position: fixed;
    inset: 0;
}
.user-generated-images-modal .layout {
    background: rgba(var(--panel), 1);
    border-radius: 32px;
    flex-direction: column;
    gap: 16px;
    width: 100%;
    max-width: 800px;
    height: 90vh;
    margin: 0 auto;
    padding: 32px;
    display: flex;
    overflow-y: auto;
}
.user-generated-images-modal .images,
.user-generated-media-modal .images {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 16px;
    display: grid;
}
.admin-auth {
    background: rgba(var(--background), 1);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 32px;
    width: 100vw;
    height: 100vh;
    display: flex;
}
.admin-auth form {
    flex-direction: column;
    gap: 12px;
    width: 320px;
    max-width: 100%;
    display: flex;
}
.admin-auth form input {
    text-align: center;
}
.admin-view-filter-drop {
    z-index: 12;
    position: absolute;
    bottom: 20px;
    left: 20px;
}
.admin-view-filter-drop .filter-button {
    background: #64646499;
}
.admin-view-filter-drop .filter-buttons {
    justify-content: flex-start;
    gap: 8px;
    width: 100%;
}
.subscriptions-tab {
    flex-direction: column;
    display: flex;
}
.subscriptions-tab .sub-user {
    background: #6464644d;
    border-radius: 16px;
    align-items: center;
    gap: 12px;
    padding: 8px;
    display: flex;
}
.subscriptions-tab .sub-user .copy {
    flex-direction: column;
    flex: 1;
    gap: 2px;
    display: flex;
    overflow: hidden;
}
.subscriptions-tab .sub-user .stats {
    align-items: center;
    gap: 8px;
    display: flex;
}
.subscriptions-tab .sub-date-group {
    flex-direction: column;
    gap: 10px;
    margin-bottom: 16px;
    display: flex;
}
.subscriptions-tab .subscription-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 32px;
    display: grid;
}
.subscriptions-tab .subscription-stats .stat-card {
    background: rgba(var(--panel-active), 1);
    border: 1px solid rgba(var(--primary), 0.06);
    border-radius: 16px;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    display: flex;
}
.subscriptions-tab .subscription-stats .stat-card .stat-value {
    color: rgba(var(--primary), 1);
    font-size: 36px;
    font-weight: 700;
}
.subscriptions-tab .subscription-stats .stat-card .stat-label {
    color: rgba(var(--primary), 0.6);
    letter-spacing: 0.4px;
    text-transform: uppercase;
    margin-top: 4px;
    font:
        600 11px/16px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .subscriptions-tab .subscription-stats .stat-card .stat-label {
        font:
            500 11px/16px Inter,
            sans-serif;
    }
}
.subscriptions-tab .subscription-table-container {
    border: 1px solid rgba(var(--primary), 0.06);
    background: rgba(var(--panel), 1);
    border-radius: 16px;
    width: 100%;
    margin-bottom: 40px;
    overflow-x: auto;
}
.subscriptions-tab .subscription-table-container .subscription-table {
    border-collapse: collapse;
    width: 100%;
}
.subscriptions-tab .subscription-table-container .subscription-table th {
    text-align: left;
    background: rgba(var(--panel-active), 1);
    color: rgba(var(--primary), 0.6);
    border-bottom: 1px solid rgba(var(--primary), 0.06);
    cursor: pointer;
    letter-spacing: -0.2px;
    white-space: nowrap;
    padding: 16px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .subscriptions-tab .subscription-table-container .subscription-table th {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.subscriptions-tab .subscription-table-container .subscription-table th:hover {
    color: rgba(var(--primary), 1);
}
.subscriptions-tab .subscription-table-container .subscription-table th .sort-arrow {
    color: rgba(var(--primary), 1);
    margin-left: 4px;
}
.subscriptions-tab .subscription-table-container .subscription-table td {
    border-bottom: 1px solid rgba(var(--primary), 0.06);
    letter-spacing: -0.3px;
    padding: 16px;
    font:
        16px/22px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .subscriptions-tab .subscription-table-container .subscription-table td {
        letter-spacing: -0.2px;
        font:
            17px/22px Inter,
            sans-serif;
    }
}
.subscriptions-tab .subscription-table-container .subscription-table td:last-child {
    width: 100px;
}
.subscriptions-tab .subscription-table-container .subscription-table tr:last-child td {
    border-bottom: none;
}
.subscriptions-tab .subscription-table-container .subscription-table .user-info {
    flex-direction: column;
    display: flex;
}
.subscriptions-tab .subscription-table-container .subscription-table .user-info .user-email {
    color: rgba(var(--primary), 1);
    font-weight: 500;
}
.subscriptions-tab .subscription-table-container .subscription-table .user-info .user-name {
    color: rgba(var(--primary), 0.36);
    letter-spacing: -0.2px;
    margin-top: 4px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .subscriptions-tab .subscription-table-container .subscription-table .user-info .user-name {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.subscriptions-tab .subscription-table-container .subscription-table .avatar-container {
    align-items: center;
    gap: 8px;
    display: flex;
}
.subscriptions-tab
    .subscription-table-container
    .subscription-table
    .avatar-container
    .user-avatar {
    object-fit: cover;
    border: 2px solid rgba(var(--primary), 0.06);
    border-radius: 50%;
    width: 40px;
    height: 40px;
}
.subscriptions-tab
    .subscription-table-container
    .subscription-table
    .avatar-container
    .avatar-placeholder {
    background: rgba(var(--primary), 1);
    width: 40px;
    height: 40px;
    color: rgba(var(--secondary), 1);
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    display: flex;
}
.subscriptions-tab .subscription-table-container .subscription-table .plan-badge {
    text-transform: capitalize;
    background: rgba(var(--primary), 0.06);
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
    display: inline-block;
}
.subscriptions-tab .subscription-table-container .subscription-table .plan-badge.premium {
    background: rgba(var(--background), 0.8);
    color: rgba(var(--primary), 1);
    border: 1px solid rgba(var(--info), 1);
}
.subscriptions-tab .subscription-table-container .subscription-table .plan-badge.basic {
    background: rgba(var(--background), 0.8);
    color: rgba(var(--primary), 1);
    border: 1px solid rgba(var(--success), 1);
}
.subscriptions-tab .subscription-table-container .subscription-table .plan-badge.free {
    background: rgba(var(--background), 0.8);
    color: rgba(var(--primary), 1);
    border: 1px solid rgba(var(--primary), 0.36);
}
.subscriptions-tab .subscription-table-container .subscription-table .renewal-date {
    color: rgba(var(--primary), 1);
}
.subscriptions-tab .subscription-table-container .subscription-table .status-pill {
    text-transform: capitalize;
    border-radius: 20px;
    padding: 4px 10px;
    font-size: 12px;
    display: inline-block;
}
.subscriptions-tab .subscription-table-container .subscription-table .status-pill.active {
    background: rgba(var(--background), 0.8);
    color: rgba(var(--success), 1);
    border: 1px solid rgba(var(--success), 1);
}
.subscriptions-tab .subscription-table-container .subscription-table .status-pill.canceled {
    background: rgba(var(--background), 0.8);
    color: rgba(var(--danger), 1);
    border: 1px solid rgba(var(--danger), 1);
}
.subscriptions-tab .subscription-table-container .subscription-table .action-buttons {
    gap: 8px;
    display: flex;
}
.subscriptions-tab .loading-container {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 16px;
    height: 300px;
    display: flex;
}
.subscriptions-tab .loading-container .spinner {
    border: 3px solid rgba(var(--primary), 0.06);
    border-top-color: rgba(var(--primary), 1);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: 1s linear infinite spin;
}
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
.subscriptions-tab .empty-state {
    background: rgba(var(--panel-active), 1);
    border: 1px dashed rgba(var(--primary), 0.06);
    border-radius: 16px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
    display: flex;
}
.subscriptions-tab .empty-state .empty-icon {
    margin-bottom: 16px;
    font-size: 48px;
}
.subscriptions-tab .empty-state h3 {
    color: rgba(var(--primary), 1);
    letter-spacing: -0.6px;
    margin-bottom: 8px;
    font:
        500 17px/24px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .subscriptions-tab .empty-state h3 {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
.subscriptions-tab .empty-state p {
    color: rgba(var(--primary), 0.36);
    text-align: center;
    max-width: 400px;
}
.loading-portal {
    background: #000;
    place-items: center;
    display: grid;
    position: fixed;
    inset: 0;
}
.loading-portal .preloader svg {
    transform: scale(1.6);
}
.loading-portal .preloader circle {
    stroke: rgba(var(--primary), 0.6);
}
.page-main {
    background: #000;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    display: flex;
    overflow-y: auto;
}
.page-main .layout {
    flex-direction: column;
    width: 100%;
    max-width: 640px;
    height: max-content;
    padding: 32px 16px;
    display: flex;
}
.auth-page .layout {
    justify-content: center;
    align-self: center;
    gap: 32px;
    max-width: 380px;
}
.auth-page .head {
    flex-direction: column;
    gap: 16px;
    display: flex;
}
.auth-page .head h1 {
    font-weight: 500;
    line-height: 110%;
}
.auth-page .head h1 span {
    color: #eb432a;
}
.auth-page form {
    flex-direction: column;
    gap: 16px;
    display: flex;
}
.auth-page form input,
.auth-page form button {
    text-align: center;
    border-radius: 10px;
    width: 100%;
    padding: 10px;
}
.shots-ad-portal .layout {
    gap: 8px;
}
.shots-ad-portal .page-head {
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    display: flex;
}
.shots-ad-portal .page-head .name {
    align-items: center;
    gap: 8px;
    display: flex;
}
.shots-ad-portal .page-head .name .logo {
    width: 40px;
    height: 40px;
}
.shots-ad-portal .page-head .buttons {
    gap: 8px;
}
.shots-ad-portal .total-stats .head {
    flex-direction: row;
    justify-content: space-between;
}
.shots-ad-portal .total-stats .head .live {
    background: rgba(var(--primary), 1);
    color: rgba(var(--secondary), 1);
    border-radius: 8px;
    padding: 4px 8px;
}
.shots-ad-portal .total-stats .image-container {
    aspect-ratio: 3/2;
    background: rgba(var(--primary), 0.12);
    border-radius: 12px;
    align-self: center;
    width: 70%;
    max-width: 320px;
    margin: 16px auto;
    position: relative;
    overflow: hidden;
    transform: rotate(-3deg);
    box-shadow: 0 10px 20px #0000004d;
}
.shots-ad-portal .total-stats .image-container img {
    width: 100%;
}
.shots-ad-portal .total-stats .stats {
    flex-basis: 100%;
}
.shots-ad-portal .total-stats .progress {
    background: rgba(var(--primary), 0.12);
    border-radius: 4px;
    width: 100%;
    height: 8px;
    overflow: hidden;
}
.shots-ad-portal .total-stats .progress .track {
    background: rgba(var(--primary), 0.6);
    border-radius: inherit;
    height: 100%;
}
.shots-ad-portal .calendar-stats .graph-container {
    aspect-ratio: 2;
    flex-basis: 100%;
}
.shots-ad-portal .stat-box {
    border-right: solid 1px rgba(var(--primary), 0.12);
    gap: 8px;
}
.shots-ad-portal .stat-box:not(:first-child) {
    padding-left: 32px;
}
.shots-ad-portal .stat-box:not(:last-child) {
    padding-right: 32px;
}
.shots-ad-portal .stat-box:last-child {
    border-right: none;
}
.stats-widget {
    background: rgba(var(--panel), 1);
    color: rgba(var(--primary), 1);
    border-radius: 24px;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;
    display: flex;
    position: relative;
    overflow: hidden;
}
.stats-widget .head {
    flex-direction: column;
    flex-basis: 100%;
    gap: 4px;
    display: flex;
}
.stats-widget .head .title {
    align-items: center;
    gap: 8px;
    display: flex;
}
.stats-widget .head .title svg {
    width: 24px;
    height: 24px;
}
.stats-widget .head .title span {
    letter-spacing: -0.4px;
    font:
        500 19px/26px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .stats-widget .head .title span {
        letter-spacing: -0.5px;
        font:
            500 19.5px/25px Inter,
            sans-serif;
    }
}
.stats-widget .stats {
    display: flex;
}
.stats-widget .stats .stats-subvalue svg {
    width: 16px;
    height: 16px;
    margin-right: 4px;
}
.color-picker .react-colorful {
    width: 100%;
    padding: 0;
}
.color-picker .react-colorful__last-control {
    display: none;
}
.color-picker .react-colorful__saturation {
    border-bottom: none;
    border-radius: 8px;
    margin-bottom: 16px;
}
.color-picker .react-colorful__hue,
.color-picker .react-colorful__alpha {
    border-radius: 8px;
    height: 16px;
}
.color-picker .react-colorful__hue-pointer,
.color-picker .react-colorful__alpha-pointer {
    width: 20px;
    height: 20px;
}
.timeline-component {
    height: max-content;
    min-height: max-content;
    max-height: max-content;
    position: relative;
}
.timeline-component .timeline-zoom-slider .slider-component {
    border-radius: 50px;
}
.timeline-component .timeline-zoom-slider .slider-component .track {
    background: rgba(var(--primary), 0.06);
}
.timeline-component .timeline-zoom-slider .slider-component .labels {
    padding: 0 12px;
}
.timeline-component .timeline-controls {
    z-index: 1;
    gap: 16px;
    padding: 12px 16px;
    display: flex;
    position: absolute;
    top: -64px;
    left: 228px;
    right: 228px;
}
@media only screen and (width>=800px) and (width<=1200px) {
    .timeline-component .timeline-controls {
        right: 0;
    }
}
.timeline-component .timeline-controls .left,
.timeline-component .timeline-controls .center,
.timeline-component .timeline-controls .right {
    align-items: center;
    gap: 12px;
    display: flex;
}
.timeline-component .timeline-controls .left > .wrapper,
.timeline-component .timeline-controls .center > .wrapper,
.timeline-component .timeline-controls .right > .wrapper {
    width: max-content;
    display: inherit;
    align-items: inherit;
    gap: inherit;
}
.timeline-component .timeline-controls .left,
.timeline-component .timeline-controls .right {
    flex: 1;
    justify-content: space-between;
}
.timeline-component .timeline-track-wrapper {
    background: rgba(var(--panel), 1);
    border-radius: 16px;
    flex-direction: column;
    justify-content: flex-end;
    padding: 8px 12px 6px;
    display: flex;
    position: relative;
    overflow: auto hidden;
}
.timeline-component .timeline-track-wrapper .timeline-bottom-controls {
    inset: 6px;
    top: unset;
    z-index: 10;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    padding: 8px;
    display: flex;
    position: fixed;
}
.timeline-component .timeline-track-wrapper:hover .slide-duration-handle .handle-knob {
    opacity: 1 !important;
    visibility: visible !important;
}
.timeline-component .timeline-track-wrapper:hover .slide-duration-handle .hint {
    opacity: 0.7;
}
.timeline-component .timeline-track-wrapper .slide-duration-handle {
    cursor: ew-resize;
    z-index: 5;
    justify-content: center;
    align-items: center;
    width: 24px;
    display: flex;
    position: absolute;
    top: 0;
    bottom: 0;
}
.timeline-component .timeline-track-wrapper .slide-duration-handle:active .hint {
    opacity: 0;
}
.timeline-component .timeline-track-wrapper .slide-duration-handle:hover .handle-knob {
    background: rgba(var(--primary), 1);
    width: 4px;
    height: 55%;
}
.timeline-component .timeline-track-wrapper .slide-duration-handle:hover .hint {
    opacity: 0;
}
.timeline-component .timeline-track-wrapper .slide-duration-handle .none-slide-area {
    background: rgba(var(--panel-dim), 0.5);
    pointer-events: none;
    border-left: solid 1px rgba(var(--panel-dim), 1);
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    position: absolute;
    top: -8px;
    bottom: -6px;
    left: 0;
}
.timeline-component .timeline-track-wrapper .slide-duration-handle .handle-knob {
    z-index: 100;
    background: rgba(var(--primary), 0.36);
    opacity: 0;
    visibility: hidden;
    border-radius: 20px;
    width: 2px;
    height: 50%;
}
.timeline-component .timeline-track-wrapper .slide-duration-handle .hint {
    pointer-events: none;
    width: max-content;
    color: rgba(var(--primary), 0.36);
    opacity: 0;
    letter-spacing: -0.4px;
    font:
        500 14px/20px Inter,
        sans-serif;
    position: absolute;
    left: 100%;
}
@media only screen and (width>=0) and (width<=800px) {
    .timeline-component .timeline-track-wrapper .slide-duration-handle .hint {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.timeline-component .timeline-track-wrapper .timeline-track {
    flex-direction: column;
    display: flex;
    position: relative;
}
.timeline-component .timeline-track-wrapper .timeline-track .track-label {
    z-index: 6;
    pointer-events: none;
    color: rgba(var(--primary), 0.6);
    background: linear-gradient(90deg, rgba(var(--panel), 0.85) 30%, transparent 100%);
    align-items: center;
    gap: 6px;
    padding-left: 10px;
    padding-right: 32px;
    display: flex;
    position: absolute;
    top: 0;
    bottom: 0;
    left: -4px;
}
.timeline-component .timeline-track-wrapper .timeline-track .track-label svg {
    width: 26px;
    height: 26px;
}
.timeline-component .timeline-track-wrapper .timeline-track .track-label span {
    letter-spacing: -0.4px;
    font:
        500 14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .timeline-component .timeline-track-wrapper .timeline-track .track-label span {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.timeline-component .timeline-track-wrapper .timeline-track .track-label .mockup-label-preview {
    aspect-ratio: 3/3;
    width: 40px;
}
.timeline-component .timeline-track-wrapper .timeline-track .track-label .mockup-label-preview img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.timeline-component .timeline-track-wrapper .timeline-track .time-track {
    width: 100%;
    height: 24px;
    display: flex;
}
.timeline-component .timeline-track-wrapper .timeline-track .base-track,
.timeline-component .timeline-track-wrapper .timeline-track .animation-track,
.timeline-component .timeline-track-wrapper .timeline-track .video-track {
    width: 100%;
    height: max-content;
    padding: 6px 0;
    position: relative;
}
.timeline-component .timeline-track-wrapper .timeline-track .base-track:hover .track-label,
.timeline-component .timeline-track-wrapper .timeline-track .animation-track:hover .track-label,
.timeline-component .timeline-track-wrapper .timeline-track .video-track:hover .track-label {
    opacity: 0;
}
.timeline-component .timeline-track-wrapper .timeline-track .base-track .base-track-inner,
.timeline-component .timeline-track-wrapper .timeline-track .base-track .animation-track-inner,
.timeline-component .timeline-track-wrapper .timeline-track .base-track .video-track-inner,
.timeline-component .timeline-track-wrapper .timeline-track .animation-track .base-track-inner,
.timeline-component .timeline-track-wrapper .timeline-track .animation-track .animation-track-inner,
.timeline-component .timeline-track-wrapper .timeline-track .animation-track .video-track-inner,
.timeline-component .timeline-track-wrapper .timeline-track .video-track .base-track-inner,
.timeline-component .timeline-track-wrapper .timeline-track .video-track .animation-track-inner,
.timeline-component .timeline-track-wrapper .timeline-track .video-track .video-track-inner {
    border-radius: 12px;
    width: 100%;
    transition: inherit;
    position: relative;
}
.timeline-component .timeline-track-wrapper .timeline-track .animation-track {
    z-index: 5;
}
.timeline-component .timeline-track-wrapper .timeline-track .animation-track .animation-clip-item {
    height: inherit;
}
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .animation-track.animation-track-minimized
    .animation-track-inner {
    height: 12px;
}
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .animation-track.animation-track-minimized
    .details,
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .animation-track.animation-track-minimized
    .is-hidden-badge,
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .animation-track.animation-track-minimized
    .handle {
    opacity: 0;
}
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .animation-track.animation-track-minimized
    .animation-clip-preview {
    visibility: hidden;
    opacity: 0;
}
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .animation-track.animation-track-expanded
    .animation-track-inner,
.timeline-component .timeline-track-wrapper .timeline-track .base-track .base-track-inner {
    height: 44px;
}
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .base-track
    .base-track-inner
    .base-clip-item {
    height: inherit;
}
.timeline-component .timeline-track-wrapper .timeline-track .video-track {
    z-index: 4;
}
.timeline-component .timeline-track-wrapper .timeline-track .video-track .video-track-inner {
    background: rgba(var(--panel-dim), 1);
}
.timeline-component .timeline-track-wrapper .timeline-track .video-track .video-clip-item,
.timeline-component .timeline-track-wrapper .timeline-track .video-track .pause-clip-item {
    height: inherit;
}
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .video-track.video-track-minimized
    .video-track-inner {
    height: 12px;
}
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .video-track.video-track-minimized
    .video-clip-item
    .details,
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .video-track.video-track-minimized
    .video-clip-item
    .handle,
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .video-track.video-track-minimized
    .video-clip-item
    .image-media-preview,
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .video-track.video-track-minimized
    .pause-clip-item
    span {
    opacity: 0;
}
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .video-track.video-track-expanded
    .video-track-inner {
    height: 44px;
}
.timeline-component
    .timeline-track-wrapper
    .timeline-track
    .video-track.video-track-expanded
    .video-clip-item
    .clip
    .handle:after {
    opacity: 1;
}
.add-animation-icon {
    background: linear-gradient(rgba(var(--panel-active), 0.3), rgba(var(--panel-active), 0.9));
    aspect-ratio: 3/2;
    outline: solid 1px rgba(var(--panel-active), 0.5);
    outline-offset: -1px;
    border-radius: 7.5px;
    place-items: center;
    width: 40px;
    display: grid;
}
.add-animation-icon svg {
    width: 18px;
    height: 18px;
    color: rgba(var(--primary), 0.6);
}
.timeline-new-animation .add-animation-icon {
    border-radius: 12px;
    width: 100%;
    height: 44px;
    box-shadow: 0 8px 20px #0006;
}
.timeline-new-animation .add-animation-icon svg {
    width: 24px;
    height: 24px;
}
.animation-track-empty-state {
    position: absolute;
    inset: 0;
}
.animation-track-empty-state .wrapper {
    justify-content: center;
    align-items: center;
    gap: 8px;
    max-width: 100vw;
    height: 100%;
    display: flex;
    position: sticky;
    left: 0;
}
.mockup-track {
    cursor: pointer;
    width: 100%;
    height: max-content;
    position: relative;
}
.mockup-track:hover .track-label {
    opacity: 0;
}
.mockup-track:before {
    content: '';
    background: rgba(var(--primary), 0.06);
    border-radius: 15px;
    position: absolute;
    inset: 3px -2.5px;
}
.mockup-track.is-active:before {
    outline-offset: 0 !important;
}
.base-clip-item,
.animation-clip-item,
.video-clip-item {
    --color: rgba(var(--panel-active), 0.5);
}
.base-clip-item .clip,
.animation-clip-item .clip,
.video-clip-item .clip {
    cursor: pointer;
    background: linear-gradient(rgba(var(--panel-active), 0.3), rgba(var(--panel-active), 0.9));
    border-radius: 12px;
    width: calc(100% - 1px);
    height: 100%;
    margin-left: 0.5px;
    display: flex;
    position: absolute;
}
.base-clip-item .clip:active,
.animation-clip-item .clip:active,
.video-clip-item .clip:active {
    box-shadow: 0 4px 16px -2px rgba(var(--background), 0.8);
    transform: translateY(-3px);
}
.base-clip-item .clip:after,
.animation-clip-item .clip:after,
.video-clip-item .clip:after {
    content: '';
    border-radius: inherit;
    outline: solid 1px rgba(var(--panel-active), 0.5);
    outline-offset: -1px;
    pointer-events: none;
    z-index: 1;
    position: absolute;
    inset: 0;
}
.base-clip-item .clip .clip-content,
.animation-clip-item .clip .clip-content,
.video-clip-item .clip .clip-content {
    z-index: 2;
    border-radius: inherit;
    width: 100%;
    max-width: 100vw;
    height: 100%;
    padding: 0 2px;
    display: flex;
    position: sticky;
    left: 0;
    overflow: hidden;
}
.base-clip-item .clip .clip-content:before,
.base-clip-item .clip .clip-content:after,
.animation-clip-item .clip .clip-content:before,
.animation-clip-item .clip .clip-content:after,
.video-clip-item .clip .clip-content:before,
.video-clip-item .clip .clip-content:after {
    content: '';
    flex: 1;
}
.base-clip-item .clip .clip-content:before,
.animation-clip-item .clip .clip-content:before,
.video-clip-item .clip .clip-content:before {
    order: 1;
}
.base-clip-item .clip .clip-content:after,
.animation-clip-item .clip .clip-content:after,
.video-clip-item .clip .clip-content:after {
    order: 3;
}
.base-clip-item .clip .clip-content .clip-details,
.animation-clip-item .clip .clip-content .clip-details,
.video-clip-item .clip .clip-content .clip-details {
    order: 2;
    align-items: center;
    gap: 8px;
    display: flex;
    position: relative;
}
.base-clip-item .clip .clip-content .clip-details .badge,
.animation-clip-item .clip .clip-content .clip-details .badge,
.video-clip-item .clip .clip-content .clip-details .badge {
    align-items: center;
    gap: 4px;
    margin-left: 4px;
    display: flex;
}
.base-clip-item .clip .clip-content .clip-details .badge svg,
.animation-clip-item .clip .clip-content .clip-details .badge svg,
.video-clip-item .clip .clip-content .clip-details .badge svg {
    width: 20px;
    height: 20px;
}
.base-clip-item .clip .clip-content .clip-details .badge span,
.animation-clip-item .clip .clip-content .clip-details .badge span,
.video-clip-item .clip .clip-content .clip-details .badge span {
    letter-spacing: -0.4px;
    font:
        500 14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .base-clip-item .clip .clip-content .clip-details .badge span,
    .animation-clip-item .clip .clip-content .clip-details .badge span,
    .video-clip-item .clip .clip-content .clip-details .badge span {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.base-clip-item .clip .clip-content .clip-details .copy,
.animation-clip-item .clip .clip-content .clip-details .copy,
.video-clip-item .clip .clip-content .clip-details .copy {
    pointer-events: none;
    flex-direction: column;
    align-items: flex-start;
    display: flex;
}
.base-clip-item .clip .clip-content .clip-details .copy > span:first-child,
.animation-clip-item .clip .clip-content .clip-details .copy > span:first-child,
.video-clip-item .clip .clip-content .clip-details .copy > span:first-child {
    color: rgba(var(--primary), 0.6);
    letter-spacing: -0.2px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .base-clip-item .clip .clip-content .clip-details .copy > span:first-child,
    .animation-clip-item .clip .clip-content .clip-details .copy > span:first-child,
    .video-clip-item .clip .clip-content .clip-details .copy > span:first-child {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.base-clip-item .clip .clip-content .clip-details .copy > span:nth-child(2),
.animation-clip-item .clip .clip-content .clip-details .copy > span:nth-child(2),
.video-clip-item .clip .clip-content .clip-details .copy > span:nth-child(2) {
    letter-spacing: -0.2px;
    font:
        12.5px/20px Inter,
        sans-serif;
    font-weight: 500 !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .base-clip-item .clip .clip-content .clip-details .copy > span:nth-child(2),
    .animation-clip-item .clip .clip-content .clip-details .copy > span:nth-child(2),
    .video-clip-item .clip .clip-content .clip-details .copy > span:nth-child(2) {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.base-clip-item .clip .clip-content .clip-details .copy .badges,
.animation-clip-item .clip .clip-content .clip-details .copy .badges,
.video-clip-item .clip .clip-content .clip-details .copy .badges {
    align-items: center;
    gap: 9px;
    margin-top: 3px;
    display: flex;
}
.base-clip-item .clip .clip-content .clip-details .copy .badges > div,
.animation-clip-item .clip .clip-content .clip-details .copy .badges > div,
.video-clip-item .clip .clip-content .clip-details .copy .badges > div {
    align-items: center;
    gap: 3px;
    display: flex;
}
.base-clip-item .clip .clip-content .clip-details .copy .badges > div svg,
.animation-clip-item .clip .clip-content .clip-details .copy .badges > div svg,
.video-clip-item .clip .clip-content .clip-details .copy .badges > div svg {
    width: 14px;
    height: 14px;
}
.base-clip-item .clip .clip-content .clip-details .copy .badges > div span,
.animation-clip-item .clip .clip-content .clip-details .copy .badges > div span,
.video-clip-item .clip .clip-content .clip-details .copy .badges > div span {
    letter-spacing: -0.2px;
    font:
        12.5px/20px Inter,
        sans-serif;
    font-weight: 500 !important;
    line-height: 14px !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .base-clip-item .clip .clip-content .clip-details .copy .badges > div span,
    .animation-clip-item .clip .clip-content .clip-details .copy .badges > div span,
    .video-clip-item .clip .clip-content .clip-details .copy .badges > div span {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.base-clip-item .clip .clip-content .clip-details .mockup-clip-preview,
.animation-clip-item .clip .clip-content .clip-details .mockup-clip-preview,
.video-clip-item .clip .clip-content .clip-details .mockup-clip-preview {
    aspect-ratio: 4/3;
    height: 100%;
}
.base-clip-item .clip .clip-content .clip-details .mockup-clip-preview img,
.animation-clip-item .clip .clip-content .clip-details .mockup-clip-preview img,
.video-clip-item .clip .clip-content .clip-details .mockup-clip-preview img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.base-clip-item .clip .clip-content .clip-details .media-clip-preview svg,
.animation-clip-item .clip .clip-content .clip-details .media-clip-preview svg,
.video-clip-item .clip .clip-content .clip-details .media-clip-preview svg {
    width: 24px;
    height: 24px;
}
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview,
.animation-clip-item .clip .clip-content .clip-details .animation-clip-preview,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview {
    z-index: 3;
    border-radius: 10px;
    justify-content: center;
    width: max-content;
    max-width: 100%;
    display: flex;
    overflow: hidden;
}
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview:hover,
.animation-clip-item .clip .clip-content .clip-details .animation-clip-preview:hover,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview:hover {
    overflow: visible !important;
}
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .layout-item,
.animation-clip-item .clip .clip-content .clip-details .animation-clip-preview .layout-item,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .layout-item {
    border-radius: 8px;
}
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .timeline-clip-preview,
.animation-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .timeline-clip-preview,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .timeline-clip-preview {
    border-radius: 8px;
    height: 100%;
    position: relative;
    overflow: hidden;
}
.base-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .timeline-clip-preview:hover,
.animation-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .timeline-clip-preview:hover,
.video-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .timeline-clip-preview:hover {
    box-shadow: 0 4px 8px #00000080;
}
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame,
.animation-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame {
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame:hover,
.animation-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame:hover,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame:hover {
    box-shadow: 0 4px 8px #00000080;
}
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame *,
.animation-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame *,
.video-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame * {
    pointer-events: none !important;
}
.base-clip-item .clip .clip-content .clip-details .animation-clip-preview .base-frame .empty-drop *,
.animation-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .base-frame
    .empty-drop
    *,
.video-clip-item
    .clip
    .clip-content
    .clip-details
    .animation-clip-preview
    .base-frame
    .empty-drop
    * {
    visibility: hidden;
    display: none;
}
.base-clip-item .clip .handle,
.animation-clip-item .clip .handle,
.video-clip-item .clip .handle {
    cursor: ew-resize;
    z-index: 4;
    visibility: hidden;
    opacity: 0;
    border-radius: 12px;
    width: max-content;
    height: 100%;
    padding: 8px;
    transition: inherit;
    display: flex;
    position: absolute;
}
.base-clip-item .clip .handle:before,
.animation-clip-item .clip .handle:before,
.video-clip-item .clip .handle:before {
    content: '';
    background: rgba(var(--primary), 0.6);
    z-index: 1;
    border-radius: 3px;
    width: 3px;
    height: 100%;
    transition: inherit;
}
.base-clip-item .clip .handle:after,
.animation-clip-item .clip .handle:after,
.video-clip-item .clip .handle:after {
    content: '';
    pointer-events: none;
    width: 150%;
    transition: inherit;
    position: absolute;
    top: 0;
    bottom: 0;
}
.base-clip-item .clip .handle.handle-left,
.animation-clip-item .clip .handle.handle-left,
.video-clip-item .clip .handle.handle-left {
    left: 0;
}
.base-clip-item .clip .handle.handle-left:after,
.animation-clip-item .clip .handle.handle-left:after,
.video-clip-item .clip .handle.handle-left:after {
    background: linear-gradient(90deg, rgba(var(--panel-active), 0.5) 0%, transparent);
    border-radius: 12px 0 0 12px;
    left: 0;
}
.base-clip-item .clip .handle.handle-right,
.animation-clip-item .clip .handle.handle-right,
.video-clip-item .clip .handle.handle-right {
    right: 0;
}
.base-clip-item .clip .handle.handle-right:after,
.animation-clip-item .clip .handle.handle-right:after,
.video-clip-item .clip .handle.handle-right:after {
    background: linear-gradient(-90deg, rgba(var(--panel-active), 0.5) 0%, transparent);
    border-radius: 0 12px 12px 0;
    right: 0;
}
.base-clip-item .clip .handle:hover:before,
.animation-clip-item .clip .handle:hover:before,
.video-clip-item .clip .handle:hover:before {
    background: rgba(var(--primary), 1);
}
.base-clip-item .clip .handle:hover:after,
.animation-clip-item .clip .handle:hover:after,
.video-clip-item .clip .handle:hover:after {
    opacity: 1;
}
.base-clip-item .options-ornament-wrapper,
.animation-clip-item .options-ornament-wrapper,
.video-clip-item .options-ornament-wrapper {
    opacity: 0;
    visibility: hidden;
    z-index: 10;
    justify-content: center;
    align-items: center;
    width: max-content;
    margin: 0 auto;
    padding: 8px 8px 0;
    display: flex;
    position: absolute;
    bottom: -18px;
    left: 0;
    right: 0;
}
.base-clip-item .options-ornament-wrapper.is-active,
.animation-clip-item .options-ornament-wrapper.is-active,
.video-clip-item .options-ornament-wrapper.is-active {
    opacity: 1 !important;
    visibility: visible !important;
}
.base-clip-item .options-ornament,
.animation-clip-item .options-ornament,
.video-clip-item .options-ornament {
    background: rgba(var(--modal), 1);
    outline: solid 1px rgba(var(--primary), 0.12);
    outline-offset: -1px;
    border-radius: 100px;
}
.base-clip-item:hover .clip .handle,
.animation-clip-item:hover .clip .handle,
.video-clip-item:hover .clip .handle {
    visibility: visible;
    opacity: 1;
}
.base-clip-item:hover .options-ornament-wrapper,
.animation-clip-item:hover .options-ornament-wrapper,
.video-clip-item:hover .options-ornament-wrapper {
    opacity: 1;
    visibility: visible;
}
.base-clip-item:active,
.animation-clip-item:active,
.video-clip-item:active {
    z-index: 5;
}
.base-clip-item:active .clip,
.animation-clip-item:active .clip,
.video-clip-item:active .clip {
    cursor: grabbing;
}
.base-clip-item.is-hidden .clip,
.animation-clip-item.is-hidden .clip,
.video-clip-item.is-hidden .clip {
    opacity: 0.5;
}
.base-clip-item.is-hidden .clip .is-hidden-badge,
.animation-clip-item.is-hidden .clip .is-hidden-badge,
.video-clip-item.is-hidden .clip .is-hidden-badge {
    z-index: 4;
    place-items: center;
    display: grid;
    position: absolute;
    inset: 0;
}
.base-clip-item.is-hidden .clip .is-hidden-badge svg,
.animation-clip-item.is-hidden .clip .is-hidden-badge svg,
.video-clip-item.is-hidden .clip .is-hidden-badge svg {
    width: 24px;
    height: 24px;
}
.base-clip-item.is-editing,
.animation-clip-item.is-editing,
.video-clip-item.is-editing {
    z-index: 4;
}
.base-clip-item.is-editing .clip .handle,
.animation-clip-item.is-editing .clip .handle,
.video-clip-item.is-editing .clip .handle {
    visibility: visible;
    opacity: 1;
}
.base-clip-item .timeline-clip-is-playing,
.animation-clip-item .timeline-clip-is-playing,
.video-clip-item .timeline-clip-is-playing {
    z-index: 0;
    pointer-events: none;
    background: rgba(var(--primary), 0.36);
    opacity: 0;
    border-radius: 6px;
    height: 2px;
    position: absolute;
    bottom: -5px;
    left: 2px;
    right: 2px;
    display: none !important;
}
.base-clip-item .timeline-clip-is-playing.is-active,
.animation-clip-item .timeline-clip-is-playing.is-active,
.video-clip-item .timeline-clip-is-playing.is-active {
    opacity: 1;
}
.animation-clip-item .handle:after {
    display: none;
}
.animation-clip-item.intro-kind.is-disabled .clip,
.animation-clip-item.outro-kind.is-disabled .clip {
    outline: 1px dashed rgba(var(--primary), 0.12);
    outline-offset: -1px;
    background: 0 0 !important;
}
.animation-clip-item.intro-kind.is-disabled .clip .details,
.animation-clip-item.outro-kind.is-disabled .clip .details {
    opacity: 0.4;
}
.video-clip-item {
    position: relative;
}
.video-clip-item .clip:after {
    z-index: 3;
}
.video-clip-item .clip .clip-content:before {
    background: linear-gradient(-90deg, rgba(var(--panel), 0.7) 0%, transparent 50%);
}
.video-clip-item .clip .clip-content:after {
    background: linear-gradient(90deg, rgba(var(--panel), 0.7) 0%, transparent 50%);
}
.video-clip-item .clip .clip-content .clip-details {
    background: rgba(var(--panel), 0.7);
}
.video-clip-item .clip .video-thumbnails-wrapper {
    border-radius: inherit;
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.video-clip-item .clip .video-thumbnails-wrapper .video-thumbnails-container {
    border-radius: inherit;
    pointer-events: none;
    position: absolute;
    top: 0;
    bottom: 0;
    overflow: hidden;
}
.video-clip-item .image-media-preview {
    pointer-events: none;
    align-items: center;
    gap: 6px;
    width: max-content;
    height: 44px;
    margin: 0 auto;
    display: flex;
    position: absolute;
    inset: 0;
}
.video-clip-item .image-media-preview .media-item {
    aspect-ratio: 3/4;
    border-radius: 4px;
    height: 44px;
    overflow: hidden;
}
.video-clip-item .image-media-preview .media-item img {
    width: 100%;
    height: 100%;
}
.pause-clip-item {
    z-index: 0;
    height: 44px;
    position: absolute;
    top: 0;
}
.pause-clip-item .clip {
    border-radius: 12px;
    align-items: center;
    width: calc(100% + 20px);
    height: 100%;
    padding: 0 12px;
    display: flex;
    overflow: hidden;
}
.pause-clip-item .clip span {
    color: rgba(var(--primary), 0.36);
    letter-spacing: -0.2px;
    font:
        11px/14px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .pause-clip-item .clip span {
        letter-spacing: 0.2px;
        font:
            10.5px/13px Inter,
            sans-serif;
    }
}
.pause-clip-item.end-pause .clip {
    justify-content: flex-end;
    margin-left: -20px;
}
.timeline-clip-hover-preview {
    z-index: 1;
    border-radius: 10px;
    position: absolute;
    bottom: calc(100% + 8px);
    overflow: hidden;
}
.timeline-clip-hover-preview > div {
    background: rgba(var(--panel), 1);
    width: 100%;
    height: 100%;
}
.timeline-clip-hover-preview.intro-kind {
    left: 0;
}
.timeline-clip-hover-preview.middle-kind,
.timeline-clip-hover-preview.outro-kind {
    left: 50%;
    transform: translate(-50%);
}
.timeline-ghost-ticker {
    z-index: 6;
    pointer-events: none;
    width: 1.5px;
    height: 200%;
    position: absolute;
}
.timeline-ghost-ticker .needle {
    opacity: 0.5;
    background: #ff342a;
    width: 1.5px;
    height: 100%;
}
.timeline-track-ticker {
    z-index: 7;
    pointer-events: none;
    flex-direction: column;
    align-items: center;
    width: 1.5px;
    height: calc(100% - 6px);
    display: flex;
    position: absolute;
}
.timeline-track-ticker.is-active .needle {
    opacity: 0.7 !important;
    background: #ff342a !important;
}
.timeline-track-ticker.is-active .orb {
    outline-color: #ff342a !important;
}
.timeline-track-ticker .needle {
    background: rgba(var(--primary), 1);
    opacity: 0.5;
    z-index: 1;
    pointer-events: none;
    border-radius: 2px;
    width: 1.5px;
    height: 200%;
    margin-top: 24px;
    position: absolute;
}
.timeline-track-ticker .orb {
    background: rgba(var(--secondary), 1);
    outline: solid 1.5px rgba(var(--primary), 1);
    outline-offset: -1.5px;
    pointer-events: all;
    z-index: 2;
    cursor: grab;
    border-radius: 40px;
    justify-content: center;
    align-items: center;
    width: 12px;
    max-width: 12px;
    min-height: 24px;
    display: flex;
    bottom: 0;
    left: 0;
}
.timeline-track-ticker .orb span {
    color: rgba(var(--secondary), 1);
    opacity: 0;
    transition: inherit !important;
}
.timeline-track-ticker .orb:active {
    cursor: grabbing;
    transform: scale(1.1);
}
.timeline-track-ticker:hover .needle,
.timeline-track-ticker:active .needle {
    opacity: 0.8;
}
.timeline-track-ticker:hover .orb,
.timeline-track-ticker:active .orb {
    outline: none;
    width: 48px;
    max-width: 48px;
    min-height: 28px;
    margin-top: -2px;
}
.timeline-track-ticker:hover .orb span,
.timeline-track-ticker:active .orb span {
    opacity: 1;
    color: rgba(var(--primary), 1);
}
.timeline-time-tick {
    height: 24px;
    display: flex;
    position: absolute;
}
.timeline-time-tick > div {
    flex-direction: column;
    flex: 1;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    padding: 5px 0;
    display: flex;
    position: relative;
    transform: translate(-50%);
}
.timeline-time-tick > div span {
    color: rgba(var(--primary), 0.36);
    text-align: center;
    margin: 0 auto;
    font-weight: 500;
    position: absolute;
    top: -4px;
    left: 0;
    right: 0;
}
.timeline-time-tick > div .dot {
    background: rgba(var(--primary), 0.36);
    border-radius: 50%;
    width: 2.5px;
    height: 2.5px;
}
.timeline-time-tick > div .line {
    background: rgba(var(--primary), 0.36);
    border-radius: 2px;
    width: 1.5px;
    height: 8px;
}
.video-trimmer-popover {
    background:
        linear-gradient(transparent 0%, rgba(var(--background), 1) 80%),
        linear-gradient(transparent 0%, rgba(var(--background), 1) 80%),
        linear-gradient(transparent 0%, rgba(var(--background), 1) 80%);
    box-shadow: none;
    margin: 0 auto;
    border-radius: 0 !important;
    width: 100vw !important;
    left: 0 !important;
    right: 0 !important;
}
.video-trimmer-popover:after {
    border: none;
}
.video-trimmer-popover .content {
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 100px 32px 32px;
    display: flex;
    position: relative;
}
.video-trimmer {
    border-radius: 10px;
    width: 100%;
    height: 48px;
    position: relative;
    overflow: hidden;
}
.video-trimmer:after {
    content: '';
    outline: solid 1px rgba(var(--primary), 0.12);
    outline-offset: -1px;
    border-radius: inherit;
    pointer-events: none;
    position: absolute;
    inset: 0;
}
.video-trimmer .trimmer {
    top: 0;
    left: 0;
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.video-trimmer .trimmer .trimmer-inner {
    width: 100%;
    height: 100%;
    position: relative;
}
.video-trimmer .trimmer .trimmer-inner .handler {
    border: 1px solid rgba(var(--primary), 1);
    border-radius: 8px;
    position: absolute;
    top: 0;
    bottom: 0;
}
.video-trimmer .trimmer .trimmer-inner .handler .handler-inner {
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
}
.video-trimmer .trimmer .trimmer-inner .handler .handler-inner .move-handler {
    cursor: move;
    flex: 1;
    width: 10px;
    height: 100%;
    left: 0;
}
.video-trimmer .trimmer .trimmer-inner .handler .handler-inner .left-trim {
    background: rgba(var(--primary), 1);
    cursor: ew-resize;
    background: red;
    border-radius: 8px 0 0 8px;
    width: 10px;
    height: 100%;
}
.video-trimmer .trimmer .trimmer-inner .handler .handler-inner .right-trim {
    background: rgba(var(--primary), 1);
    cursor: ew-resize;
    border-radius: 0 8px 8px 0;
    width: 10px;
    height: 100%;
}
.animation-options-popover {
    flex-direction: column;
    padding: 6px;
    display: flex;
}
.animation-options-popover button {
    justify-content: flex-start !important;
}
.animation-options-popover button .keys {
    color: rgba(var(--primary), 0.36);
    letter-spacing: -0.2px;
    margin-left: auto;
    font:
        12.5px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .animation-options-popover button .keys {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.templates-menu-view,
.template-preview-view {
    margin-top: 8px;
    margin-left: 8px;
    overflow: hidden;
    background: rgba(var(--sheet), 1) !important;
    border-radius: 22px !important;
}
.template-preview-view {
    flex-direction: column;
    justify-content: center;
    display: flex;
}
.template-preview-view .backdrop {
    z-index: 0;
    position: absolute;
    inset: 0;
}
.template-menu-navbar {
    z-index: 10;
    background:
        linear-gradient(rgba(var(--sheet), 1), transparent),
        linear-gradient(rgba(var(--sheet), 1), transparent);
    justify-content: space-between;
    align-items: flex-start;
    padding: 16px 16px 72px 24px;
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}
.template-menu-type-filters {
    z-index: 12;
    flex-wrap: wrap;
    gap: 4px;
    width: max-content;
    display: flex;
    position: absolute;
    top: 64px;
    left: 24px;
}
.template-menu-filters {
    flex-wrap: wrap;
    gap: 12px;
    padding: 0 24px;
    display: flex;
}
.template-menu-filters .template-filter {
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    display: flex;
}
.template-menu-filters .template-filter .icon {
    background: rgba(var(--secondary), 1);
    border-radius: 50%;
    padding: 12px;
}
.template-menu-filters .template-filter .icon svg {
    width: 28px;
    height: 28px;
}
@media only screen and (width>=1200px) {
    .template-menu-filters .template-filter:hover .icon {
        background: rgba(var(--primary), 0.06);
    }
}
.template-menu-filters .template-filter.is-active .icon {
    background: rgba(var(--primary), 1);
}
.template-menu-filters .template-filter.is-active .icon svg {
    color: rgba(var(--secondary), 1);
}
.template-section {
    flex-direction: column;
    display: flex;
    position: relative;
}
.template-section:before {
    content: '';
    inset: 0;
    left: unset;
    background: linear-gradient(90deg, transparent, rgba(var(--sheet), 1));
    z-index: 2;
    pointer-events: none;
    width: 48px;
    position: absolute;
}
.template-section.grid-view:before {
    display: none !important;
}
.template-section .h-stack .content {
    padding: 12px 24px;
}
.template-section .head {
    z-index: 2;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    display: flex;
}
.template-section .head .copy {
    gap: 2px;
}
.template-section .head button {
    background: rgba(var(--primary), 0.12);
}
@media only screen and (width>=0) and (width<=800px) {
    .template-section button {
        visibility: hidden;
    }
}
.template-item {
    z-index: 1;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    gap: 9px;
    display: flex;
}
.template-item .preview-wrapper {
    flex: 1;
    place-items: center;
    display: grid;
}
.template-item .display-wrapper {
    background: rgba(var(--primary), 0.12);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}
.template-item .display-wrapper:after {
    content: '';
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: inherit;
    pointer-events: none;
    position: absolute;
    inset: 0;
}
.template-item .display-wrapper .media-tag {
    z-index: 1;
    filter: drop-shadow(0 0 5px #00000080);
    position: absolute;
    bottom: 8px;
    left: 8px;
}
.template-item .display-wrapper .media-tag svg {
    color: #fff;
    width: 20px;
    height: 20px;
}
.template-item .display-wrapper .media-tag.is-playing {
    animation: 0.7s ease-in-out infinite alternate heartbeat;
}
@keyframes heartbeat {
    to {
        opacity: 0.5;
        transform: scale(0.9);
    }
}
.template-item .display-wrapper .animated-template-info {
    flex-direction: column;
    align-items: center;
    gap: 16px;
    margin: 0 auto;
    display: flex;
    position: absolute;
    bottom: -68px;
    left: 0;
    right: 0;
}
.template-item .display-wrapper .animated-template-info .video-duration {
    background: rgba(var(--primary), 0.12);
    border-radius: 2px;
    width: 130px;
    height: 4px;
}
.template-item .display-wrapper .animated-template-info .video-duration .progress {
    background: rgba(var(--primary), 1);
    border-radius: inherit;
    height: 100%;
}
.template-item .display-wrapper .animated-template-info .playback-control {
    transform: scale(0.8);
}
.template-item .copy {
    text-align: center;
    flex-direction: column;
    order: 1;
    gap: 3px;
    display: flex;
}
.template-item.big-preview {
    cursor: default;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
    height: 100%;
    padding: 32px;
}
.template-item.big-preview .admin-controls {
    gap: 8px;
    display: flex;
}
.template-item.big-preview .copy {
    order: unset;
    width: 100%;
}
.template-item.big-preview .copy .caption2 {
    letter-spacing: -0.4px;
    font:
        500 19px/26px Inter,
        sans-serif;
    color: rgba(var(--primary), 1) !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .template-item.big-preview .copy .caption2 {
        letter-spacing: -0.5px;
        font:
            500 19.5px/25px Inter,
            sans-serif;
    }
}
.template-item.big-preview .copy .tags {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
    max-width: 80%;
    margin: 16px auto 0;
    display: flex;
}
.template-item.big-preview .copy .tags div {
    color: rgba(var(--primary), 0.6);
    text-transform: capitalize;
    letter-spacing: -0.2px;
    align-items: center;
    gap: 4px;
    margin-top: -8px;
    font:
        12.5px/20px Inter,
        sans-serif;
    display: flex;
}
@media only screen and (width>=0) and (width<=800px) {
    .template-item.big-preview .copy .tags div {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.template-item.big-preview .copy .tags div svg {
    width: 16px;
    height: 16px;
}
.template-item.big-preview .display-wrapper {
    border-radius: 16px;
}
.template-item.big-preview .display-wrapper > * {
    cursor: default;
}
.template-item.big-preview > button {
    margin-bottom: 8px;
}
.template-item.big-preview .use-template-button-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    display: flex;
}
.template-item.big-preview .use-template-button-wrapper > span {
    text-align: center;
}
.save-template-modal .groups-list {
    flex-wrap: wrap;
    gap: 8px;
    display: flex;
}
.template-groups-edit-modal .groups {
    flex-direction: column;
    gap: 8px;
    display: flex;
}
.template-groups-edit-modal .groups .group-item {
    background: rgba(var(--primary), 0.06);
    border-radius: 12px;
    align-items: center;
    gap: 8px;
    padding: 8px 8px 8px 12px;
    display: flex;
}
.template-groups-edit-modal .groups .group-item > span {
    flex: 1;
}
.release-notes-modal .head .h1 {
    width: max-content;
    font:
        500 44px/44px Delicious Handrawn,
        sans-serif;
    position: relative;
}
.release-notes-modal .head p {
    letter-spacing: -0.4px;
    max-width: 90%;
    margin-top: 12px;
    font:
        500 14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .head p {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.release-notes-modal .release .h5 {
    padding-left: 24px;
    position: relative;
}
.release-notes-modal .release .h5:before,
.release-notes-modal .release .h5:after {
    content: '';
    border-radius: 50%;
    position: absolute;
}
.release-notes-modal .release .h5:before {
    background: rgba(var(--primary), 1);
    width: 12px;
    height: 12px;
    top: 6px;
    left: 0;
}
.release-notes-modal .release .h5:after {
    background: rgba(var(--background), 0.8);
    width: 8px;
    height: 8px;
    top: 8px;
    left: 2px;
}
.release-notes-modal .release .content {
    height: max-content;
    padding: 8px 8px 8px 24px;
    position: relative;
}
.release-notes-modal .release .content .cards {
    flex-flow: wrap;
    gap: 10px;
    display: flex;
}
.release-notes-modal .release .content:before {
    content: '';
    background: linear-gradient(rgba(var(--primary), 1) 0%, transparent 100%);
    border-radius: 2px;
    width: 2px;
    position: absolute;
    top: 4px;
    bottom: 4px;
    left: 5px;
}
.release-notes-modal .update-card {
    aspect-ratio: 1;
    background: #28282b;
    border-radius: 18px;
    flex-basis: 100%;
    display: flex;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 8px #00000029;
}
.release-notes-modal .update-card:after {
    content: '';
    z-index: 3;
    border: solid 1px rgba(var(--primary), 0.08);
    border-radius: inherit;
    position: absolute;
    inset: 0;
}
.release-notes-modal .update-card .text-layer {
    z-index: 2;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 16px;
    display: flex;
}
.release-notes-modal .update-card .text-layer p {
    color: #eee;
    font-weight: 500 !important;
}
.release-notes-modal .update-card .text-layer.light p {
    color: #000;
    font-weight: 550 !important;
}
.release-notes-modal .update-card .text-layer.caption {
    background: #000;
    border-radius: 8px;
    width: max-content;
    height: max-content;
    padding: 3px 8px;
    position: absolute;
    bottom: 8px;
    left: 8px;
}
.release-notes-modal .update-card .text-layer.caption p {
    letter-spacing: -0.2px;
    font:
        14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .update-card .text-layer.caption p {
        letter-spacing: -0.07px;
        font:
            13px/18px Inter,
            sans-serif;
    }
}
.release-notes-modal .update-card .text-layer.overlay {
    -webkit-backdrop-filter: blur(50px) saturate(120%);
    backdrop-filter: blur(50px) saturate(120%);
}
.release-notes-modal .update-card .media {
    z-index: 0;
    position: absolute;
    inset: 0;
}
.release-notes-modal .update-card .media img,
.release-notes-modal .update-card .media video {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.release-notes-modal .update-card.xs {
    height: max-content;
    aspect-ratio: unset;
}
.release-notes-modal .update-card.xs p {
    letter-spacing: -0.3px;
    font:
        16px/22px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .update-card.xs p {
        letter-spacing: -0.2px;
        font:
            17px/22px Inter,
            sans-serif;
    }
}
.release-notes-modal .update-card.sm {
    flex-basis: calc(50% - 5px);
}
.release-notes-modal .update-card.sm p {
    letter-spacing: -0.3px;
    font:
        16px/22px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .update-card.sm p {
        letter-spacing: -0.2px;
        font:
            17px/22px Inter,
            sans-serif;
    }
}
.release-notes-modal .update-card.tl {
    aspect-ratio: 1/2;
    flex-basis: calc(50% - 5px);
}
.release-notes-modal .update-card.tl p {
    letter-spacing: -0.4px;
    font:
        500 19px/26px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .update-card.tl p {
        letter-spacing: -0.5px;
        font:
            500 19.5px/25px Inter,
            sans-serif;
    }
}
.release-notes-modal .update-card.md {
    aspect-ratio: 2;
}
.release-notes-modal .update-card.md p {
    letter-spacing: -0.4px;
    font:
        500 19px/26px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .update-card.md p {
        letter-spacing: -0.5px;
        font:
            500 19.5px/25px Inter,
            sans-serif;
    }
}
.release-notes-modal .update-card.lg p {
    letter-spacing: -0.8px;
    font:
        500 23px/32px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .update-card.lg p {
        letter-spacing: -0.75px;
        font:
            500 21.5px/28px Inter,
            sans-serif;
    }
}
.release-notes-modal .update-card.small p {
    letter-spacing: -0.3px;
    font:
        16px/22px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .update-card.small p {
        letter-spacing: -0.2px;
        font:
            17px/22px Inter,
            sans-serif;
    }
}
.release-notes-modal .update-card.def p {
    letter-spacing: -0.4px;
    font:
        500 19px/26px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .update-card.def p {
        letter-spacing: -0.5px;
        font:
            500 19.5px/25px Inter,
            sans-serif;
    }
}
.release-notes-modal .update-card.big p {
    letter-spacing: -0.8px;
    font:
        500 23px/32px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .release-notes-modal .update-card.big p {
        letter-spacing: -0.75px;
        font:
            500 21.5px/28px Inter,
            sans-serif;
    }
}
.upgrade-flow {
    outline: solid 1px rgba(var(--primary), 0.06);
    outline-offset: -1px;
    border-radius: 24px;
    height: 100%;
    position: relative;
    overflow: hidden;
}
.upgrade-flow-steps {
    inset: 0;
    bottom: unset;
    z-index: 1;
    pointer-events: none;
    align-items: flex-start;
    gap: 20px;
    padding: 28px 64px 16px;
    display: flex;
    position: absolute;
}
@media only screen and (width>=0) and (width<=800px) {
    .upgrade-flow-steps {
        padding: calc(16px + env(safe-area-inset-top, 16px)) 16px 16px !important;
    }
}
.upgrade-flow-steps .step {
    text-align: center;
    flex-direction: column;
    flex: 1;
    align-items: center;
    gap: 2px;
    display: flex;
    overflow: hidden;
}
.upgrade-flow-steps .step:before {
    content: '';
    background: rgba(var(--primary), 0.06);
    border-radius: 8px;
    width: 100%;
    height: 3px;
    margin-bottom: 8px;
}
.upgrade-flow-steps .step span {
    color: rgba(var(--primary), 0.36);
    letter-spacing: -0.2px;
    font:
        12.5px/20px Inter,
        sans-serif;
    display: none;
    font-weight: 500 !important;
}
@media only screen and (width>=0) and (width<=800px) {
    .upgrade-flow-steps .step span {
        letter-spacing: 0;
        font:
            12px/16px Inter,
            sans-serif;
    }
}
.upgrade-flow-steps .step p {
    color: rgba(var(--primary), 0.36);
    text-transform: capitalize;
    letter-spacing: -0.6px;
    font:
        500 17px/24px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .upgrade-flow-steps .step p {
        letter-spacing: -0.35px;
        font:
            500 17px/22px Inter,
            sans-serif;
    }
}
.upgrade-flow-steps .step.is-active:before {
    background: rgba(var(--primary), 1);
}
.upgrade-flow-steps .step.is-active p {
    color: rgba(var(--primary), 1);
}
.upgrade-flow-steps .step.is-active span {
    display: initial;
}
.upgrade-flow-steps .step.is-done:before {
    background: rgba(var(--primary), 1);
}
.upgrade-flow-steps .step.is-done p {
    color: rgba(var(--primary), 1);
}
.upgrade-flow-steps .step.is-done span {
    display: initial;
    color: rgba(var(--primary), 1);
}
.stripe-component {
    z-index: 2;
    background: #000;
    position: absolute;
    inset: 0;
}
.stripe-component .backdrop {
    z-index: -1;
    border-radius: inherit;
    background: linear-gradient(
        80.18deg,
        #d1ccdd 0%,
        #c893e1 25%,
        #eb47a7 50%,
        #f94a73 75%,
        #fb7a53 100%
    );
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.stripe-component .backdrop:after {
    content: '';
    background:
        linear-gradient(rgba(var(--background), 1) 30%, transparent 100%),
        linear-gradient(rgba(var(--background), 1) 30%, transparent 100%);
    position: absolute;
    inset: 0;
}
.stripe-component:before {
    content: '';
    inset: 0;
    bottom: unset;
    background:
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%),
        linear-gradient(rgba(var(--background), 1) 0%, transparent 100%);
    z-index: 10;
    height: 140px;
    position: absolute;
}
.stripe-component .stripe-component-loading {
    justify-content: center;
    align-items: center;
    display: flex;
    position: absolute;
    inset: 0;
}
.stripe-component .stripe-checkout {
    border-radius: 16px;
    overflow: hidden;
}
.stripe-component .upgrade-result-content {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 32px;
    width: 100%;
    height: 100%;
    display: flex;
}
.stripe-component .upgrade-result-content .copy {
    text-align: center;
    flex-direction: column;
    gap: 12px;
    display: flex;
}
.stripe-component .upgrade-result-content .copy p {
    color: rgba(var(--primary), 0.6);
}
.upgrade-modal {
    height: 100%;
}
.upgrade-modal .backdrop {
    z-index: -1;
    border-radius: inherit;
    background: linear-gradient(
        80.18deg,
        #d1ccdd 0%,
        #c893e1 25%,
        #eb47a7 50%,
        #f94a73 75%,
        #fb7a53 100%
    );
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.upgrade-modal .backdrop:after {
    content: '';
    background:
        linear-gradient(rgba(var(--background), 1) 30%, transparent 100%),
        linear-gradient(rgba(var(--background), 1) 30%, transparent 100%);
    position: absolute;
    inset: 0;
}
.upgrade-modal .header {
    aspect-ratio: 2;
    text-align: center;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    padding: 16px 16px 48px;
    display: flex;
    position: relative;
}
.upgrade-modal .header .cover {
    z-index: -1;
    position: absolute;
    inset: 0;
}
.upgrade-modal .header .cover video {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.upgrade-modal .header .cover:before {
    content: '';
    background:
        linear-gradient(0deg, rgba(var(--background), 1) 0%, transparent 50%),
        linear-gradient(0deg, rgba(var(--background), 1) 0%, transparent 50%);
    position: absolute;
    inset: 0;
}
@media only screen and (width>=0) and (width<=800px) {
    .upgrade-modal .header {
        aspect-ratio: 1;
    }
    .upgrade-modal .header > h2,
    .upgrade-modal .header > span {
        max-width: 70%;
    }
}
.upgrade-modal .perks-section {
    width: 100%;
    margin-top: -32px;
}
.upgrade-modal .perk-card {
    aspect-ratio: 2/3;
    background: #000;
    border-radius: 20px;
    flex-direction: column;
    gap: 8px;
    padding: 20px;
    display: flex;
    position: relative;
    overflow: hidden;
    width: 260px !important;
    min-width: 260px !important;
}
.upgrade-modal .perk-card:after {
    content: '';
    border: solid 1px rgba(var(--primary), 0.06);
    z-index: 1;
    border-radius: inherit;
    position: absolute;
    inset: 0;
}
.upgrade-modal .perk-card > :not(.cover) {
    z-index: 1;
}
.upgrade-modal .perk-card .cover {
    position: absolute;
    inset: 0;
}
.upgrade-modal .perk-card .cover img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.upgrade-modal .plans-head {
    flex-direction: column;
    align-items: center;
    gap: 16px;
    margin: 16px 0;
    display: flex;
}
.upgrade-modal .plans-head .tabs {
    background: rgba(var(--primary), 0.12);
    border-radius: 50px;
    gap: 2px;
    padding: 2px;
    display: flex;
}
.upgrade-modal .plans-head .tabs button {
    width: max-content;
    min-width: 100px;
}
.upgrade-modal .plans-head .tabs button.true-active {
    background: rgba(var(--primary), 1);
    color: rgba(var(--secondary), 1);
}
.upgrade-modal .all-plans {
    justify-content: center;
    gap: 16px;
    margin-bottom: 80px;
    display: flex;
}
@media only screen and (width>=0) and (width<=800px) {
    .upgrade-modal .all-plans {
        gap: 0;
        width: 100vw;
    }
    .upgrade-modal .all-plans .plan-section {
        width: 100%;
        overflow: hidden;
    }
    .upgrade-modal .all-plans .plan-section > button {
        width: 100% !important;
    }
}
.upgrade-modal .all-plans .plan-section {
    flex-direction: column;
    align-items: center;
    gap: 20px;
    width: 240px;
    padding: 16px;
    display: flex;
}
.upgrade-modal .all-plans .plan-section .details {
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    display: flex;
}
.upgrade-modal .all-plans .plan-section .details p {
    align-items: center;
    gap: 4px;
    display: flex;
}
.upgrade-modal .all-plans .plan-section > button {
    width: 180px;
}
.upgrade-modal .all-plans .plan-section .h-divider {
    min-width: 80%;
    max-width: 80%;
}
.upgrade-modal .all-plans .plan-section .features {
    flex-direction: column;
    gap: 40px;
    margin-top: 20px;
    display: flex;
}
.upgrade-modal .all-plans .plan-section .features .feature-item {
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    display: flex;
    position: relative;
}
.upgrade-modal .all-plans .plan-section .features .feature-item .icon {
    align-items: center;
    gap: 4px;
    display: flex;
}
.upgrade-modal .all-plans .plan-section .features .feature-item .icon svg {
    width: 32px;
    height: 32px;
}
.upgrade-modal .all-plans .plan-section .features .feature-item.none-item * {
    visibility: hidden;
}
.upgrade-modal .all-plans .plan-section .features .feature-item.none-item:before {
    content: '';
    visibility: visible;
    background: rgba(var(--primary), 1);
    width: 26px;
    height: 2px;
    position: absolute;
    top: 50%;
}
.upgrade-modal .bottom {
    z-index: 1;
    background: #373636;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 64px 16px 16px;
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}
.upgrade-modal .bottom .v-stack {
    flex-direction: column;
    gap: 12px;
    width: 280px;
    display: flex;
}
.upgrade-modal .bottom .v-stack > * {
    width: 100%;
}
.upgrade-modal .bottom .v-stack > p {
    text-align: center;
}
.upgrade-modal .bottom .switch-component {
    background: rgba(var(--background), 0.8);
}
.upgrade-modal .bottom .renew-plan {
    justify-content: space-between;
    align-items: center;
    display: flex;
}
.upgrade-modal .bottom .durations {
    gap: 10px;
    display: flex;
}
.upgrade-modal .bottom .durations.pass-durations {
    flex-direction: column;
}
.upgrade-modal .bottom .durations .plan-btn {
    background: rgba(var(--background), 0.8);
    border-radius: 14px;
    flex-direction: column;
    flex: 1;
    gap: 4px;
    padding: 8px;
    position: relative;
    overflow: visible;
}
.upgrade-modal .bottom .durations .plan-btn .plan-price {
    align-items: center;
    gap: 4px;
    display: flex;
}
.upgrade-modal .bottom .durations .plan-btn.is-active {
    outline: solid 2px rgba(var(--primary), 1);
    outline-offset: 2px;
}
.upgrade-modal .bottom .durations .plan-btn .tag {
    z-index: 1;
    position: absolute;
    top: -10px;
    right: -12px;
    transform: rotate(8deg);
}
@media only screen and (width>=0) and (width<=800px) {
    .upgrade-modal .bottom {
        padding: 48px 20px 20px;
    }
    .upgrade-modal .bottom .durations,
    .upgrade-modal .bottom .submit-button {
        width: 100%;
    }
}
.frame-item {
    border-radius: 10px;
    flex-direction: column;
    gap: 0;
    padding: 10px;
    display: flex;
    position: relative;
}
.frame-item .icon-wrapper {
    aspect-ratio: 4/5;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 10px;
    display: flex;
}
.frame-item .icon-wrapper .frame-icon-display {
    width: 100%;
    height: 100%;
    position: relative;
}
.frame-item .icon-wrapper .frame-icon-display .frame-item-icon {
    background: rgba(var(--primary), 0.06);
    border: solid 1.5px rgba(var(--primary), 0.36);
    border-radius: 8px;
    justify-content: center;
    align-items: center;
    max-height: 100%;
    margin: auto;
    transition: all 0.2s;
    display: flex;
    position: absolute;
    inset: 0;
}
.frame-item .icon-wrapper .frame-icon-display .frame-item-icon img {
    width: 20px;
    height: 20px;
}
.frame-item .details {
    flex-direction: column;
    gap: 2px;
    display: flex;
}
.frame-item.active {
    background: rgba(var(--primary), 0.06);
    outline-offset: 0px;
}
.frame-picker-desktop-dropdown .panel-selector-btn-desktop .preview {
    min-width: 40px;
    height: 40px;
    padding: 4px;
}
.frame-picker-desktop-dropdown .panel-selector-btn-desktop .frame-preview {
    width: 100%;
    height: 100%;
    position: relative;
}
.frame-picker-desktop-dropdown .panel-selector-btn-desktop .frame-preview .current-frame-icon {
    background: rgba(var(--primary), 0.36);
    border: solid 1px rgba(var(--primary), 1);
    border-radius: 3px;
    max-height: 100%;
    margin: auto;
    transition: all 0.2s;
    position: absolute;
    inset: 0;
}
.frame-picker-desktop-dropdown .drop-menu {
    height: 70vh;
    max-height: 70vh;
    padding: 0;
    overflow-y: scroll;
}
.frame-picker-desktop-dropdown .drop-menu .custom-frame {
    border-bottom: solid 1px rgba(var(--primary), 0.08);
    padding: 12px;
    position: relative;
}
.frame-picker-desktop-dropdown .drop-menu .frame-platform-presets {
    padding: 12px;
}
.frame-picker-desktop-dropdown .drop-menu .frame-platform-presets:not(:last-child) {
    border-bottom: solid 1px rgba(var(--primary), 0.08);
}
.frame-picker-desktop-dropdown .drop-menu .frame-platform-presets .section-head {
    align-items: center;
    gap: 8px;
    padding: 8px;
    display: flex;
}
.frame-picker-desktop-dropdown .drop-menu .frame-platform-presets .section-head h5 {
    text-transform: capitalize;
}
.frame-picker-desktop-dropdown .drop-menu .frame-platform-presets .section-head img {
    width: 20px;
    height: 20px;
}
.frame-picker-desktop-dropdown .drop-menu .frame-platform-presets .frames-grid {
    flex-flow: wrap;
    align-items: flex-end;
    display: flex;
}
.frame-picker-desktop-dropdown .drop-menu .frame-platform-presets .frames-grid .frame-item {
    flex-basis: 33.3%;
}
.frame-picker-desktop-dropdown .drop-menu .frame-platform-presets .frames-grid .frame-item:hover {
    background: rgba(var(--primary), 0.06);
    color: rgba(var(--primary), 1);
}
.frame-picker-desktop-dropdown
    .drop-menu
    .frame-platform-presets
    .frames-grid
    .frame-item:hover
    .frame-item-icon {
    border-color: rgba(var(--primary), 1);
}
.frame-picker-control-mobile .frame-platform-presets {
    flex-direction: column;
    gap: 12px;
    display: flex;
}
.frame-picker-control-mobile .frame-platform-presets:not(:last-child) .frames-grid:after {
    content: '';
    border-right: solid 1px rgba(var(--primary), 0.08);
    align-self: center;
    width: 8px;
    height: 100px;
}
.frame-picker-control-mobile .frame-platform-presets .section-head {
    justify-content: center;
    align-items: center;
    gap: 6px;
    display: flex;
}
.frame-picker-control-mobile .frame-platform-presets .section-head h5 {
    text-transform: capitalize;
    color: rgba(var(--primary), 0.6);
    letter-spacing: -0.4px;
    font:
        500 14px/20px Inter,
        sans-serif;
}
@media only screen and (width>=0) and (width<=800px) {
    .frame-picker-control-mobile .frame-platform-presets .section-head h5 {
        letter-spacing: 0;
        font:
            14.5px/20px Inter,
            sans-serif;
    }
}
.frame-picker-control-mobile .frame-platform-presets .section-head img {
    width: 18px;
    height: 18px;
}
.frame-picker-control-mobile .frame-platform-presets .frames-grid {
    display: flex;
}
.frame-picker-control-mobile .frame-platform-presets .frames-grid .frame-item {
    width: 84px;
    padding: 8px 6px;
}
.frame-picker-control-mobile .frame-platform-presets .frames-grid .frame-item .icon-wrapper {
    padding: 6px;
}
.frame-picker-control-mobile .frame-platform-presets .frames-grid .frame-item .icon-wrapper img {
    width: 16px;
    height: 16px;
}
.frame-picker-control-mobile .default-presets {
    align-self: flex-end;
}
.custom-frame form {
    gap: 8px;
    display: flex;
}
.custom-frame form .custom-frame-input {
    flex: 1;
    position: relative;
}
.custom-frame form .custom-frame-input h6 {
    width: max-content;
    color: rgba(var(--primary), 0.6);
    position: absolute;
    top: 10px;
    left: 10px;
}
.custom-frame form .custom-frame-input input {
    text-align: right;
    width: 100%;
}
.custom-frame form .custom-frame-input input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.custom-frame form .custom-frame-input input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.custom-frame form .custom-frame-input input[type='number'] {
    -moz-appearance: textfield;
}
.custom-frame form .search {
    align-items: center;
    gap: 6px;
    display: flex;
}
.custom-frame form .search input {
    flex-grow: 1;
    padding: 6px 8px;
}
.mock-item {
    background: rgba(var(--primary), 0.06);
    aspect-ratio: 2/3;
    cursor: pointer;
    border-radius: 16px;
    flex-direction: column;
    justify-content: center;
    width: calc(50cqw - 21px);
    display: flex;
    position: relative;
    overflow: hidden;
}
@media only screen and (width>=1200px) {
    .mock-item:hover {
        background: rgba(var(--primary), 0.08);
    }
}
.mock-item .preview {
    aspect-ratio: 5/4;
    position: relative;
}
.mock-item .preview img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.mock-item .details {
    flex-direction: column;
    justify-content: space-between;
    gap: 10px;
    padding: 12px;
    display: flex;
    position: absolute;
    inset: 0;
}
.mock-item .details .copy {
    text-align: left;
    flex-direction: column;
    gap: 4px;
    padding: 4px;
    display: flex;
}
.mock-item .details .copy .tag {
    position: absolute;
    right: 14px;
}
.mock-item .details .variants {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 4px;
    display: grid;
}
.mock-item .details .variants > div {
    aspect-ratio: 4/3;
    background: rgba(var(--primary), 0.06);
    border-radius: 6px;
    place-items: center;
    display: grid;
    overflow: hidden;
}
.mock-item .details .variants > div img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.mockup-picker-desktop-dropdown .drop-menu {
    height: 70vh;
    max-height: 70vh;
    padding: 0;
    overflow: hidden;
}
.mockup-picker-desktop-dropdown .drop-menu .mockups-list {
    flex-direction: column;
    height: 100%;
    padding-top: 44px;
    display: flex;
    overflow-y: auto;
}
.mockup-picker-desktop-dropdown .drop-menu .mockups-list .h-stack .content {
    padding: 12px 16px;
}
.mockup-picker-desktop-dropdown .drop-menu .mockups-list .mock-picker-section {
    flex-direction: column;
    display: flex;
}
.mockup-picker-desktop-dropdown .drop-menu .mockups-list .mock-picker-section .head {
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px 0;
    display: flex;
}
.mockup-picker-desktop-dropdown .drop-menu .picker-filters {
    z-index: 1;
    background:
        linear-gradient(rgba(var(--modal), 1), transparent),
        linear-gradient(rgba(var(--modal), 1), transparent),
        linear-gradient(rgba(var(--modal), 1), transparent);
    padding: 10px 10px 20px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}
.mockup-picker-desktop-dropdown .drop-menu .picker-filters .buttons-wrapper {
    gap: 2px;
    display: flex;
}
.mockup-picker-desktop-dropdown .drop-menu .picker-filters .buttons-wrapper button {
    width: max-content;
    min-width: 64px;
}
.mockup-picker-mobile-modal .picker-filters {
    border-bottom: solid 1px rgba(var(--primary), 0.06);
    width: calc(100% + 32px);
    margin: -8px -16px 0;
    padding-bottom: 12px;
    position: relative;
    overflow-x: auto;
}
.mockup-picker-mobile-modal .picker-filters .buttons-wrapper {
    width: max-content;
    padding: 0 16px;
    display: flex;
}
.mockup-picker-mobile-modal .picker-filters .buttons-wrapper button {
    border-radius: 50px;
    gap: 4px;
    width: max-content;
    min-width: 64px;
}
.mockup-picker-mobile-modal .picker-filters .buttons-wrapper button svg {
    width: 20px;
    height: 20px;
}
.mockup-picker-mobile-modal .mockups-list {
    flex-direction: column;
    height: 100%;
    display: flex;
}
.mockup-picker-mobile-modal .mockups-list .mock-picker-section {
    flex-direction: column;
    display: flex;
}
.mockup-picker-mobile-modal .mockups-list .mock-picker-section .head {
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    display: flex;
}
.mockup-picker-mobile-modal .mockups-list .mock-picker-section .head button {
    visibility: hidden !important;
}
.mockup-picker-mobile-modal .h-stack {
    width: calc(100% + 32px);
    margin: -8px -16px 8px;
}
.mockup-picker-mobile-modal .mock-item .details {
    padding: 14px;
}
.mockup-switch-alert-graphic {
    align-items: center;
    gap: 8px;
    display: flex;
}
.mockup-switch-alert-graphic svg {
    color: rgba(var(--primary), 0.5);
}
.mockup-switch-alert-graphic > svg {
    width: 20px;
    height: 20px;
}
.mockup-switch-alert-graphic .current svg,
.mockup-switch-alert-graphic .final svg {
    width: 40px;
    height: 40px;
}
.app-menu {
    box-shadow: none;
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
    background: 0 0 !important;
    border-radius: 0 !important;
    height: 100vh !important;
    max-height: 100vh !important;
}
.app-menu .app-menu-title {
    align-items: center;
    gap: 8px;
    margin-top: -40px;
    display: flex;
}
.app-menu .app-menu-title .logo {
    pointer-events: none;
    width: auto;
    height: 36px;
}
@media only screen and (width>=0) and (width<=800px) {
    .app-menu .app-menu-title {
        top: 6px;
    }
}
.app-menu .h-divider {
    background: rgba(var(--primary), 0.12);
    min-width: calc(100% - 16px);
    max-width: calc(100% - 16px);
    margin: 0 6px;
}
@media only screen and (width>=1200px) {
    .app-menu .app-menu-card-view {
        padding: 8px;
    }
    .app-menu .app-menu-card-view > div {
        border-radius: 22px;
    }
}
@media only screen and (width>=800px) and (width<=1200px) {
    .app-menu .app-menu-card-view {
        padding: 8px;
    }
    .app-menu .app-menu-card-view > div {
        border-radius: 22px;
    }
}
@media only screen and (width>=0) and (width<=800px) {
    .app-menu,
    .app-menu > .modal-view {
        width: 100vw !important;
    }
}
.app-menu .modal-title-bar {
    background: rgba(var(--body), 0.6) !important;
}
.app-menu > .modal-view > .modal-scroll-view .content-view {
    padding-left: 24px;
    padding-right: 24px;
}
.app-menu .account-card-avatar {
    aspect-ratio: 1;
    background: rgba(var(--primary), 0.12);
    border-radius: 50%;
    width: 48px;
    overflow: hidden;
}
.app-menu .account-card-avatar img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.app-menu .app-menu-theme {
    gap: 12px;
    width: 100%;
    display: flex;
}
.app-menu .app-menu-theme .theme-item {
    text-align: center;
    cursor: pointer;
    flex-direction: column;
    flex: 1;
    align-items: center;
    gap: 12px;
    padding-top: 6px;
    display: flex;
}
.app-menu .app-menu-theme .theme-item .thumbnail {
    outline: solid 1px rgba(var(--primary), 0.12);
    outline-offset: -1px;
    background: rgba(var(--primary), 0.06);
    border-radius: 12px;
    width: 100%;
    position: relative;
    overflow: hidden;
}
.app-menu .app-menu-theme .theme-item .thumbnail .image {
    background: rgba(var(--primary), 0.06);
    width: 100%;
    height: 100%;
}
.app-menu .app-menu-theme .theme-item .thumbnail .image img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.app-menu .app-menu-theme .theme-item .radio {
    border: solid 1px rgba(var(--primary), 0.12);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    padding: 5px;
}
.app-menu .app-menu-theme .theme-item .radio svg {
    width: 100%;
    height: 100%;
}
.app-menu .app-menu-theme .theme-item .radio.is-active {
    background: rgba(var(--primary), 1);
    border-color: #0000;
}
.app-menu .app-menu-theme .theme-item .radio.is-active svg {
    color: rgba(var(--secondary), 1);
}
@media only screen and (width>=0) and (width<=800px) {
    .app-menu .app-menu-theme {
        justify-content: space-evenly;
    }
    .app-menu .app-menu-theme .theme-item {
        flex: unset;
        width: 72px;
    }
    .app-menu .app-menu-theme .theme-item .thumbnail {
        aspect-ratio: 393/852;
        border-radius: 10px;
    }
}
@media only screen and (width>=800px) {
    .app-menu .app-menu-theme {
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 40px;
        width: 100%;
        height: calc(100vh - 120px);
    }
    .app-menu .app-menu-theme .theme-item {
        flex: unset;
        flex-direction: row-reverse;
        justify-content: space-between;
        width: 100%;
    }
    .app-menu .app-menu-theme .theme-item .thumbnail {
        aspect-ratio: 16/10;
        width: 180px;
    }
    .app-menu .app-menu-theme .theme-item > span {
        text-align: left;
        flex: 1;
    }
}
.app-menu-links {
    flex-flow: wrap;
    gap: 16px;
    display: flex;
    container-type: inline-size;
}
.link-card {
    background: rgba(var(--panel), 1);
    cursor: pointer;
    border-radius: 22px;
    flex-direction: column;
    justify-content: space-between;
    gap: 12px;
    padding: 16px;
    display: flex;
    position: relative;
    overflow: hidden;
}
.link-card.is-disabled {
    cursor: default;
    pointer-events: none;
}
.link-card.tiny-size,
.link-card.small-size {
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}
.link-card.tiny-size .external-link,
.link-card.small-size .external-link {
    top: calc(50% - 8px);
    right: 16px;
}
.link-card.tiny-size {
    flex-basis: calc(50cqw - 8px);
    height: calc(25cqw - 12px);
}
.link-card.small-size {
    flex-basis: 100%;
    height: calc(25cqw - 12px);
}
.link-card.medium-size {
    flex-basis: calc(50cqw - 8px);
    height: calc(50cqw - 8px);
}
.link-card.large-size {
    flex-basis: 100%;
    height: calc(50cqw - 8px);
}
.link-card.huge-size {
    flex-basis: 100%;
    height: calc(75cqw - 4px);
}
.link-card .icon {
    z-index: 1;
    width: max-content;
}
.link-card .icon svg {
    width: 24px;
    height: 24px;
}
.link-card .texts {
    z-index: 1;
    flex-direction: column;
    gap: 4px;
    display: flex;
}
.link-card .external-link {
    z-index: 1;
    position: absolute;
    top: 16px;
    right: 16px;
}
.link-card .external-link svg {
    width: 18px;
    height: 18px;
    color: rgba(var(--primary), 0.36);
}
.link-card .media {
    z-index: 0;
    border-radius: inherit;
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.link-card .media img,
.link-card .media video {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.link-card .media:after {
    content: '';
    background: linear-gradient(20deg, rgba(var(--panel), 1) 10%, transparent 60%);
    z-index: 1;
    position: absolute;
    inset: 0;
}
.app-menu-mobile-cta {
    flex-direction: column;
    justify-content: center;
    height: 100%;
    display: flex;
}
.app-menu-mobile-cta .media {
    aspect-ratio: 3/4;
    position: relative;
}
.app-menu-mobile-cta .media img {
    object-fit: cover;
    object-position: bottom;
    filter: brightness(110%);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}
.app-menu-mobile-cta .media:after {
    content: '';
    background: linear-gradient(0deg, transparent 70%, rgba(var(--body), 1) 100%);
    position: absolute;
    inset: 0;
}
.app-menu-mobile-cta .details {
    z-index: 1;
    flex-direction: column;
    gap: 32px;
    padding: 0 32px 48px;
    display: flex;
}
.app-menu-mobile-cta .details .texts {
    flex-direction: column;
    gap: 8px;
    display: flex;
}
.app-menu-mobile-cta .details > .texts {
    text-align: center;
}
.app-menu-mobile-cta .details .scan-to-get {
    justify-content: center;
    align-items: center;
    gap: 24px;
    display: flex;
}
.app-menu-mobile-cta .details .scan-to-get .qr-code {
    aspect-ratio: 1;
    background: rgba(var(--primary), 1);
    border-radius: 16px;
    width: 88px;
    overflow: hidden;
}
.app-menu-mobile-cta .details .scan-to-get .qr-code svg {
    color: rgba(var(--secondary), 1);
    width: 100%;
    height: 100%;
    scale: 0.95;
}
.sub-card-preview {
    aspect-ratio: 4/5;
    color: #fff;
    border-radius: 12px;
    width: 90px;
    padding: 10px;
    position: absolute;
    top: 24px;
    right: 36px;
    transform: rotate(-3deg);
}
.sub-card-preview .card-content {
    z-index: 1;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
}
.sub-card-preview .card-content .top {
    justify-content: space-between;
    align-items: flex-start;
    display: flex;
}
.sub-card-preview .card-content .top .shots-logo {
    width: 20px;
}
.sub-card-preview .card-content .bottom {
    flex-direction: column;
    gap: 10px;
    display: flex;
}
.sub-card-preview .card-content .bottom .plan-details {
    transform-origin: 0 100%;
    justify-content: space-between;
    align-items: flex-end;
    display: flex;
    transform: scale(0.9);
}
.sub-card-preview .card-content .bottom .plan-details > div {
    flex-direction: column;
    display: flex;
}
.sub-card-preview .sub-card-blur {
    display: none;
}
.shots-credits {
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 16px;
    display: flex;
}
.shots-credits .logo {
    height: 64px;
}
.shots-credits .app-stage-tag {
    margin-top: 8px;
}
.assets-library .assets-library-button {
    border-radius: 14px;
    justify-content: flex-start;
    width: 100%;
    height: 44px;
    padding: 0 20px;
    position: relative;
    overflow: hidden;
}
.assets-library .assets-library-button:hover {
    box-shadow: initial !important;
}
.assets-library .assets-library-button img {
    height: 50px;
    position: absolute;
    top: 5px;
    right: 20px;
}
.assets-library .drop-menu {
    height: 70vh;
    max-height: 70vh;
    padding: 0;
    overflow: hidden;
}
.assets-library .drop-menu .head {
    padding: 16px;
}
.assets-library .drop-menu .head button {
    width: 100%;
}
.assets-library .drop-menu .assets-stack {
    flex-direction: column;
    height: 100%;
    display: flex;
    position: relative;
    overflow-y: auto;
}
.assets-library .drop-menu .assets-stack .stack {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    padding: 16px 16px 80px;
    display: grid;
}
.assets-library .drop-menu:after {
    content: '';
    inset: 0;
    top: unset;
    background: linear-gradient(transparent 0%, rgba(var(--modal), 1) 100%);
    height: 60px;
    position: absolute;
}
.asset-item {
    aspect-ratio: 1;
    background: rgba(var(--primary), 0.06);
    width: 100%;
    position: relative;
    overflow: hidden;
}
.asset-item img {
    object-fit: contain;
    width: 100%;
    height: 100%;
}
.asset-item .cancel-button {
    z-index: 1;
    position: absolute;
    top: 0;
    right: 0;
}
.devices {
    --aspect-ratio: 1570/2932;
    --shadow-inset: 1%;
    --shadow-radius: 6em;
    --drop-padding: 0%;
    --drop-radius: 0;
    --device-asset-scale: 1.248;
    pointer-events: all;
    justify-content: center;
    align-items: center;
    display: flex;
}
.devices.imac-24,
.devices.imac-pro,
.devices.pro-display-xdr {
    --device-asset-scale: 2.248;
}
.devices.macbook-air-m2,
.devices.macbook-air-13,
.devices.macbook-pro-16 {
    --device-asset-scale: 1.49;
}
.devices.apple-watch-10-42mm,
.devices.apple-watch-10-46mm,
.devices.apple-watch-ultra {
    --device-asset-scale: 1.99;
}
.devices.phone-shadow-config {
    --shadow-inset: -1%;
}
.devices.tablet-shadow-config {
    --shadow-inset: -2.5%;
    --shadow-radius: 3em;
}
.devices.lay-2-items {
    aspect-ratio: calc(var(--aspect-ratio) * 2);
}
.devices.lay-3-items {
    aspect-ratio: calc(var(--aspect-ratio) * 3);
}
.devices .item {
    width: 100%;
    height: 100%;
    position: relative;
    container: display-item/size;
}
.devices .item .item-container {
    width: 100%;
    height: 100%;
}
@container display-item (width>0) {
    .devices .item .item-container {
        font-size: calc(0.666667cqw + 0.666667cqh);
    }
}
.devices .item .device-asset {
    z-index: 1;
    pointer-events: none;
    width: 100%;
    height: 100%;
    transform: scale(var(--device-asset-scale));
    position: absolute;
    top: 0;
    left: 0;
}
.devices .item .device-asset .device-asset-image {
    object-fit: contain;
    width: 100%;
    height: 100%;
}
.devices .item .drop-wrapper {
    width: 100%;
    height: 100%;
}
.devices .item .drop-wrapper .dropzone {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.devices .item .drop-wrapper .dropzone .dropped-image {
    max-width: 100%;
    max-height: 100%;
}
.devices .item .drop-wrapper .dropzone .empty-drop {
    height: 100%;
    max-width: 100% !important;
    max-height: 100% !important;
}
.devices .item .adaptive-shadow-layer {
    inset: var(--shadow-inset);
    z-index: -5;
    border-radius: var(--shadow-radius);
    position: absolute;
}
.devices .item .adaptive-shadow-layer > div {
    border-radius: inherit;
    pointer-events: none;
    position: absolute;
    inset: 0;
    overflow: hidden;
}
.devices .item .adaptive-shadow-layer > div:first-child {
    z-index: 1;
}
.devices .item .adaptive-shadow-layer > div:nth-child(2) {
    z-index: 2;
}
.devices .item .shadow {
    inset: var(--shadow-inset);
    z-index: -5;
    border-radius: var(--shadow-radius);
    position: absolute;
}
.devices .item .shadow .shadow-layer {
    border-radius: inherit;
    pointer-events: none;
    background: 0 0;
    position: absolute;
    inset: 0;
}
.image-resizer {
    background: rgba(var(--panel), 1);
    position: relative;
}
.image-resizer .drag-area {
    z-index: 1;
    position: absolute;
}
.image-resizer .image-wrapper {
    height: inherit;
    width: inherit;
}
.image-resizer .dropzone {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.image-resizer .dropzone .dropped-image {
    max-width: 100%;
    max-height: 100%;
}
.image-resizer .dropzone .empty-drop {
    height: 100%;
    max-width: 100% !important;
    max-height: 100% !important;
}
.global-resize-handle {
    z-index: 10;
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    position: absolute;
    inset: 0;
}
.global-resize-handle .handle-area {
    aspect-ratio: 1;
    width: 5em;
    position: absolute;
    cursor: default !important;
}
.global-resize-handle .handle-area.nw {
    transform-origin: 0 0;
    top: 0;
    left: 0;
}
.global-resize-handle .handle-area.nw .resize-handle {
    cursor: nwse-resize;
    top: 0;
    left: 0;
    transform: rotate(180deg);
}
.global-resize-handle .handle-area.nw .move-control {
    top: 2.5em;
    left: 2.5em;
}
.global-resize-handle .handle-area.ne {
    transform-origin: 100% 0;
    top: 0;
    right: 0;
}
.global-resize-handle .handle-area.ne .resize-handle {
    cursor: nesw-resize;
    top: 0;
    right: 0;
    transform: rotate(-90deg);
}
.global-resize-handle .handle-area.ne .move-control {
    top: 2.5em;
    right: 2.5em;
}
.global-resize-handle .handle-area.se {
    transform-origin: 100% 100%;
    bottom: 0;
    right: 0;
}
.global-resize-handle .handle-area.se .resize-handle {
    cursor: nwse-resize;
    bottom: 0;
    right: 0;
}
.global-resize-handle .handle-area.se .move-control {
    bottom: 2.5em;
    right: 2.5em;
}
.global-resize-handle .handle-area.sw {
    transform-origin: 0 100%;
    bottom: 0;
    left: 0;
}
.global-resize-handle .handle-area.sw .resize-handle {
    cursor: nesw-resize;
    bottom: 0;
    left: 0;
    transform: rotate(90deg);
}
.global-resize-handle .handle-area.sw .move-control {
    bottom: 2.5em;
    left: 2.5em;
}
.global-resize-handle .move-control {
    z-index: 100;
    color: #fff;
    cursor: move;
    pointer-events: all;
    background: #64646480;
    position: absolute;
}
.global-resize-handle .resize-handle {
    aspect-ratio: 1;
    pointer-events: all;
    border-bottom: 0.5em solid #fff;
    border-right: 0.5em solid #fff;
    border-bottom-right-radius: 2em;
    width: 2em;
    position: absolute;
}
.global-resize-handle .resize-handle:after,
.global-resize-handle .resize-handle:before {
    content: '';
    background: #fff;
    border-radius: 10px;
    width: 0.5em;
    height: 0.5em;
    position: absolute;
}
.global-resize-handle .resize-handle:before {
    top: -0.25em;
    right: -0.5em;
}
.global-resize-handle .resize-handle:after {
    bottom: -0.5em;
    left: -0.25em;
}
.screenshot .item {
    width: 100%;
    max-width: 100%;
    height: 100%;
    max-height: 100%;
}
.screenshot .item .item-container .shadow {
    border-radius: inherit;
}
.screenshot .item .interface {
    position: relative;
}
.screenshot .item .interface .img-use {
    width: 100%;
    height: 100%;
}
.screenshot .item .interface .screenshot-retro {
    background: #000;
    margin: -0.6em;
    position: absolute;
    inset: 0;
}
.screenshot .item .interface .screenshot-retro div {
    z-index: -1;
    background: #000;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 1.2em;
    left: 1.2em;
}
.screenshot .item .interface .screenshot-border {
    background: #000;
    margin: -0.8em;
    position: absolute;
    inset: 0;
}
.screenshot .item .interface .screenshot-outline {
    border: 0.3em solid #64646466;
    margin: -1em;
    position: absolute;
    inset: 0;
}
.screenshot .item .interface .screenshot-glass {
    margin: -1em;
    position: absolute;
    inset: 0;
    overflow: hidden;
    transform: translate(0);
}
.screenshot .item .interface .screenshot-glass.glass-light {
    -webkit-backdrop-filter: blur(2em) saturate(200%) contrast(150%) brightness(105%);
    backdrop-filter: blur(2em) saturate(200%) contrast(150%) brightness(105%);
    will-change: backdrop-filter;
    background: #fff9;
    transform: translateZ(0);
}
.screenshot .item .interface .screenshot-glass.glass-dark {
    -webkit-backdrop-filter: blur(2em) saturate(200%) contrast(150%) brightness(95%);
    backdrop-filter: blur(2em) saturate(200%) contrast(150%) brightness(95%);
    will-change: backdrop-filter;
    background: #0009;
    transform: translateZ(0);
}
.screenshot .item .interface .screenshot-glass.remove-effect {
    -webkit-backdrop-filter: unset !important;
    backdrop-filter: unset !important;
}
.screenshot .item .interface .screenshot-stack {
    position: absolute;
    inset: 0;
    box-shadow: 0 0.5em 0.5em #0003;
}
.screenshot .item .interface .screenshot-stack > div {
    border-radius: inherit;
    background: #fff;
    height: 100%;
    position: absolute;
    overflow: hidden;
}
.screenshot .item .interface .screenshot-stack > div .img-use {
    filter: blur(5em);
}
.screenshot .item .interface .screenshot-stack > div:first-child {
    z-index: -1;
    box-shadow: inherit;
    inset: 2em;
}
.screenshot .item .interface .screenshot-stack > div:nth-child(2) {
    z-index: -2;
    inset: 4em;
}
.screenshot .item .interface .screenshot-inward ~ .drop-wrapper:after,
.screenshot .item .interface .screenshot-inward > div:after {
    content: '';
    z-index: 1;
    border-radius: inherit;
    inset: 0;
    border: 0.3em solid #ffffff80;
    position: absolute;
    inset: 0;
}
.screenshot .item .interface .screenshot-inward-2 ~ .drop-wrapper,
.screenshot .item .interface .screenshot-inward-2 > div {
    z-index: 1;
    isolation: isolate;
    position: relative;
}
.screenshot .item .interface .screenshot-inward-2 ~ .drop-wrapper:before,
.screenshot .item .interface .screenshot-inward-2 > div:before {
    content: '';
    border-radius: inherit;
    filter: saturate(1.2) contrast(1.2) brightness(1.3);
    background: #ffffffe6;
    position: absolute;
    inset: 0;
}
.screenshot .item .interface .screenshot-inward-2 ~ .drop-wrapper:after,
.screenshot .item .interface .screenshot-inward-2 > div:after {
    content: '';
    border-radius: inherit;
    clip-path: inset(0 0 0 0 round inherit);
    background: 0 0;
    position: absolute;
    inset: 0.2em;
}
.screenshot .item .interface .screenshot-stack-2 {
    position: absolute;
    inset: 0;
    box-shadow: -1em 1em 4em #0003;
}
.screenshot .item .interface .screenshot-stack-2 ~ .drop-wrapper:after,
.screenshot .item .interface .screenshot-stack-2 > div:after {
    content: '';
    z-index: 1;
    border-radius: inherit;
    border: 0.1em solid #00000026;
    position: absolute;
    inset: 0;
}
.screenshot .item .interface .screenshot-stack-2 > div {
    border-radius: inherit;
    background: #fff;
    position: absolute;
    inset: 2em;
    overflow: hidden;
}
.screenshot .item .interface .screenshot-stack-2 > div:before {
    content: '';
    z-index: 1;
    position: absolute;
    inset: 0;
}
.screenshot .item .interface .screenshot-stack-2 > div .img-use {
    filter: blur(5em);
    transform: scale(1.5);
}
.screenshot .item .interface .screenshot-stack-2 > div:first-child {
    z-index: -1;
    box-shadow: inherit;
    transform: translate(-3.5em, -4.5em) rotate(-1.5deg);
}
.screenshot .item .interface .screenshot-stack-2 > div:first-child:before {
    background: #ffffff80;
}
.screenshot .item .interface .screenshot-stack-2 > div:nth-child(2) {
    z-index: -2;
    transform: translate(-7em, -7.5em) rotate(-2.5deg) scale(0.96);
}
.screenshot .item .interface .screenshot-stack-2 > div:nth-child(2):before {
    background: #ffffff80;
}
.screenshot .item .interface .screenshot-card {
    z-index: -1;
    background: #fff;
    position: absolute;
    inset: 0;
    overflow: hidden;
    transform: translate(-3.5em, 3em) rotate(-1.5deg) scale(0.98);
}
.screenshot .item .interface .screenshot-card .img-use {
    filter: blur(5em);
    transform: scale(1.5);
}
.screenshot .item .interface .screenshot-card:before {
    content: '';
    z-index: 1;
    background: #ffffff80;
    position: absolute;
    inset: 0;
}
.screenshot .item .interface .screenshot-card ~ .drop-wrapper:after,
.screenshot .item .interface .screenshot-card.screenshot-card:after {
    content: '';
    z-index: 1;
    border-radius: inherit;
    border: 0.1em solid #00000026;
    position: absolute;
    inset: 0;
}
.screenshot .item .interface .screenshot-card ~ .drop-wrapper {
    box-shadow: 2em -1em 4em #0003;
}
.browser {
    --shadow-inset: 0;
    --shadow-radius: 0.7em;
    --drop-padding: 0;
    --drop-radius: unset;
}
.browser .item {
    --ui-height: 3.5em;
}
.browser .item.safari-light .item-container,
.browser .item.safari-dark .item-container {
    margin-top: calc(0px - var(--ui-height) / 2);
    height: calc(100% + var(--ui-height)) !important;
}
.browser .item.chrome-light .item-container,
.browser .item.chrome-dark .item-container {
    margin-top: calc(0px - (var(--ui-height) * 0.9 + var(--ui-height) * 0.82) / 2);
    height: calc(100% + var(--ui-height) * 0.9 + var(--ui-height) * 0.82) !important;
}
.browser .item.chrome-light .interface,
.browser .item.safari-light .interface {
    background: #eee;
}
.browser .item.chrome-dark .interface,
.browser .item.safari-dark .interface {
    background: #121214;
}
.browser .item .item-container {
    position: relative;
}
.browser .item .item-container .window-border {
    z-index: 1;
    outline-offset: -0.15em;
    border: 0.05em solid #00000073;
    outline: 0.1em solid #e1e1e14d;
    position: absolute;
    inset: 0;
}
.browser .item .item-container .interface {
    flex-direction: column;
    height: 100%;
    min-height: 100%;
    max-height: 100%;
    display: flex;
    position: relative;
    overflow: hidden;
}
.browser .item .item-container .interface.arc-interface {
    padding: calc(var(--ui-height) / 5);
}
.browser .item .item-container .interface.arc-interface .drop-wrapper {
    z-index: 1;
    overflow: hidden;
}
.browser .item .item-container .interface .browser-ui {
    flex-direction: column;
    width: 100%;
    display: flex;
    position: relative;
}
.browser .item .item-container .interface .safari-ui {
    height: var(--ui-height);
    justify-content: space-between;
    width: 100%;
    display: flex;
    overflow: hidden;
}
.browser .item .item-container .interface .safari-ui img {
    height: 100%;
}
.browser .item .item-container .interface .safari-ui .center-div {
    flex: 1;
    align-items: center;
    max-width: 40%;
    height: 100%;
    display: flex;
}
.browser .item .item-container .interface .safari-ui .center-div .address-bar {
    border-radius: 0.3em;
    flex: 1;
    justify-content: center;
    height: 55%;
    display: flex;
    position: relative;
}
.browser .item .item-container .interface .safari-ui .center-div .address-bar .url-div {
    align-items: center;
    display: flex;
}
.browser .item .item-container .interface .safari-ui .center-div .address-bar .url-div p {
    font-size: calc(var(--ui-height) / 4.5);
    letter-spacing: -0.02em;
    font-family: Inter, sans-serif !important;
    font-weight: 400 !important;
}
.browser .item .item-container .interface .safari-ui .center-div .address-bar .shield {
    position: absolute;
    left: 0;
}
.browser .item .item-container .interface .safari-ui .center-div .address-bar .lock {
    position: absolute;
    right: 0;
}
.browser .item .item-container .interface .safari-ui.safari-light {
    background: #fff;
}
.browser .item .item-container .interface .safari-ui.safari-light .center-div .address-bar {
    background: #0000000d;
}
.browser
    .item
    .item-container
    .interface
    .safari-ui.safari-light
    .center-div
    .address-bar
    .url-div
    p {
    color: #4c4c4c;
}
.browser .item .item-container .interface .safari-ui.safari-dark {
    background: #191c1f;
}
.browser .item .item-container .interface .safari-ui.safari-dark .center-div .address-bar {
    background: #0c0f12;
}
.browser
    .item
    .item-container
    .interface
    .safari-ui.safari-dark
    .center-div
    .address-bar
    .url-div
    p {
    color: #fff;
}
.browser .item .item-container .interface .chrome-ui {
    flex-direction: column;
    width: 100%;
    display: flex;
    overflow: hidden;
}
.browser .item .item-container .interface .chrome-ui img {
    height: 100%;
}
.browser .item .item-container .interface .chrome-ui .top {
    height: calc(var(--ui-height) * 0.9);
    display: flex;
    position: relative;
}
.browser .item .item-container .interface .chrome-ui .top .logo-title {
    left: calc(var(--ui-height) * 0.9 * 2.1);
    top: calc(var(--ui-height) * 0.9 * 0.4);
    align-items: center;
    gap: calc(var(--ui-height) * 0.9 * 0.2);
    display: flex;
    position: absolute;
}
.browser .item .item-container .interface .chrome-ui .top .logo-title .logo {
    width: calc(var(--ui-height) * 0.9 / 2.5);
    aspect-ratio: 1;
    border-radius: 50%;
    overflow: hidden;
}
.browser .item .item-container .interface .chrome-ui .top .logo-title .logo img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.browser .item .item-container .interface .chrome-ui .top .logo-title p {
    font-size: calc(var(--ui-height) * 0.9 / 3.6);
    letter-spacing: -0.02em;
    text-transform: capitalize;
    line-height: 100%;
    font-family: Inter, sans-serif !important;
    font-weight: 400 !important;
}
.browser .item .item-container .interface .chrome-ui .bottom {
    height: calc(var(--ui-height) * 0.82);
    display: flex;
}
.browser .item .item-container .interface .chrome-ui .bottom .address-bar {
    border-radius: 10em;
    flex: 1;
    align-items: center;
    height: 80%;
    margin: auto 0;
    display: flex;
}
.browser .item .item-container .interface .chrome-ui .bottom .address-bar p {
    font-size: calc(var(--ui-height) * 0.9 / 3.2);
    letter-spacing: -0.02em;
    line-height: 110%;
    font-family: Inter, sans-serif !important;
    font-weight: 400 !important;
}
.browser .item .item-container .interface .chrome-ui.chrome-light .top {
    background: #dfe1e5;
}
.browser .item .item-container .interface .chrome-ui.chrome-light .top p {
    color: #202124;
}
.browser .item .item-container .interface .chrome-ui.chrome-light .bottom {
    background: #fff;
}
.browser .item .item-container .interface .chrome-ui.chrome-light .bottom .address-bar {
    background: #f1f3f4;
}
.browser .item .item-container .interface .chrome-ui.chrome-light .bottom .address-bar p {
    color: #202124;
}
.browser .item .item-container .interface .chrome-ui.chrome-dark .top {
    background: #202124;
}
.browser .item .item-container .interface .chrome-ui.chrome-dark .top p {
    color: #fff;
}
.browser .item .item-container .interface .chrome-ui.chrome-dark .bottom {
    background: #35363a;
}
.browser .item .item-container .interface .chrome-ui.chrome-dark .bottom .address-bar {
    background: #202124;
}
.browser .item .item-container .interface .chrome-ui.chrome-dark .bottom .address-bar p {
    color: #fff;
}
.browser .item .item-container .interface .arc-ui {
    border-radius: inherit;
    background-blend-mode: overlay;
    position: absolute;
    inset: 0;
}
.browser .item .item-container .interface .arc-ui.arc-light {
    -webkit-backdrop-filter: blur(3.51em) saturate(105%) contrast(105%) brightness(95%);
    backdrop-filter: blur(3.51em) saturate(105%) contrast(105%) brightness(95%);
    will-change: backdrop-filter;
    background: #98979a59;
    transform: translateZ(0);
}
.browser .item .item-container .interface .arc-ui.arc-dark {
    -webkit-backdrop-filter: blur(1.5em) saturate(200%) contrast(550%) brightness(35%)
        hue-rotate(10deg);
    backdrop-filter: blur(1.5em) saturate(200%) contrast(550%) brightness(35%) hue-rotate(10deg);
    will-change: backdrop-filter;
    background: #3e3955e2;
    transform: translateZ(0);
}
.browser .item .item-container .interface .arc-ui.arc-dark .noise {
    opacity: 0.034;
    z-index: -1;
    mix-blend-mode: overlay;
    border-radius: inherit;
    // background: url(https://assets.shots.so/canvas/noise2.svg);
    width: 100%;
    height: 100%;
    position: absolute;
}
.browser .item .item-container .interface .arc-ui.remove-effect {
    -webkit-backdrop-filter: unset !important;
    backdrop-filter: unset !important;
}
.minimal-desktop {
    align-self: flex-start !important;
    height: 150% !important;
    min-height: 150% !important;
    max-height: 150% !important;
}
.minimal-desktop .item .item-container .drop-wrapper {
    border-radius: inherit;
    overflow: hidden;
}
.minimal-desktop .item .item-container .shadow {
    border-radius: inherit;
}
.imac-24 .shadow,
.imac-retina .shadow,
.imac-pro .shadow,
.studio-display .shadow,
.pro-display-xdr .shadow {
    display: none;
}
.imac-24.imac-24,
.imac-retina.imac-24,
.imac-pro.imac-24,
.studio-display.imac-24,
.pro-display-xdr.imac-24 {
    --aspect-ratio: 3240/2760;
    --drop-padding: 3.88% 4% 29.6%;
}
.imac-24.imac-retina,
.imac-retina.imac-retina,
.imac-pro.imac-retina,
.studio-display.imac-retina,
.pro-display-xdr.imac-retina {
    --aspect-ratio: 4096/3444;
    --drop-padding: 5.2% 4% 27%;
}
.imac-24.imac-pro,
.imac-retina.imac-pro,
.imac-pro.imac-pro,
.studio-display.imac-pro,
.pro-display-xdr.imac-pro {
    --aspect-ratio: 4096/3411;
    --drop-padding: 5.2% 4% 27%;
}
.imac-24.pro-display-xdr,
.imac-retina.pro-display-xdr,
.imac-pro.pro-display-xdr,
.studio-display.pro-display-xdr,
.pro-display-xdr.pro-display-xdr {
    --aspect-ratio: 4096/3090;
    --drop-padding: 1.8% 1.8% 19.4%;
}
.imac-24.studio-display,
.imac-retina.studio-display,
.imac-pro.studio-display,
.studio-display.studio-display,
.pro-display-xdr.studio-display {
    --aspect-ratio: 1338/1028;
    --drop-padding: 2% 2% 20.6%;
}
.imac-24.layout-1-1 .item:first-child,
.imac-retina.layout-1-1 .item:first-child,
.imac-pro.layout-1-1 .item:first-child,
.studio-display.layout-1-1 .item:first-child,
.pro-display-xdr.layout-1-1 .item:first-child {
    display: block;
}
.imac-24.layout-1-2 .item:first-child,
.imac-retina.layout-1-2 .item:first-child,
.imac-pro.layout-1-2 .item:first-child,
.studio-display.layout-1-2 .item:first-child,
.pro-display-xdr.layout-1-2 .item:first-child {
    display: block;
    transform: translate(-30%, 10%) scale(1.2);
}
.imac-24.layout-1-3 .item:first-child,
.imac-retina.layout-1-3 .item:first-child,
.imac-pro.layout-1-3 .item:first-child,
.studio-display.layout-1-3 .item:first-child,
.pro-display-xdr.layout-1-3 .item:first-child {
    display: block;
    transform: translate(30%, 10%) scale(1.2);
}
.imac-24.layout-1-4 .item:first-child,
.imac-retina.layout-1-4 .item:first-child,
.imac-pro.layout-1-4 .item:first-child,
.studio-display.layout-1-4 .item:first-child,
.pro-display-xdr.layout-1-4 .item:first-child {
    display: block;
    transform: translate(-34%, -8%) scale(1.8);
}
.imac-24.layout-1-5 .item:first-child,
.imac-retina.layout-1-5 .item:first-child,
.imac-pro.layout-1-5 .item:first-child,
.studio-display.layout-1-5 .item:first-child,
.pro-display-xdr.layout-1-5 .item:first-child {
    display: block;
    transform: translate(34%, -8%) scale(1.8);
}
.imac-24.layout-1-6 .item:first-child,
.imac-retina.layout-1-6 .item:first-child,
.imac-pro.layout-1-6 .item:first-child,
.studio-display.layout-1-6 .item:first-child,
.pro-display-xdr.layout-1-6 .item:first-child {
    display: block;
    transform: translateY(13%) scale(1.3);
}
.imac-24.layout-1-7 .item:first-child,
.imac-retina.layout-1-7 .item:first-child,
.imac-pro.layout-1-7 .item:first-child,
.studio-display.layout-1-7 .item:first-child,
.pro-display-xdr.layout-1-7 .item:first-child {
    display: block;
    transform: translateY(40%) scale(1.8);
}
.imac-24.layout-1-8 .item:first-child,
.imac-retina.layout-1-8 .item:first-child,
.imac-pro.layout-1-8 .item:first-child,
.studio-display.layout-1-8 .item:first-child,
.pro-display-xdr.layout-1-8 .item:first-child {
    display: block;
    transform: translate(34%, 40%) scale(1.8);
}
.imac-24.layout-1-9 .item:first-child,
.imac-retina.layout-1-9 .item:first-child,
.imac-pro.layout-1-9 .item:first-child,
.studio-display.layout-1-9 .item:first-child,
.pro-display-xdr.layout-1-9 .item:first-child {
    display: block;
    transform: translate(-34%, 40%) scale(1.8);
}
.surface-book .shadow,
.macbook-air-13 .shadow,
.macbook-air-m2 .shadow,
.macbook-pro-13 .shadow,
.macbook-pro-15 .shadow,
.macbook-pro-16 .shadow {
    display: none;
}
.surface-book.surface-book,
.macbook-air-13.surface-book,
.macbook-air-m2.surface-book,
.macbook-pro-13.surface-book,
.macbook-pro-15.surface-book,
.macbook-pro-16.surface-book {
    --aspect-ratio: 4096/2428;
    --drop-padding: 4.4% 13.6% 6.4% 13.8%;
}
.surface-book.macbook-air-13,
.macbook-air-13.macbook-air-13,
.macbook-air-m2.macbook-air-13,
.macbook-pro-13.macbook-air-13,
.macbook-pro-15.macbook-air-13,
.macbook-pro-16.macbook-air-13 {
    --aspect-ratio: 3460/2060;
    --drop-padding: 6.6% 12.9%;
}
.surface-book.macbook-air-m2,
.macbook-air-13.macbook-air-m2,
.macbook-air-m2.macbook-air-m2,
.macbook-pro-13.macbook-air-m2,
.macbook-pro-15.macbook-air-m2,
.macbook-pro-16.macbook-air-m2 {
    --aspect-ratio: 1602/969;
    --drop-padding: 1.8% 10.5% 7.2%;
}
.surface-book.macbook-pro-13,
.macbook-air-13.macbook-pro-13,
.macbook-air-m2.macbook-pro-13,
.macbook-pro-13.macbook-pro-13,
.macbook-pro-15.macbook-pro-13,
.macbook-pro-16.macbook-pro-13 {
    --aspect-ratio: 3460/2200;
    --drop-padding: 8.6% 12.9%;
}
.surface-book.macbook-pro-15,
.macbook-air-13.macbook-pro-15,
.macbook-air-m2.macbook-pro-15,
.macbook-pro-13.macbook-pro-15,
.macbook-pro-15.macbook-pro-15,
.macbook-pro-16.macbook-pro-15 {
    --aspect-ratio: 3880/2400;
    --drop-padding: 7.6% 12.8%;
}
.surface-book.macbook-pro-16,
.macbook-air-13.macbook-pro-16,
.macbook-air-m2.macbook-pro-16,
.macbook-pro-13.macbook-pro-16,
.macbook-pro-15.macbook-pro-16,
.macbook-pro-16.macbook-pro-16 {
    --aspect-ratio: 4096/2699;
    --drop-padding: 7.2% 10.2% 7.14%;
}
.surface-book.layout-1-1 .item:first-child,
.macbook-air-13.layout-1-1 .item:first-child,
.macbook-air-m2.layout-1-1 .item:first-child,
.macbook-pro-13.layout-1-1 .item:first-child,
.macbook-pro-15.layout-1-1 .item:first-child,
.macbook-pro-16.layout-1-1 .item:first-child {
    display: block;
}
.surface-book.layout-1-2 .item:first-child,
.macbook-air-13.layout-1-2 .item:first-child,
.macbook-air-m2.layout-1-2 .item:first-child,
.macbook-pro-13.layout-1-2 .item:first-child,
.macbook-pro-15.layout-1-2 .item:first-child,
.macbook-pro-16.layout-1-2 .item:first-child {
    display: block;
    transform: translate(-30%) scale(1.2);
}
.surface-book.layout-1-3 .item:first-child,
.macbook-air-13.layout-1-3 .item:first-child,
.macbook-air-m2.layout-1-3 .item:first-child,
.macbook-pro-13.layout-1-3 .item:first-child,
.macbook-pro-15.layout-1-3 .item:first-child,
.macbook-pro-16.layout-1-3 .item:first-child {
    display: block;
    transform: translate(30%) scale(1.2);
}
.surface-book.layout-1-4 .item:first-child,
.macbook-air-13.layout-1-4 .item:first-child,
.macbook-air-m2.layout-1-4 .item:first-child,
.macbook-pro-13.layout-1-4 .item:first-child,
.macbook-pro-15.layout-1-4 .item:first-child,
.macbook-pro-16.layout-1-4 .item:first-child {
    display: block;
    transform: translate(-32%, -24%) scale(1.5);
}
.surface-book.layout-1-5 .item:first-child,
.macbook-air-13.layout-1-5 .item:first-child,
.macbook-air-m2.layout-1-5 .item:first-child,
.macbook-pro-13.layout-1-5 .item:first-child,
.macbook-pro-15.layout-1-5 .item:first-child,
.macbook-pro-16.layout-1-5 .item:first-child {
    display: block;
    transform: translate(32%, -24%) scale(1.5);
}
.surface-book.layout-1-7 .item:first-child,
.macbook-air-13.layout-1-7 .item:first-child,
.macbook-air-m2.layout-1-7 .item:first-child,
.macbook-pro-13.layout-1-7 .item:first-child,
.macbook-pro-15.layout-1-7 .item:first-child,
.macbook-pro-16.layout-1-7 .item:first-child {
    display: block;
    transform: translateY(40%) scale(1.8);
}
.surface-book.layout-1-8 .item:first-child,
.macbook-air-13.layout-1-8 .item:first-child,
.macbook-air-m2.layout-1-8 .item:first-child,
.macbook-pro-13.layout-1-8 .item:first-child,
.macbook-pro-15.layout-1-8 .item:first-child,
.macbook-pro-16.layout-1-8 .item:first-child {
    display: block;
    transform: translate(32%, 32%) scale(1.6);
}
.surface-book.layout-1-9 .item:first-child,
.macbook-air-13.layout-1-9 .item:first-child,
.macbook-air-m2.layout-1-9 .item:first-child,
.macbook-pro-13.layout-1-9 .item:first-child,
.macbook-pro-15.layout-1-9 .item:first-child,
.macbook-pro-16.layout-1-9 .item:first-child {
    display: block;
    transform: translate(-32%, 32%) scale(1.6);
}
