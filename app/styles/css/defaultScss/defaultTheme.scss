/*! tailwindcss v4.0.9 | MIT License | https://tailwindcss.com */
@import 'https://fonts.googleapis.com/css2?family=Comforter+Brush&display=swap';
@import 'https://fonts.googleapis.com/css2?family=Delicious+Handrawn&display=swap';
// 浅色模式
:root {
    --primary: 0, 0, 0; // 主要文本颜色 【黑色】
    --secondary: 255, 255, 255; // 次要文本颜色 【白色】
    --panel: 238, 238, 238; // 面板颜色 【浅灰色】
    --panel-active: 255, 255, 255; // 激活面板颜色 【白色】
    --panel-dim: 224, 224, 224; // 暗色面板 【浅灰色】
    --background: 210, 210, 210; // 背景颜色 【浅灰色】
    --body: 210, 210, 210; // 主体背景色 【浅灰色】
    --modal: 255, 255, 255; // 模态框背景色 【白色】
    --sheet: 255, 255, 255; // 底部弹出层背景色 【白色】
    --success: 67, 210, 90; // 成功状态颜色 【绿色】
    --danger: 255, 68, 68; // 危险/错误状态颜色 【红色】
    --alert: 255, 183, 0; // 警告状态颜色 【黄色】
    --info: 10, 132, 255; // 信息状态颜色 【蓝色】
    --performance: 255, 159, 10; // 性能相关颜色 【橙色】
}
// 深色模式
html.dark:root {
    --primary: 255, 255, 255; // 主要文本颜色 【白色】
    --secondary: 0, 0, 0; // 次要文本颜色 【黑色】
    --panel: 28, 28, 30; // 面板颜色 【深灰色】
    --panel-active: 46, 46, 48; // 激活面板颜色 【中灰色】
    --panel-dim: 16, 16, 18; // 暗色面板 【更深灰色】
    --background: 13, 13, 13; // 背景颜色 【深灰色】
    --body: 0, 0, 0; // 主体背景色 【黑色】
    --modal: 16, 16, 16; // 模态框背景色 【深灰色】
    --sheet: 0, 0, 0; // 底部弹出层背景色 【黑色】
    --success: 67, 210, 90; // 成功状态颜色 【绿色】
    --danger: 255, 68, 68; // 危险/错误状态颜色 【红色】
    --alert: 255, 183, 0; // 警告状态颜色 【黄色】
    --info: 10, 132, 255; // 信息状态颜色 【蓝色】
    --performance: 255, 159, 10; // 性能相关颜色 【橙色】
}
