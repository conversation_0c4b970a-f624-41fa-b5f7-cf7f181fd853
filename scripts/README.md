# 🔧 项目脚本目录

本目录包含项目的各类脚本文件，按功能分类组织。

## 📁 目录结构

```
scripts/
├── README.md                   # 本文件
├── build/                     # 构建相关脚本
├── deploy/                    # 部署相关脚本
├── maintenance/               # 维护相关脚本
└── development/               # 开发相关脚本
    └── format-check.mjs       # 代码格式检查脚本
```

## 📋 脚本说明

### 🏗️ build/ - 构建脚本
- 项目构建相关的脚本将放置在此目录

### 🚀 deploy/ - 部署脚本
- 项目部署相关的脚本将放置在此目录

### 🔧 maintenance/ - 维护脚本
- 项目维护相关的脚本将放置在此目录

### 💻 development/ - 开发脚本
- **format-check.mjs**: 代码格式检查和验证脚本

## 📝 使用说明

1. **执行脚本**: 从项目根目录执行脚本
   ```bash
   # 例如执行格式检查
   node scripts/development/format-check.mjs
   ```

2. **添加脚本**: 新增脚本时，请放置到合适的分类目录中
3. **权限设置**: 确保脚本文件具有执行权限
   ```bash
   chmod +x scripts/category/script-name.sh
   ```

## ⚠️ 注意事项

- 所有脚本都应该从项目根目录执行
- 添加新脚本时请更新此README文档
- 确保脚本具有适当的错误处理和日志输出

---

**创建时间**: 2025-01-31  
**目的**: 优化项目脚本组织结构
