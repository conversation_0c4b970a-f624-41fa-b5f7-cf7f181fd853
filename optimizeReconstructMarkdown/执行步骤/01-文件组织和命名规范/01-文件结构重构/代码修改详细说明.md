# 📝 第一步：代码修改详细说明

## 🎯 概述

这个文档详细说明每个文件移动后需要修改的具体代码内容，确保所有引用和导入路径都能正确更新。

## 📋 文件移动和代码修改对照表

### 🏗️ 布局组件修改

#### 1. MobileMockup_Layout.tsx → mobileMockupLayout.tsx

**文件移动：**
```bash
mv app/MobileMockup_Layout.tsx app/components/layout/mobileMockupLayout.tsx
```

**代码修改内容：**
```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { useMockupStore } from './hooks/useMockupStore'
import './css/globals.scss'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { useMockupStore } from '@/shared/hooks/useMockupStore'
import '@/styles/globals/globals.scss'

// 组件导出保持不变
export default function MobileMockupLayout() {
  // 组件内容不变
}
```

#### 2. MobileMockup_Tabs.tsx → mobileMockupTabs.tsx

**文件移动：**
```bash
mv app/MobileMockup_Tabs.tsx app/components/layout/mobileMockupTabs.tsx
```

**代码修改内容：**
```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { ColorTypes } from './types/colorTypes'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { ColorTypes } from '@/shared/types/colorTypes'

export default function MobileMockupTabs() {
  // 组件内容不变
}
```

### 🔧 业务组件修改

#### 3. PcMockup_Media.tsx → pcMockupMediaSelector.tsx

**文件移动：**
```bash
mv app/PcMockup_Media.tsx app/components/business/pcMockupMediaSelector.tsx
```

**代码修改内容：**
```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { useImageStore } from './ImageMange/imageMangeIndex'
import PublicMediaPickerModal from './Public_MediaPickerModal'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { useImageStore } from '@/features/imageManagement/core/store'
import PublicMediaPickerModal from './modals/publicMediaPickerModal'

export default function PcMockupMedia() {
  // 组件内容不变
}
```

#### 4. PcRightSlider.tsx → pcRightSlider.tsx

**文件移动：**
```bash
mv app/PcRightSlider.tsx app/components/business/pcRightSlider.tsx
```

**代码修改内容：**
```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { useMockupStore } from './hooks/useMockupStore'
import PcFrameEffectsModalDefault from './PcFrame_Effects__ModalDefault'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { useMockupStore } from '@/shared/hooks/useMockupStore'
import PcFrameEffectsModalDefault from './modals/pcFrameEffectsModalDefault'

export default function PcRightSlider() {
  // 组件内容不变
}
```

### 🔲 模态框组件修改

#### 5. PcFrame_Effects__ModalDefault.tsx → pcFrameEffectsModalDefault.tsx

**文件移动：**
```bash
mv app/PcFrame_Effects__ModalDefault.tsx app/components/business/modals/pcFrameEffectsModalDefault.tsx
```

**代码修改内容：**
```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { ColorTypes } from './types/colorTypes'
import { Modal } from './components/Modal'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { ColorTypes } from '@/shared/types/colorTypes'
import { Modal } from '@/components/ui/Modal'

export default function PcFrameEffectsModalDefault() {
  // 组件内容不变
}
```

#### 6. Public_MediaPickerModal.tsx → publicMediaPickerModal.tsx

**文件移动：**
```bash
mv app/Public_MediaPickerModal.tsx app/components/business/modals/publicMediaPickerModal.tsx
```

**代码修改内容：**
```typescript
// ===== 修改前 =====
import { useAppState } from './hooks/useAppState'
import { useImageStore } from './ImageMange/imageMangeIndex'
import { imageExport } from './utils/imageExport'

// ===== 修改后 =====
import { useAppState } from '@/shared/hooks/useAppState'
import { useImageStore } from '@/features/imageManagement/core/store'
import { imageExport } from '@/shared/utils/imageExport'

export default function PublicMediaPickerModal() {
  // 组件内容不变
}
```

### 🎯 功能模块修改

#### 7. ImageMange/imageMangeIndex.tsx → imageManagement/core/store.ts

**文件移动：**
```bash
mv app/ImageMange/imageMangeIndex.tsx app/features/imageManagement/core/store.ts
```

**代码修改内容：**
```typescript
// ===== 修改前 =====
import { ColorTypes } from '../types/colorTypes'
import { imageExport } from '../utils/imageExport'

// ===== 修改后 =====
import { ColorTypes } from '@/shared/types/colorTypes'
import { imageExport } from '@/shared/utils/imageExport'

// Zustand store 内容保持不变
export const useImageStore = create<ImageStore>((set, get) => ({
  // store 内容不变
}))
```

## 📄 主要文件的完整修改

### app/page.tsx 主页面文件

**修改前的导入部分：**
```typescript
import MobileMockupLayout from './MobileMockup_Layout'
import MobileMockupTabs from './MobileMockup_Tabs'
import PcMockupMedia from './PcMockup_Media'
import PcMockupVisibility from './PcMockup_Visibility'
import PcRightSlider from './PcRightSlider'
import PublicMediaPickerModal from './Public_MediaPickerModal'
import PcFrameEffectsModalDefault from './PcFrame_Effects__ModalDefault'
import PcFrameEffectsModalPortrait from './PcFrame_Effects__ModalPortrait'
import PcFrameEffectsModalVef from './PcFrame_Effects__ModalVef'
import PublicMockupModal from './Public_MockupModal'
import PublicFrameSizeModal from './Public_FrameSizeModal'
import { useAppState } from './hooks/useAppState'
import { useImageStore } from './ImageMange/imageMangeIndex'
```

**修改后的导入部分：**
```typescript
import MobileMockupLayout from '@/components/layout/mobileMockupLayout'
import MobileMockupTabs from '@/components/layout/mobileMockupTabs'
import PcMockupMediaSelector from '@/components/business/pcMockupMediaSelector'
import PcMockupVisibilityControl from '@/components/business/pcMockupVisibilityControl'
import PcRightSlider from '@/components/business/pcRightSlider'
import PublicMediaPickerModal from '@/components/business/modals/publicMediaPickerModal'
import PcFrameEffectsModalDefault from '@/components/business/modals/pcFrameEffectsModalDefault'
import PcFrameEffectsModalPortrait from '@/components/business/modals/pcFrameEffectsModalPortrait'
import PcFrameEffectsModalVef from '@/components/business/modals/pcFrameEffectsModalVef'
import PublicMockupModal from '@/components/business/modals/publicMockupModal'
import PublicFrameSizeModal from '@/components/business/modals/publicFrameSizeModal'
import { useAppState } from '@/shared/hooks/useAppState'
import { useImageStore } from '@/features/imageManagement/core/store'
```

**组件使用部分保持不变：**
```typescript
export default function Home() {
  // 组件逻辑不变，只是导入路径更新了
  return (
    <>
      <main>
        {isMobile ? <MobileMockupLayout /> : <PcMockupMediaSelector />}
      </main>
      <div id='modal_root'>
        <PublicMediaPickerModal />
        <PcFrameEffectsModalDefault />
        {/* 其他模态框 */}
      </div>
    </>
  )
}
```

### app/layout.tsx 布局文件

**修改前：**
```typescript
import './css/globals.scss'
import { TopBar } from './components/TopBar'
```

**修改后：**
```typescript
import '@/styles/globals/globals.scss'
import { TopBar } from '@/components/layout/TopBar'
```

## 🔧 批量更新脚本

### scripts/批量更新导入路径.js

```javascript
const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 完整的路径映射表
const importMappings = {
  // 布局组件
  "from './MobileMockup_Layout'": "from '@/components/layout/mobileMockupLayout'",
  "from './MobileMockup_Tabs'": "from '@/components/layout/mobileMockupTabs'",
  "from './Public_MockupSmallLayout'": "from '@/components/layout/publicMockupSmallLayout'",
  "from './Public_PanelTabs'": "from '@/components/layout/publicPanelTabs'",
  
  // 业务组件
  "from './PcMockup_Media'": "from '@/components/business/pcMockupMediaSelector'",
  "from './PcMockup_Visibility'": "from '@/components/business/pcMockupVisibilityControl'",
  "from './PcRightSlider'": "from '@/components/business/pcRightSlider'",
  
  // 模态框组件
  "from './Public_MediaPickerModal'": "from '@/components/business/modals/publicMediaPickerModal'",
  "from './PcFrame_Effects__ModalDefault'": "from '@/components/business/modals/pcFrameEffectsModalDefault'",
  "from './PcFrame_Effects__ModalPortrait'": "from '@/components/business/modals/pcFrameEffectsModalPortrait'",
  "from './PcFrame_Effects__ModalVef'": "from '@/components/business/modals/pcFrameEffectsModalVef'",
  "from './Public_MockupModal'": "from '@/components/business/modals/publicMockupModal'",
  "from './Public_FrameSizeModal'": "from '@/components/business/modals/publicFrameSizeModal'",
  
  // 功能模块
  "from './ImageMange/imageMangeIndex'": "from '@/features/imageManagement/core/store'",
  "from './get_color/": "from '@/features/colorExtraction/'",
  
  // 共享资源
  "from './hooks/": "from '@/shared/hooks/'",
  "from './utils/": "from '@/shared/utils/'",
  "from './types/": "from '@/shared/types/'",
  "from './config/": "from '@/shared/config/'",
  "from '../hooks/": "from '@/shared/hooks/'",
  "from '../utils/": "from '@/shared/utils/'",
  "from '../types/": "from '@/shared/types/'",
  
  // 样式文件
  "from './css/globals.scss'": "from '@/styles/globals/globals.scss'",
  "'./css/": "'@/styles/components/",
}

function updateFileImports(filePath) {
  if (!fs.existsSync(filePath)) return
  
  let content = fs.readFileSync(filePath, 'utf8')
  let hasChanges = false
  
  Object.entries(importMappings).forEach(([oldImport, newImport]) => {
    if (content.includes(oldImport)) {
      content = content.replace(new RegExp(oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newImport)
      hasChanges = true
    }
  })
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content)
    console.log(`✅ 已更新导入路径: ${filePath}`)
  }
}

// 执行更新
console.log('🔄 开始更新导入路径...')

// 更新主要文件
updateFileImports('app/page.tsx')
updateFileImports('app/layout.tsx')

// 更新所有移动后的组件文件
const filesToUpdate = [
  'app/components/layout/mobileMockupLayout.tsx',
  'app/components/layout/mobileMockupTabs.tsx',
  'app/components/business/pcMockupMediaSelector.tsx',
  'app/components/business/pcRightSlider.tsx',
  'app/components/business/modals/publicMediaPickerModal.tsx',
  'app/components/business/modals/pcFrameEffectsModalDefault.tsx',
  'app/features/imageManagement/core/store.ts',
]

filesToUpdate.forEach(updateFileImports)

console.log('✅ 导入路径更新完成！')
```

## ✅ 验证检查清单

### 每个文件移动后的检查：
- [ ] 文件成功移动到新位置
- [ ] 文件内的导入路径已更新
- [ ] 引用该文件的其他文件导入路径已更新
- [ ] TypeScript 编译无错误
- [ ] 相关功能测试正常

### 全局检查：
- [ ] 运行 `npx tsc --noEmit` 无错误
- [ ] 运行 `npm run dev` 应用正常启动
- [ ] 所有页面和功能正常工作
- [ ] 运行 `npm run build` 构建成功

这个详细的代码修改说明确保了每个文件移动后都有明确的修改指导。
