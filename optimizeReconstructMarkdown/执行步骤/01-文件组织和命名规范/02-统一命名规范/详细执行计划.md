# 📋 第二步：统一命名规范 - 详细执行计划

## 🎯 总体目标

统一项目中的命名规范，提高代码可读性和维护性，确保所有组件、文件、变量都遵循一致的命名约定。

## 📊 当前问题分析

### 🔍 发现的具体命名问题

经过代码扫描，发现以下命名不规范的问题：

#### 1. 组件命名问题
- `PcMockup_Media` → 应改为 `PcMockupMedia`
- `Public_SliderComponet` → 应改为 `PublicSliderComponent`（还有拼写错误）
- `UniversalSlider` → 保持不变（已符合规范）

#### 2. 文件夹命名问题
- `Public_SliderComponet/` → 应改为 `PublicSliderComponent/`
- `get_color/` → 应改为 `getColor/`
- `test_color/` → 应改为 `testColor/`

#### 3. 文件命名问题
- `Public_SliderComponet.tsx` → 应改为 `PublicSliderComponent.tsx`
- `mobileFrameColorsBackground.tsx` → 保持不变（已符合规范）
- `publicFrameScene.tsx` → 保持不变（已符合规范）

## 🚀 执行阶段规划

### 阶段1：组件和文件夹重命名 (预计20分钟)

#### 🎯 目标
- 统一所有文件夹使用camelCase命名
- 修复拼写错误
- 确保组件名与文件名一致

#### 📋 具体重命名操作

1. **重命名文件夹**
   ```bash
   # 修复拼写错误并统一命名
   mv app/components/Public_SliderComponet app/components/PublicSliderComponent
   
   # 统一颜色相关文件夹命名
   mv app/features/colorExtraction/get_color app/features/colorExtraction/getColor
   mv app/features/colorExtraction/test_color app/features/colorExtraction/testColor
   ```

2. **重命名组件文件**
   ```bash
   # 重命名滑动条组件文件
   mv app/components/PublicSliderComponent/Public_SliderComponet.tsx app/components/PublicSliderComponent/PublicSliderComponent.tsx
   ```

#### ✅ 验收标准
- [ ] 所有文件夹使用camelCase命名
- [ ] 拼写错误已修复
- [ ] 文件名与组件名一致
- [ ] 无TypeScript编译错误

### 阶段2：更新导入路径 (预计15分钟)

#### 🎯 目标
- 更新所有受影响的导入语句
- 确保路径引用正确
- 验证应用正常运行

#### 📋 需要更新的文件

1. **查找所有引用**
   ```bash
   # 查找所有引用 Public_SliderComponet 的文件
   grep -r "Public_SliderComponet" app/ --include="*.tsx" --include="*.ts"
   
   # 查找所有引用 get_color 的文件
   grep -r "get_color" app/ --include="*.tsx" --include="*.ts"
   
   # 查找所有引用 test_color 的文件
   grep -r "test_color" app/ --include="*.tsx" --include="*.ts"
   ```

2. **批量替换导入路径**
   ```bash
   # 替换滑动条组件的导入路径
   find app/ -name "*.tsx" -o -name "*.ts" | xargs sed -i "s|Public_SliderComponet|PublicSliderComponent|g"
   
   # 替换颜色相关的导入路径
   find app/ -name "*.tsx" -o -name "*.ts" | xargs sed -i "s|get_color|getColor|g"
   find app/ -name "*.tsx" -o -name "*.ts" | xargs sed -i "s|test_color|testColor|g"
   ```

#### ✅ 验收标准
- [ ] 所有导入路径已更新
- [ ] 无模块找不到的错误
- [ ] TypeScript编译通过
- [ ] 应用正常启动

### 阶段3：组件内部命名统一 (预计25分钟)

#### 🎯 目标
- 统一组件内部的变量和函数命名
- 确保事件处理函数使用handle前缀
- 统一布尔值函数使用is/has前缀

#### 📋 具体修改内容

1. **pcMockupMediaSelector.tsx 组件**
   - 组件名：`PcMockup_Media` → `PcMockupMedia`
   - 接口名：`PcMockup_MediaProps` → `PcMockupMediaProps`

2. **其他组件的命名检查**
   - 检查所有事件处理函数是否使用handle前缀
   - 检查所有布尔值函数是否使用is/has前缀
   - 统一变量命名为camelCase

#### ✅ 验收标准
- [ ] 组件名使用PascalCase
- [ ] 事件处理函数使用handle前缀
- [ ] 布尔值函数使用is/has前缀
- [ ] 变量名使用camelCase

### 阶段4：验证和测试 (预计10分钟)

#### 🎯 目标
- 确保所有修改不影响功能
- 验证应用正常运行
- 检查编译状态

#### 📋 验证步骤

1. **编译检查**
   ```bash
   # TypeScript编译检查
   npx tsc --noEmit
   ```

2. **启动测试**
   ```bash
   # 启动开发服务器
   pnpm dev
   ```

3. **功能验证**
   - 检查滑动条组件是否正常工作
   - 检查颜色提取功能是否正常
   - 检查媒体选择器是否正常

#### ✅ 验收标准
- [ ] TypeScript编译无错误
- [ ] 开发服务器正常启动
- [ ] 关键功能正常工作
- [ ] 页面可以正常访问

## 🔧 风险控制

### 备份策略
1. **Git分支保护**
   ```bash
   # 创建备份分支
   git checkout -b backup-before-naming-refactor
   git push origin backup-before-naming-refactor
   
   # 切换到工作分支
   git checkout -b feature/naming-refactor
   ```

2. **分阶段提交**
   ```bash
   # 每个阶段完成后提交
   git add .
   git commit -m "阶段1: 重命名文件夹和组件文件"
   
   git add .
   git commit -m "阶段2: 更新导入路径"
   
   git add .
   git commit -m "阶段3: 统一组件内部命名"
   ```

### 回滚方案
如果出现问题，可以快速回滚：
```bash
# 回滚到上一个提交
git reset --hard HEAD~1

# 或回滚到备份分支
git checkout backup-before-naming-refactor
```

## 📝 注意事项

1. **谨慎操作**：每次重命名前确保理解影响范围
2. **逐步验证**：每个阶段完成后立即测试
3. **保持备份**：随时准备回滚到稳定状态
4. **路径检查**：特别注意相对路径和绝对路径的更新

## 🎯 预期成果

### 直接成果
- 统一的文件和组件命名规范
- 修复的拼写错误
- 更清晰的代码结构
- 提高的代码可读性

### 长期收益
- 降低新人理解成本
- 提高团队协作效率
- 为后续重构奠定基础
- 提升代码质量标准

---

**创建时间**: 2025-01-31  
**状态**: 准备执行  
**预计总耗时**: 70分钟  
**版本**: v1.0
