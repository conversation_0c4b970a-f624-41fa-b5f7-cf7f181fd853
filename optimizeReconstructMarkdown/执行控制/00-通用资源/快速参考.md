# 🚀 快速参考卡片

## ⚠️ 重要提醒

### 🚨 绝对不能做的事情
```bash
# ❌ 错误：修改代码后直接提交
git add .
git commit -m "修改了一些文件"  # 没有测试！

# ❌ 错误：忽略编译错误
# TypeScript有错误但还是提交

# ❌ 错误：忽略运行时错误  
# 项目无法启动但还是提交
```

### ✅ 正确的执行顺序
```bash
# 1. 修改代码
# ... 进行文件移动、修改导入路径等操作 ...

# 2. 测试项目
pnpm dev  # 启动开发服务器
npx tsc --noEmit  # 检查TypeScript编译

# 3. 验证功能
# 打开 http://localhost:3000
# 测试关键功能是否正常

# 4. 确认无误后提交
git add .
git commit -m "描述性的提交信息"

# 5. 更新文档状态
```

## 📋 每次提交前的检查清单

- [ ] 代码修改已完成
- [ ] `pnpm dev` 启动成功
- [ ] `npx tsc --noEmit` 无错误
- [ ] 页面可以正常访问
- [ ] 关键功能正常工作
- [ ] 无JavaScript运行时错误
- [ ] 提交信息描述清晰

## 🔧 常用命令

### 项目测试
```bash
# 启动开发服务器
pnpm dev

# 检查TypeScript
npx tsc --noEmit

# 检查ESLint（如果有）
npm run lint

# 构建检查
npm run build
```

### Git操作
```bash
# 查看状态
git status

# 查看修改
git diff

# 添加文件
git add .

# 提交修改
git commit -m "类型: 简短描述"

# 查看提交历史
git log --oneline
```

### 问题排查
```bash
# 查看错误日志
# 在浏览器开发者工具中查看Console

# 查看网络请求
# 在浏览器开发者工具中查看Network

# 查看文件结构
find app/ -type f -name "*.tsx" | head -20
```

## 🎯 提交信息规范

### 格式
```
类型: 简短描述

详细说明（可选）
```

### 类型说明
- `feat`: 新功能
- `fix`: 修复bug
- `refactor`: 重构代码
- `move`: 移动文件
- `update`: 更新配置或文档
- `test`: 添加或修改测试

### 示例
```bash
# 好的提交信息
git commit -m "refactor: 移动布局组件到components/layout目录

- 移动MobileMockup_Layout.tsx到mobileMockupLayout.tsx
- 更新相关导入路径
- 验证功能正常"

# 简短版本
git commit -m "move: 移动布局组件到components/layout目录"
```

## 🚨 紧急情况处理

### 如果测试失败
```bash
# 1. 不要提交！
# 2. 查看错误信息
# 3. 修复问题
# 4. 重新测试
# 5. 确认无误后再提交
```

### 如果需要回滚
```bash
# 回滚到上一个提交
git reset --hard HEAD~1

# 回滚到特定提交
git reset --hard <commit-hash>

# 回滚到标签
git reset --hard v重构前备份
```

### 如果项目无法启动
```bash
# 1. 检查错误信息
pnpm dev

# 2. 检查TypeScript错误
npx tsc --noEmit

# 3. 检查依赖
npm install

# 4. 清理缓存
rm -rf .next
rm -rf node_modules/.cache
```

## 💡 最佳实践提醒

1. **小步快跑**：每次只修改少量文件
2. **频繁测试**：每个小修改都要测试
3. **及时提交**：测试通过后立即提交
4. **描述清晰**：提交信息要说明做了什么
5. **保持整洁**：不要积累问题

## 📞 寻求帮助

如果遇到问题：
1. 先查看错误信息和日志
2. 检查相关文档
3. 搜索类似问题的解决方案
4. 寻求团队成员帮助

记住：**宁可慢一点，也要确保每一步都是正确的！**
