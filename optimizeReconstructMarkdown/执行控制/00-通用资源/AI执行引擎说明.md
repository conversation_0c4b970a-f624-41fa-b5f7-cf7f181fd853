# 🤖 AI执行引擎说明

## 🎯 概述

这个文档说明AI如何解析`执行控制器.md`文件中的指令，并自动执行相应的重构操作。

## 🚫 重要排除项

**AI执行时必须排除以下内容：**
1. **CSS样式优化** - 不处理任何CSS相关的重构
2. **Mobile/PC代码重复** - 不合并移动端和PC端的重复代码
3. **样式文件移动** - 保持现有样式文件组织结构

## 🔍 指令解析逻辑

### 指令格式识别
AI会扫描`执行控制器.md`文件中的以下指令格式：

```bash
# 执行指令格式
EXECUTE: <步骤名称>           # 开始执行指定步骤
CONTINUE: <步骤名称-阶段>     # 继续执行指定阶段
ROLLBACK: <回滚类型>         # 执行回滚操作
COMPLETED: <步骤名称>        # 标记步骤完成
CHECK: <检查类型>            # 执行状态检查
```

### 指令优先级
1. **ROLLBACK** - 最高优先级，立即执行回滚
2. **CHECK** - 状态检查，用于诊断问题
3. **CONTINUE** - 继续中断的执行
4. **EXECUTE** - 开始新的执行
5. **COMPLETED** - 标记完成状态

## 🚀 执行流程

### 1. EXECUTE指令处理

当检测到`EXECUTE: 第一步文件结构重构`时：

```typescript
// AI执行逻辑伪代码
async function executeStep(stepName: string) {
    // 1. 验证前置条件
    await checkPrerequisites()
    
    // 2. 检查排除项
    await validateExclusions()
    
    // 3. 创建Git备份
    await createGitBackup(stepName)
    
    // 4. 加载执行计划（排除CSS和Mobile/PC重复）
    const plan = await loadExecutionPlan(stepName, {
        excludeCSS: true,
        excludeMobilePCDuplication: true
    })
    
    // 5. 分阶段执行
    for (const stage of plan.stages) {
        await executeStage(stage)
        await validateStage(stage)
        await createCheckpoint(stage)
    }
    
    // 6. 更新执行记录
    await updateExecutionLog(stepName, 'COMPLETED')
}
```

### 2. 排除项验证

```typescript
async function validateExclusions() {
    const excludedPatterns = [
        /css/i,                    // CSS相关文件
        /style/i,                  // 样式相关文件
        /mobile.*pc/i,             // Mobile/PC重复代码
        /pc.*mobile/i,             // PC/Mobile重复代码
    ]
    
    // 确保不会处理排除的内容
    console.log('✅ 已排除CSS优化和Mobile/PC代码重复处理')
}
```

## 📋 调整后的执行映射

### 第一步文件结构重构执行映射（已调整）

```typescript
const 第一步执行计划 = {
    name: '第一步文件结构重构',
    documentPath: '执行步骤/第一步文件结构重构/',
    excludePatterns: ['css/', 'styles/', '*Mobile*', '*Pc*'],
    stages: [
        {
            name: '阶段1-根目录清理',
            actions: [
                'createDirectories',
                'moveDocumentFiles',
                'moveScriptFiles',
                'updatePackageJson'
                // 排除：不移动CSS文件
            ],
            validation: ['checkTypeScript', 'checkFileStructure']
        },
        {
            name: '阶段2-共享资源移动',
            actions: [
                'moveUtilFiles',
                'moveTypeFiles',
                'moveHookFiles',
                'createIndexFiles'
                // 排除：不移动样式相关文件
            ],
            validation: ['checkTypeScript', 'checkImports']
        },
        {
            name: '阶段3-布局组件移动',
            actions: [
                'moveLayoutComponents',
                'updateLayoutImports'
                // 排除：不处理Mobile/PC重复的布局组件
            ],
            validation: ['checkTypeScript', 'checkComponentImports']
        },
        {
            name: '阶段4-业务组件移动',
            actions: [
                'moveBusinessComponents',
                'updateBusinessImports'
                // 排除：保持Mobile和PC组件分离
            ],
            validation: ['checkTypeScript', 'checkBusinessLogic']
        },
        {
            name: '阶段5-模态框组件移动',
            actions: [
                'moveModalComponents',
                'updateModalImports'
            ],
            validation: ['checkTypeScript', 'checkModalFunctionality']
        },
        {
            name: '阶段6-功能模块移动',
            actions: [
                'moveFunctionalModules',
                'updateModuleImports'
                // 排除：不合并Mobile/PC功能模块
            ],
            validation: ['checkTypeScript', 'checkModuleFunctionality']
        },
        {
            name: '阶段7-主要文件更新',
            actions: [
                'updateMainFiles',
                'updateImportPaths',
                'validateAllImports'
            ],
            validation: ['checkTypeScript', 'checkAppStart', 'checkBuild']
        }
    ]
}
```

### 文件操作映射（已调整）

```typescript
const FileOperations = {
    moveFile: async (from: string, to: string) => {
        // 检查是否为排除的文件类型
        if (isExcludedFile(from)) {
            console.log(`⏭️ 跳过排除的文件: ${from}`)
            return
        }
        
        await ensureDirectoryExists(path.dirname(to))
        await moveFile(from, to)
        console.log(`✅ 移动文件: ${from} → ${to}`)
    },
    
    isExcludedFile: (filePath: string) => {
        const excludePatterns = [
            /\.css$/i,
            /\.scss$/i,
            /\.less$/i,
            /style/i,
            /Mobile.*Pc/i,
            /Pc.*Mobile/i
        ]
        
        return excludePatterns.some(pattern => pattern.test(filePath))
    },
    
    updateImports: async (filePath: string, importMappings: Record<string, string>) => {
        let content = await readFile(filePath)
        
        // 过滤掉CSS和样式相关的导入更新
        const filteredMappings = Object.fromEntries(
            Object.entries(importMappings).filter(([oldImport, newImport]) => {
                return !oldImport.includes('css') && 
                       !oldImport.includes('style') &&
                       !oldImport.includes('scss')
            })
        )
        
        Object.entries(filteredMappings).forEach(([oldImport, newImport]) => {
            content = content.replace(
                new RegExp(oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
                newImport
            )
        })
        
        await writeFile(filePath, content)
        console.log(`✅ 更新导入路径: ${filePath}`)
    }
}
```

## 📊 调整后的状态跟踪

### 执行状态记录（已调整）

```typescript
interface ExecutionState {
    currentStep: string
    currentStage: string
    completedStages: string[]
    excludedItems: string[]        // 新增：记录排除的项目
    lastCheckpoint: string
    gitCommits: string[]
    errors: string[]
    startTime: Date
    lastUpdateTime: Date
}

// 状态持久化
const saveExecutionState = async (state: ExecutionState) => {
    // 记录排除项
    state.excludedItems = [
        'CSS样式优化',
        'Mobile/PC代码重复处理',
        '样式文件重构'
    ]
    
    const stateFile = 'optimizeReconstructMarkdown/执行状态.json'
    await writeFile(stateFile, JSON.stringify(state, null, 2))
}
```

## 🔍 调整后的验证和检查

### 自动验证流程（已调整）

```typescript
const ValidationChecks = {
    checkTypeScript: async () => {
        const result = await execCommand('npx tsc --noEmit')
        return result.exitCode === 0
    },
    
    checkAppStart: async () => {
        try {
            await execCommand('timeout 30s npm run dev', { timeout: 35000 })
            return true
        } catch {
            return false
        }
    },
    
    checkFileStructure: async () => {
        const requiredDirs = [
            'app/components/ui',
            'app/components/layout',
            'app/components/business',
            'app/features',
            'app/shared'
            // 排除：不检查CSS相关目录
        ]
        
        return requiredDirs.every(dir => existsSync(dir))
    },
    
    checkExclusions: async () => {
        // 验证排除项没有被处理
        const cssFiles = await glob('app/**/*.{css,scss,less}')
        const mobileFiles = await glob('app/**/*Mobile*.tsx')
        const pcFiles = await glob('app/**/*Pc*.tsx')
        
        console.log(`✅ 保持CSS文件不变: ${cssFiles.length} 个文件`)
        console.log(`✅ 保持Mobile组件不变: ${mobileFiles.length} 个文件`)
        console.log(`✅ 保持PC组件不变: ${pcFiles.length} 个文件`)
        
        return true
    }
}
```

## 🚨 调整后的错误处理

### 执行失败处理（已调整）

```typescript
const handleExecutionError = async (error: Error, context: ExecutionContext) => {
    // 1. 记录错误
    console.error(`❌ 执行失败: ${error.message}`)
    
    // 2. 检查是否因为处理了排除项导致的错误
    if (error.message.includes('css') || error.message.includes('Mobile') || error.message.includes('Pc')) {
        console.warn('⚠️ 可能尝试处理了排除的内容，请检查执行逻辑')
    }
    
    // 3. 自动回滚到最近的检查点
    await rollbackToLastCheckpoint()
    
    // 4. 更新执行记录
    await updateExecutionLog('ERROR', error.message)
    
    // 5. 提供修复建议
    const suggestions = generateFixSuggestions(error, context)
    return { success: false, error: error.message, suggestions }
}
```

## 📝 调整后的使用流程

1. **用户编辑执行控制器**：取消注释相应的执行指令
2. **发送给AI**：将`执行控制器.md`文件发送给AI
3. **AI解析指令**：AI自动识别和解析指令，同时排除CSS和Mobile/PC重复处理
4. **执行操作**：AI按照调整后的逻辑执行操作
5. **更新状态**：AI自动更新执行记录和状态
6. **返回结果**：AI提供详细的执行报告，明确说明排除的内容

这个调整后的AI执行引擎确保了不会处理CSS优化和Mobile/PC代码重复问题，同时保持了自动化、安全和可控的重构执行过程。
