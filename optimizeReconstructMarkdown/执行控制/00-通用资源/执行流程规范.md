# 🔄 执行流程规范

## 🎯 目的

本文档规定了项目重构过程中的标准执行流程，确保每个步骤都经过充分测试验证后才进行提交，避免引入错误或破坏性变更。

## ⚠️ 核心原则

### 1. 测试优先原则
- **任何代码修改都必须先测试，后提交**
- 绝不允许未经测试的代码进入版本控制

### 2. 渐进式验证原则
- 每个小步骤都要验证
- 发现问题立即修复，不积累问题

### 3. 可回滚原则
- 每次提交都应该是一个可工作的状态
- 任何时候都可以安全回滚到上一个提交

## 📋 标准执行流程

### 步骤1: 📝 执行代码修改
- 移动文件
- 修改导入路径
- 更新配置文件
- 修改组件代码

### 步骤2: 🧪 运行项目测试

#### 2.1 启动开发服务器
```bash
# 使用项目的包管理器启动
pnpm dev
# 或
npm run dev
# 或
yarn dev
```

#### 2.2 检查编译状态
```bash
# 检查TypeScript编译
npx tsc --noEmit

# 检查ESLint（如果有）
npm run lint
```

#### 2.3 检查构建状态（可选）
```bash
# 检查生产构建
npm run build
```

### 步骤3: ✅ 确认项目正常

#### 3.1 编译验证
- [ ] TypeScript编译无错误
- [ ] 开发服务器成功启动
- [ ] 无明显的警告信息

#### 3.2 功能验证
- [ ] 页面可以正常访问 (`http://localhost:3000`)
- [ ] 关键功能正常工作
- [ ] 无JavaScript运行时错误
- [ ] 无网络请求错误

#### 3.3 特定功能验证（根据修改内容）
- [ ] 如果修改了状态管理：测试状态更新
- [ ] 如果修改了组件：测试组件渲染
- [ ] 如果修改了路由：测试页面跳转
- [ ] 如果修改了API：测试数据获取

### 步骤4: 📦 执行Git提交

#### 4.1 检查修改内容
```bash
# 查看修改的文件
git status

# 查看具体修改内容
git diff
```

#### 4.2 添加文件到暂存区
```bash
# 添加所有修改
git add .

# 或选择性添加
git add path/to/specific/file
```

#### 4.3 提交修改
```bash
# 使用描述性的提交信息
git commit -m "类型: 简短描述

详细说明修改内容和原因（如果需要）"
```

#### 4.4 提交信息规范
- **格式**: `类型: 简短描述`
- **类型**:
  - `feat`: 新功能
  - `fix`: 修复bug
  - `refactor`: 重构代码
  - `move`: 移动文件
  - `update`: 更新配置或文档
  - `test`: 添加或修改测试

### 步骤5: 📋 更新文档状态
- 更新执行控制器中的状态
- 记录完成的工作
- 标记下一步计划

## 🚨 严禁的操作

### ❌ 直接提交未测试的代码
```bash
# 错误示例 - 绝对不要这样做
git add .
git commit -m "修改了一些文件"  # 没有测试！
```

### ❌ 忽略编译错误
- 有TypeScript错误时强行提交
- 有ESLint错误时强行提交
- 开发服务器无法启动时强行提交

### ❌ 忽略运行时错误
- 页面无法访问时强行提交
- 有JavaScript错误时强行提交
- 关键功能异常时强行提交

### ❌ 批量提交未验证的修改
- 一次性修改大量文件后直接提交
- 跳过中间验证步骤

## ✅ 最佳实践

### 1. 小步快跑
- 每次只修改少量文件
- 频繁测试和提交
- 保持每个提交都是可工作的状态

### 2. 描述性提交信息
```bash
# 好的提交信息
git commit -m "refactor: 移动布局组件到components/layout目录

- 移动MobileMockup_Layout.tsx到mobileMockupLayout.tsx
- 更新相关导入路径
- 验证功能正常"

# 不好的提交信息
git commit -m "修改文件"
git commit -m "更新"
git commit -m "fix"
```

### 3. 及时修复问题
- 发现问题立即停止，先修复再继续
- 不要积累问题到最后一起解决
- 保持工作区的整洁

### 4. 使用分支保护
- 在功能分支上工作
- 保持主分支的稳定性
- 完成后再合并到主分支

## 🔧 问题处理

### 如果测试失败怎么办？
1. **不要提交** - 立即停止git操作
2. **分析错误** - 查看错误信息和日志
3. **修复问题** - 针对性地解决问题
4. **重新测试** - 确保修复有效
5. **再次验证** - 完整走一遍验证流程

### 如果功能异常怎么办？
1. **回滚修改** - 使用git恢复到上一个工作状态
2. **重新分析** - 理解问题的根本原因
3. **小步修改** - 分更小的步骤进行修改
4. **逐步验证** - 每一小步都要验证

### 如果不确定怎么办？
1. **先备份** - 创建当前状态的备份
2. **寻求帮助** - 咨询团队成员或查阅文档
3. **谨慎操作** - 宁可慢一点也要确保正确

## 📊 检查清单

在每次提交前，请确认以下所有项目：

- [ ] 代码修改已完成
- [ ] 开发服务器可以正常启动
- [ ] TypeScript编译无错误
- [ ] 页面可以正常访问
- [ ] 关键功能正常工作
- [ ] 无JavaScript运行时错误
- [ ] Git状态检查完成
- [ ] 提交信息描述清晰
- [ ] 文档状态已更新

## 🎯 总结

遵循这个执行流程规范，可以确保：

1. **代码质量**：每次提交的代码都是经过测试的
2. **项目稳定性**：避免引入破坏性变更
3. **开发效率**：及时发现和解决问题
4. **团队协作**：清晰的提交历史便于协作
5. **风险控制**：任何时候都可以安全回滚

记住：**测试是开发流程中不可跳过的关键步骤！**
