# 🎮 执行控制器 - 组件职责不清晰

## 🎯 使用说明

**这是组件职责不清晰问题分类的主执行控制器。将此文件发送给AI，AI会根据当前状态自动执行相应的重构步骤。**

## 📋 当前执行状态

### 🔄 当前阶段：⏳ 组件职责不清晰处理

### 📍 当前步骤：等待前置步骤完成

### 🎯 下一步行动：等待状态管理复杂性处理完成

## 🗂️ 子步骤概览

### ⏳ 01-巨型组件拆分
- **状态**: 待开始
- **描述**: 拆分职责过多的大型组件，明确组件边界
- **文档**: `./01-巨型组件拆分/执行控制器.md`
- **预计时间**: 4-5天
- **风险等级**: 🔴 高

## 🔄 执行流程规范

### ⚠️ 重要：组件重构流程

组件职责重构涉及架构调整，必须严格按照以下顺序执行：

1. **🔍 组件分析**
   - 识别职责过多的组件
   - 分析组件依赖关系
   - 制定拆分策略

2. **🏗️ 设计组件架构**
   - 设计新的组件层次结构
   - 定义组件间接口
   - 规划数据流向

3. **✂️ 渐进式拆分**
   - 按功能模块逐步拆分
   - 保持组件功能完整性
   - 逐步优化组件接口

4. **🔗 重构组件关系**
   - 更新组件间依赖关系
   - 优化props传递
   - 简化组件通信

5. **🧪 功能测试**
   ```bash
   # 启动开发服务器
   pnpm dev
   
   # 检查编译状态
   npx tsc --noEmit
   
   # 测试拆分后的组件功能
   ```

6. **📦 分模块提交**
   ```bash
   git add .
   git commit -m "refactor: 拆分[组件名] - [具体功能模块]"
   ```

## 🚀 执行指令区域

**请在下面选择你要执行的操作（取消注释相应行）：**

```bash
# ===== 🚀 开始执行 =====
# 等待前置步骤完成后，取消下面一行的注释
# EXECUTE: 01-巨型组件拆分

# ===== 🔄 继续执行 =====
# 如果中途中断，取消下面相应行的注释来继续
# CONTINUE: 01-巨型组件拆分-组件分析阶段
# CONTINUE: 01-巨型组件拆分-架构设计阶段
# CONTINUE: 01-巨型组件拆分-渐进拆分阶段
# CONTINUE: 01-巨型组件拆分-关系重构阶段

# ===== 🚨 回滚操作 =====
# 组件重构风险较高，如有问题立即回滚
# ROLLBACK: 回滚到拆分前状态
# ROLLBACK: 回滚到上一个拆分阶段

# ===== ✅ 完成确认 =====
# 子步骤完成后，取消下面相应行的注释
# COMPLETED: 01-巨型组件拆分

# ===== 🔍 状态检查 =====
# 检查当前状态，取消下面一行的注释
# CHECK: 组件职责不清晰状态检查
```

## 📊 执行历史记录

### 执行日志
```
2025-08-01 - 组件职责不清晰阶段准备
⏸️ 等待前置步骤: 状态管理复杂性处理完成
⏳ 准备开始: 01-巨型组件拆分
```

### 当前组件现状
- **巨型组件**: 存在职责过多的大型组件
- **耦合度高**: 组件间依赖关系复杂
- **复用性差**: 组件粒度过大，难以复用
- **维护困难**: 单个组件代码量过大

## 🎯 重构目标

### 主要问题
1. **职责过多**: 单个组件承担过多职责
2. **粒度过大**: 组件粒度不合理
3. **耦合度高**: 组件间耦合度过高
4. **复用性差**: 组件难以在其他场景复用

### 解决方案
1. **职责分离**: 按单一职责原则拆分组件
2. **合理粒度**: 建立合理的组件粒度
3. **降低耦合**: 通过接口降低组件间耦合
4. **提升复用**: 设计可复用的组件接口

## 🎨 组件设计原则

### 单一职责原则
- 每个组件只负责一个明确的功能
- 避免组件承担过多职责
- 保持组件功能的内聚性

### 开放封闭原则
- 组件对扩展开放，对修改封闭
- 通过props和插槽支持扩展
- 避免直接修改组件内部逻辑

### 依赖倒置原则
- 高层组件不依赖低层组件
- 通过抽象接口定义依赖关系
- 支持依赖注入和控制反转

## 🔧 配置信息

### 项目路径
- **项目根目录**: `/Users/<USER>/Desktop/岸边/next_wallpaper/`
- **组件目录**: `./app/components/`
- **业务组件**: `./app/components/business/`
- **共享组件**: `./app/shared/components/`

### 组件架构
- **布局组件**: 负责页面布局结构
- **业务组件**: 负责具体业务功能
- **共享组件**: 负责通用功能组件

### 执行参数
- **拆分策略**: 按功能模块渐进式拆分
- **风险控制**: 高风险操作，需要充分测试
- **验证模式**: 每个拆分完成后立即验证
- **安全模式**: 启用组件功能检查点

## ⚠️ 风险提示

### 高风险操作
- **组件拆分**: 可能影响组件功能完整性
- **接口变更**: 可能影响父组件调用
- **状态管理**: 可能影响组件间状态传递

### 安全措施
- **功能验证**: 每次拆分后验证功能完整性
- **接口兼容**: 保持向后兼容的组件接口
- **渐进重构**: 逐步拆分，降低风险
- **回滚机制**: 保持快速回滚能力

## 🤖 AI执行逻辑

当你将此文件发送给AI时，AI会：

1. **检查前置条件**: 确认状态管理复杂性处理已完成
2. **解析执行指令**: 检查执行指令区域
3. **组件分析**: 分析当前组件职责分布
4. **执行相应操作**:
   - `EXECUTE`: 开始组件职责重构
   - `CONTINUE`: 继续中断的重构阶段
   - `ROLLBACK`: 执行组件回滚
   - `CHECK`: 检查组件职责现状
5. **功能验证**: 验证拆分后的组件功能
6. **更新记录**: 记录重构进度和组件变更

---

**💡 提示：组件职责重构是架构级调整，建议在状态管理优化完成后谨慎执行！**
