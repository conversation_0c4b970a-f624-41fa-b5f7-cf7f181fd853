# 📋 文档创建规范

## 🎯 重要规范

**AI在此目录下创建文件时必须遵守的规范：**

### 📁 文件组织规范

1. **禁止在根目录直接创建单个文件**
   - ❌ 错误：直接在 `optimizeReconstructMarkdown/` 下创建 `新文件.md`
   - ✅ 正确：在 `optimizeReconstructMarkdown/相关分类/` 下创建 `新文件.md`

2. **必须先创建分类文件夹**
   - 根据文件功能创建相应的分类文件夹
   - 文件夹使用中文命名
   - 然后在文件夹内创建具体文件

3. **文件夹分类建议**
   - `执行步骤/` - 具体的执行计划和步骤
   - `Git版本控制/` - Git相关操作指南
   - `文档规范/` - 文档创建和管理规范
   - `工具脚本/` - 自动化脚本和工具
   - `参考资料/` - 参考文档和资料
   - `临时文件/` - 临时性文件（定期清理）

### 🚫 优化排除项

**以下内容不需要优化，相关文档需要移除或标记为不处理：**

1. **CSS样式优化**
   - 不处理CSS重复问题
   - 不优化样式文件结构
   - 保持现有CSS组织方式

2. **Mobile与PC代码重复**
   - 不处理移动端和PC端的代码重复问题
   - 保持现有的平台差异化实现
   - 不合并相似的移动端和PC端组件

### 📝 命名规范

1. **文件夹命名**
   - 使用中文命名
   - 简洁明了，体现功能
   - 避免过长的名称

2. **文件命名**
   - 使用中文命名
   - 保留必要的英文术语（如Git、README等）
   - 文件名要能体现具体内容

### 🔄 现有文件整理

**需要移动到相应文件夹的文件：**
- `执行控制器.md` → `执行控制/执行控制器.md`
- `AI执行引擎说明.md` → `执行控制/AI执行引擎说明.md`
- `QUICK_START.md` → `使用指南/快速开始.md`
- `SUMMARY.md` → `项目分析/项目总结.md`
- `ADDITIONAL_FINDINGS.md` → `项目分析/补充发现.md`

## ✅ 遵守规范的好处

1. **文件组织清晰**：便于查找和管理
2. **避免根目录混乱**：保持根目录简洁
3. **功能分类明确**：相关文件集中管理
4. **便于维护**：后续添加文件有明确的归属

## 🚨 违规处理

如果AI违反了这些规范：
1. 立即指出违规行为
2. 要求重新按规范组织文件
3. 更新相关文档引用

---

**请AI严格遵守以上规范！**
